// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		8C6ACB932D961CC3001DDD97 /* UIKit.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = 8C6ACB902D957277001DDD97 /* UIKit.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		8CDBE4F02DE41C2800A15973 /* ZIPFoundation in Frameworks */ = {isa = PBXBuildFile; productRef = 8C6ACB8E2D9564BA001DDD97 /* ZIPFoundation */; };
		8CDBE4F12DE41C2800A15973 /* ZIPFoundation in Frameworks */ = {isa = PBXBuildFile; productRef = 8C0496672DDDDB4C00D78ED4 /* ZIPFoundation */; };
		8CDBE4F22DE41C2800A15973 /* ZIPFoundation in Frameworks */ = {isa = PBXBuildFile; productRef = 8C0496692DDDDB5200D78ED4 /* ZIPFoundation */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		8C6ACB3A2D954633001DDD97 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 8C6ACB1E2D954631001DDD97 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 8C6ACB252D954631001DDD97;
			remoteInfo = IOSReader;
		};
		8C6ACB442D954633001DDD97 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 8C6ACB1E2D954631001DDD97 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 8C6ACB252D954631001DDD97;
			remoteInfo = IOSReader;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		8C6ACB942D961CC3001DDD97 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 8;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				8C6ACB932D961CC3001DDD97 /* UIKit.framework in Embed Frameworks */,
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 1;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		8C6ACB262D954631001DDD97 /* IOSReader.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = IOSReader.app; sourceTree = BUILT_PRODUCTS_DIR; };
		8C6ACB392D954633001DDD97 /* IOSReaderTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = IOSReaderTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		8C6ACB432D954633001DDD97 /* IOSReaderUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = IOSReaderUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		8C6ACB902D957277001DDD97 /* UIKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = UIKit.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.2.sdk/System/Library/Frameworks/UIKit.framework; sourceTree = DEVELOPER_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		8C6ACB282D954631001DDD97 /* IOSReader */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = IOSReader;
			sourceTree = "<group>";
		};
		8C6ACB3C2D954633001DDD97 /* IOSReaderTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = IOSReaderTests;
			sourceTree = "<group>";
		};
		8C6ACB462D954633001DDD97 /* IOSReaderUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = IOSReaderUITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		8C6ACB232D954631001DDD97 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				8CDBE4F02DE41C2800A15973 /* ZIPFoundation in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8C6ACB362D954633001DDD97 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				8CDBE4F12DE41C2800A15973 /* ZIPFoundation in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8C6ACB402D954633001DDD97 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				8CDBE4F22DE41C2800A15973 /* ZIPFoundation in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		8C6ACB1D2D954631001DDD97 = {
			isa = PBXGroup;
			children = (
				8C6ACB282D954631001DDD97 /* IOSReader */,
				8C6ACB3C2D954633001DDD97 /* IOSReaderTests */,
				8C6ACB462D954633001DDD97 /* IOSReaderUITests */,
				8C6ACB8D2D9564BA001DDD97 /* Frameworks */,
				8C6ACB272D954631001DDD97 /* Products */,
			);
			sourceTree = "<group>";
		};
		8C6ACB272D954631001DDD97 /* Products */ = {
			isa = PBXGroup;
			children = (
				8C6ACB262D954631001DDD97 /* IOSReader.app */,
				8C6ACB392D954633001DDD97 /* IOSReaderTests.xctest */,
				8C6ACB432D954633001DDD97 /* IOSReaderUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		8C6ACB8D2D9564BA001DDD97 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				8C6ACB902D957277001DDD97 /* UIKit.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		8C6ACB252D954631001DDD97 /* IOSReader */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 8C6ACB4D2D954633001DDD97 /* Build configuration list for PBXNativeTarget "IOSReader" */;
			buildPhases = (
				8C6ACB222D954631001DDD97 /* Sources */,
				8C6ACB232D954631001DDD97 /* Frameworks */,
				8C6ACB242D954631001DDD97 /* Resources */,
				8C6ACB942D961CC3001DDD97 /* Embed Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				8C6ACB282D954631001DDD97 /* IOSReader */,
			);
			name = IOSReader;
			packageProductDependencies = (
				8C6ACB8E2D9564BA001DDD97 /* ZIPFoundation */,
			);
			productName = IOSReader;
			productReference = 8C6ACB262D954631001DDD97 /* IOSReader.app */;
			productType = "com.apple.product-type.application";
		};
		8C6ACB382D954633001DDD97 /* IOSReaderTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 8C6ACB502D954633001DDD97 /* Build configuration list for PBXNativeTarget "IOSReaderTests" */;
			buildPhases = (
				8C6ACB352D954633001DDD97 /* Sources */,
				8C6ACB362D954633001DDD97 /* Frameworks */,
				8C6ACB372D954633001DDD97 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				8C6ACB3B2D954633001DDD97 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				8C6ACB3C2D954633001DDD97 /* IOSReaderTests */,
			);
			name = IOSReaderTests;
			packageProductDependencies = (
				8C0496672DDDDB4C00D78ED4 /* ZIPFoundation */,
			);
			productName = IOSReaderTests;
			productReference = 8C6ACB392D954633001DDD97 /* IOSReaderTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		8C6ACB422D954633001DDD97 /* IOSReaderUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 8C6ACB532D954633001DDD97 /* Build configuration list for PBXNativeTarget "IOSReaderUITests" */;
			buildPhases = (
				8C6ACB3F2D954633001DDD97 /* Sources */,
				8C6ACB402D954633001DDD97 /* Frameworks */,
				8C6ACB412D954633001DDD97 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				8C6ACB452D954633001DDD97 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				8C6ACB462D954633001DDD97 /* IOSReaderUITests */,
			);
			name = IOSReaderUITests;
			packageProductDependencies = (
				8C0496692DDDDB5200D78ED4 /* ZIPFoundation */,
			);
			productName = IOSReaderUITests;
			productReference = 8C6ACB432D954633001DDD97 /* IOSReaderUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		8C6ACB1E2D954631001DDD97 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1620;
				LastUpgradeCheck = 1630;
				TargetAttributes = {
					8C6ACB252D954631001DDD97 = {
						CreatedOnToolsVersion = 16.2;
					};
					8C6ACB382D954633001DDD97 = {
						CreatedOnToolsVersion = 16.2;
						TestTargetID = 8C6ACB252D954631001DDD97;
					};
					8C6ACB422D954633001DDD97 = {
						CreatedOnToolsVersion = 16.2;
						TestTargetID = 8C6ACB252D954631001DDD97;
					};
				};
			};
			buildConfigurationList = 8C6ACB212D954631001DDD97 /* Build configuration list for PBXProject "IOSReader" */;
			developmentRegion = "zh-Hans";
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
				"zh-Hans",
			);
			mainGroup = 8C6ACB1D2D954631001DDD97;
			minimizedProjectReferenceProxies = 1;
			packageReferences = (
				8C6ACB8C2D95645D001DDD97 /* XCLocalSwiftPackageReference "../../../../Downloads/ZIPFoundation-0.9.19" */,
			);
			preferredProjectObjectVersion = 77;
			productRefGroup = 8C6ACB272D954631001DDD97 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				8C6ACB252D954631001DDD97 /* IOSReader */,
				8C6ACB382D954633001DDD97 /* IOSReaderTests */,
				8C6ACB422D954633001DDD97 /* IOSReaderUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		8C6ACB242D954631001DDD97 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8C6ACB372D954633001DDD97 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8C6ACB412D954633001DDD97 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		8C6ACB222D954631001DDD97 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8C6ACB352D954633001DDD97 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8C6ACB3F2D954633001DDD97 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		8C6ACB3B2D954633001DDD97 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 8C6ACB252D954631001DDD97 /* IOSReader */;
			targetProxy = 8C6ACB3A2D954633001DDD97 /* PBXContainerItemProxy */;
		};
		8C6ACB452D954633001DDD97 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 8C6ACB252D954631001DDD97 /* IOSReader */;
			targetProxy = 8C6ACB442D954633001DDD97 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		8C6ACB4B2D954633001DDD97 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = YES;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = YES;
				DEAD_CODE_STRIPPING = YES;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = L5ZCXC64Q2;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_ENABLE_BATCH_MODE = NO;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 6.0;
			};
			name = Debug;
		};
		8C6ACB4C2D954633001DDD97 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = YES;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = YES;
				DEAD_CODE_STRIPPING = YES;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = L5ZCXC64Q2;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_ENABLE_BATCH_MODE = NO;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 6.0;
			};
			name = Release;
		};
		8C6ACB4E2D954633001DDD97 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = NO;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CODE_SIGN_ENTITLEMENTS = IOSReader/IOSReader.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEAD_CODE_STRIPPING = YES;
				DEVELOPMENT_ASSET_PATHS = "\"IOSReader/Preview Content\"";
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphonesimulator*]" = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphoneos*]" = UIStatusBarStyleDefault;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphonesimulator*]" = UIStatusBarStyleDefault;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				LD_RUNPATH_SEARCH_PATHS = "@executable_path/Frameworks";
				"LD_RUNPATH_SEARCH_PATHS[sdk=macosx*]" = "@executable_path/../Frameworks";
				MACOSX_DEPLOYMENT_TARGET = 14.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = none.IOSReader;
				PRODUCT_NAME = "$(TARGET_NAME)";
				REGISTER_APP_GROUPS = NO;
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_ENABLE_BATCH_MODE = NO;
				SWIFT_VERSION = 6.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				XROS_DEPLOYMENT_TARGET = 2.2;
			};
			name = Debug;
		};
		8C6ACB4F2D954633001DDD97 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = NO;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CODE_SIGN_ENTITLEMENTS = IOSReader/IOSReader.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEAD_CODE_STRIPPING = YES;
				DEVELOPMENT_ASSET_PATHS = "\"IOSReader/Preview Content\"";
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphonesimulator*]" = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphoneos*]" = UIStatusBarStyleDefault;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphonesimulator*]" = UIStatusBarStyleDefault;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				LD_RUNPATH_SEARCH_PATHS = "@executable_path/Frameworks";
				"LD_RUNPATH_SEARCH_PATHS[sdk=macosx*]" = "@executable_path/../Frameworks";
				MACOSX_DEPLOYMENT_TARGET = 14.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = none.IOSReader;
				PRODUCT_NAME = "$(TARGET_NAME)";
				REGISTER_APP_GROUPS = NO;
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_ENABLE_BATCH_MODE = NO;
				SWIFT_VERSION = 6.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				XROS_DEPLOYMENT_TARGET = 2.2;
			};
			name = Release;
		};
		8C6ACB512D954633001DDD97 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEAD_CODE_STRIPPING = YES;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				MACOSX_DEPLOYMENT_TARGET = 15.2;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = none.IOSReaderTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				REGISTER_APP_GROUPS = NO;
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/IOSReader.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/IOSReader";
				XROS_DEPLOYMENT_TARGET = 2.2;
			};
			name = Debug;
		};
		8C6ACB522D954633001DDD97 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEAD_CODE_STRIPPING = YES;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				MACOSX_DEPLOYMENT_TARGET = 15.2;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = none.IOSReaderTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				REGISTER_APP_GROUPS = NO;
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/IOSReader.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/IOSReader";
				XROS_DEPLOYMENT_TARGET = 2.2;
			};
			name = Release;
		};
		8C6ACB542D954633001DDD97 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEAD_CODE_STRIPPING = YES;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				MACOSX_DEPLOYMENT_TARGET = 15.2;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = none.IOSReaderUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				REGISTER_APP_GROUPS = NO;
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx";
				SUPPORTS_MACCATALYST = NO;
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = IOSReader;
				XROS_DEPLOYMENT_TARGET = 2.2;
			};
			name = Debug;
		};
		8C6ACB552D954633001DDD97 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEAD_CODE_STRIPPING = YES;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				MACOSX_DEPLOYMENT_TARGET = 15.2;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = none.IOSReaderUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				REGISTER_APP_GROUPS = NO;
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx";
				SUPPORTS_MACCATALYST = NO;
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = IOSReader;
				XROS_DEPLOYMENT_TARGET = 2.2;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		8C6ACB212D954631001DDD97 /* Build configuration list for PBXProject "IOSReader" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				8C6ACB4B2D954633001DDD97 /* Debug */,
				8C6ACB4C2D954633001DDD97 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		8C6ACB4D2D954633001DDD97 /* Build configuration list for PBXNativeTarget "IOSReader" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				8C6ACB4E2D954633001DDD97 /* Debug */,
				8C6ACB4F2D954633001DDD97 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		8C6ACB502D954633001DDD97 /* Build configuration list for PBXNativeTarget "IOSReaderTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				8C6ACB512D954633001DDD97 /* Debug */,
				8C6ACB522D954633001DDD97 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		8C6ACB532D954633001DDD97 /* Build configuration list for PBXNativeTarget "IOSReaderUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				8C6ACB542D954633001DDD97 /* Debug */,
				8C6ACB552D954633001DDD97 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCLocalSwiftPackageReference section */
		8C6ACB8C2D95645D001DDD97 /* XCLocalSwiftPackageReference "../../../../Downloads/ZIPFoundation-0.9.19" */ = {
			isa = XCLocalSwiftPackageReference;
			relativePath = "../../../../Downloads/ZIPFoundation-0.9.19";
		};
/* End XCLocalSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		8C0496672DDDDB4C00D78ED4 /* ZIPFoundation */ = {
			isa = XCSwiftPackageProductDependency;
			package = 8C6ACB8C2D95645D001DDD97 /* XCLocalSwiftPackageReference "../../../../Downloads/ZIPFoundation-0.9.19" */;
			productName = ZIPFoundation;
		};
		8C0496692DDDDB5200D78ED4 /* ZIPFoundation */ = {
			isa = XCSwiftPackageProductDependency;
			package = 8C6ACB8C2D95645D001DDD97 /* XCLocalSwiftPackageReference "../../../../Downloads/ZIPFoundation-0.9.19" */;
			productName = ZIPFoundation;
		};
		8C6ACB8E2D9564BA001DDD97 /* ZIPFoundation */ = {
			isa = XCSwiftPackageProductDependency;
			package = 8C6ACB8C2D95645D001DDD97 /* XCLocalSwiftPackageReference "../../../../Downloads/ZIPFoundation-0.9.19" */;
			productName = ZIPFoundation;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = 8C6ACB1E2D954631001DDD97 /* Project object */;
}
