Command line invocation:
    /Applications/Xcode.app/Contents/Developer/usr/bin/xcodebuild -project IOSReader.xcodeproj -scheme IOSReader build

Resolve Package Graph


Resolved source packages:
  ZIPFoundation: /Users/<USER>/Downloads/ZIPFoundation-0.9.19 @ local

--- xcodebuild: WARNING: Using the first of multiple matching destinations:
{ platform:iOS, id:dvtdevice-DVTiPhonePlaceholder-iphoneos:placeholder, name:Any iOS Device }
{ platform:iOS Simulator, id:dvtdevice-DVTiOSDeviceSimulatorPlaceholder-iphonesimulator:placeholder, name:Any iOS Simulator Device }
{ platform:iOS Simulator, arch:arm64, id:26BA3052-B0C9-40D7-8368-78F043E0B1AB, OS:18.3.1, name:iPad (10th generation) }
{ platform:iOS Simulator, arch:x86_64, id:26BA3052-B0C9-40D7-8368-78F043E0B1AB, OS:18.3.1, name:i<PERSON> (10th generation) }
{ platform:iOS Simulator, arch:arm64, id:02C04E86-06DE-4A47-92F7-114BD7D8493B, OS:18.3.1, name:iPad (A16) }
{ platform:iOS Simulator, arch:x86_64, id:02C04E86-06DE-4A47-92F7-114BD7D8493B, OS:18.3.1, name:iPad (A16) }
{ platform:iOS Simulator, arch:arm64, id:91B38C46-F402-44A6-A642-38C0A00B13EC, OS:18.4, name:iPad (A16) }
{ platform:iOS Simulator, arch:x86_64, id:91B38C46-F402-44A6-A642-38C0A00B13EC, OS:18.4, name:iPad (A16) }
{ platform:iOS Simulator, arch:arm64, id:D30A328A-D100-4C3E-B1FF-0A27EC4BC5A3, OS:18.3.1, name:iPad Air 11-inch (M2) }
{ platform:iOS Simulator, arch:x86_64, id:D30A328A-D100-4C3E-B1FF-0A27EC4BC5A3, OS:18.3.1, name:iPad Air 11-inch (M2) }
{ platform:iOS Simulator, arch:arm64, id:30378D85-DD63-4A62-AE57-2A6E7E605592, OS:18.3.1, name:iPad Air 11-inch (M3) }
{ platform:iOS Simulator, arch:x86_64, id:30378D85-DD63-4A62-AE57-2A6E7E605592, OS:18.3.1, name:iPad Air 11-inch (M3) }
{ platform:iOS Simulator, arch:arm64, id:A03414AE-50F9-45FE-ACD1-D83E899B153D, OS:18.4, name:iPad Air 11-inch (M3) }
{ platform:iOS Simulator, arch:x86_64, id:A03414AE-50F9-45FE-ACD1-D83E899B153D, OS:18.4, name:iPad Air 11-inch (M3) }
{ platform:iOS Simulator, arch:arm64, id:FB825B78-7420-45BE-A047-843DE91C2F9D, OS:18.3.1, name:iPad Air 13-inch (M2) }
{ platform:iOS Simulator, arch:x86_64, id:FB825B78-7420-45BE-A047-843DE91C2F9D, OS:18.3.1, name:iPad Air 13-inch (M2) }
{ platform:iOS Simulator, arch:arm64, id:77C30C41-C70A-4915-A9A2-B17066545256, OS:18.3.1, name:iPad Air 13-inch (M3) }
{ platform:iOS Simulator, arch:x86_64, id:77C30C41-C70A-4915-A9A2-B17066545256, OS:18.3.1, name:iPad Air 13-inch (M3) }
{ platform:iOS Simulator, arch:arm64, id:3C14D5DE-B181-4404-834C-7B4EA57AC07A, OS:18.4, name:iPad Air 13-inch (M3) }
{ platform:iOS Simulator, arch:x86_64, id:3C14D5DE-B181-4404-834C-7B4EA57AC07A, OS:18.4, name:iPad Air 13-inch (M3) }
{ platform:iOS Simulator, arch:arm64, id:261233AC-463F-4EB0-9327-F61382F946AD, OS:18.3.1, name:iPad Pro 11-inch (M4) }
{ platform:iOS Simulator, arch:x86_64, id:261233AC-463F-4EB0-9327-F61382F946AD, OS:18.3.1, name:iPad Pro 11-inch (M4) }
{ platform:iOS Simulator, arch:arm64, id:958B9E47-3926-4358-85FD-C19036966A21, OS:18.4, name:iPad Pro 11-inch (M4) }
{ platform:iOS Simulator, arch:x86_64, id:958B9E47-3926-4358-85FD-C19036966A21, OS:18.4, name:iPad Pro 11-inch (M4) }
{ platform:iOS Simulator, arch:arm64, id:038122A3-90F6-491B-957A-D5DC3FA9996F, OS:18.3.1, name:iPad Pro 13-inch (M4) }
{ platform:iOS Simulator, arch:x86_64, id:038122A3-90F6-491B-957A-D5DC3FA9996F, OS:18.3.1, name:iPad Pro 13-inch (M4) }
{ platform:iOS Simulator, arch:arm64, id:9BBBC176-5F82-422A-BBB4-17F5980AC23C, OS:18.4, name:iPad Pro 13-inch (M4) }
{ platform:iOS Simulator, arch:x86_64, id:9BBBC176-5F82-422A-BBB4-17F5980AC23C, OS:18.4, name:iPad Pro 13-inch (M4) }
{ platform:iOS Simulator, arch:arm64, id:593A8463-C8EB-4E6E-8933-18E8C1FE7DB9, OS:18.3.1, name:iPad mini (A17 Pro) }
{ platform:iOS Simulator, arch:x86_64, id:593A8463-C8EB-4E6E-8933-18E8C1FE7DB9, OS:18.3.1, name:iPad mini (A17 Pro) }
{ platform:iOS Simulator, arch:arm64, id:FC076344-C3ED-43D2-913D-2F52558A8772, OS:18.4, name:iPad mini (A17 Pro) }
{ platform:iOS Simulator, arch:x86_64, id:FC076344-C3ED-43D2-913D-2F52558A8772, OS:18.4, name:iPad mini (A17 Pro) }
{ platform:iOS Simulator, arch:arm64, id:7301571E-B16D-4EDA-A3B7-3FF100E2524E, OS:18.3.1, name:iPhone 15 Pro }
{ platform:iOS Simulator, arch:x86_64, id:7301571E-B16D-4EDA-A3B7-3FF100E2524E, OS:18.3.1, name:iPhone 15 Pro }
{ platform:iOS Simulator, arch:arm64, id:7E9475B9-1259-4648-BFEA-72318699F235, OS:18.3.1, name:iPhone 16 }
{ platform:iOS Simulator, arch:x86_64, id:7E9475B9-1259-4648-BFEA-72318699F235, OS:18.3.1, name:iPhone 16 }
{ platform:iOS Simulator, arch:arm64, id:0347A8A6-1BD2-4761-A300-1548354AF787, OS:18.4, name:iPhone 16 }
{ platform:iOS Simulator, arch:x86_64, id:0347A8A6-1BD2-4761-A300-1548354AF787, OS:18.4, name:iPhone 16 }
{ platform:iOS Simulator, arch:arm64, id:85538649-CB1B-443C-B839-CDF8735C6A12, OS:18.3.1, name:iPhone 16 Plus }
{ platform:iOS Simulator, arch:x86_64, id:85538649-CB1B-443C-B839-CDF8735C6A12, OS:18.3.1, name:iPhone 16 Plus }
{ platform:iOS Simulator, arch:arm64, id:ACC45F86-E040-4EB7-ABFD-63EC299618C7, OS:18.4, name:iPhone 16 Plus }
{ platform:iOS Simulator, arch:x86_64, id:ACC45F86-E040-4EB7-ABFD-63EC299618C7, OS:18.4, name:iPhone 16 Plus }
{ platform:iOS Simulator, arch:arm64, id:533F4D43-1B7B-4E29-A80B-58A33A21B3EF, OS:18.3.1, name:iPhone 16 Pro }
{ platform:iOS Simulator, arch:x86_64, id:533F4D43-1B7B-4E29-A80B-58A33A21B3EF, OS:18.3.1, name:iPhone 16 Pro }
{ platform:iOS Simulator, arch:arm64, id:2B0FFA9A-AE1F-4DE6-9CA1-8232400B407D, OS:18.4, name:iPhone 16 Pro }
{ platform:iOS Simulator, arch:x86_64, id:2B0FFA9A-AE1F-4DE6-9CA1-8232400B407D, OS:18.4, name:iPhone 16 Pro }
{ platform:iOS Simulator, arch:arm64, id:D5D87DC9-29BA-4443-B1A8-AAAEF7707895, OS:18.3.1, name:iPhone 16 Pro Max }
{ platform:iOS Simulator, arch:x86_64, id:D5D87DC9-29BA-4443-B1A8-AAAEF7707895, OS:18.3.1, name:iPhone 16 Pro Max }
{ platform:iOS Simulator, arch:arm64, id:6012CD14-12FB-4961-BC0F-B83B5CD07F76, OS:18.4, name:iPhone 16 Pro Max }
{ platform:iOS Simulator, arch:x86_64, id:6012CD14-12FB-4961-BC0F-B83B5CD07F76, OS:18.4, name:iPhone 16 Pro Max }
{ platform:iOS Simulator, arch:arm64, id:7CDA8EDC-8261-4CDA-9226-32A7AECEBDC6, OS:18.3.1, name:iPhone 16e }
{ platform:iOS Simulator, arch:x86_64, id:7CDA8EDC-8261-4CDA-9226-32A7AECEBDC6, OS:18.3.1, name:iPhone 16e }
{ platform:iOS Simulator, arch:arm64, id:BD53E19C-1FB7-4A5C-A30E-534BE5B865AD, OS:18.4, name:iPhone 16e }
{ platform:iOS Simulator, arch:x86_64, id:BD53E19C-1FB7-4A5C-A30E-534BE5B865AD, OS:18.4, name:iPhone 16e }
{ platform:iOS Simulator, arch:arm64, id:DDDF818B-76D9-4480-BD5A-DA9B802269E2, OS:18.3.1, name:iPhone SE (3rd generation) }
{ platform:iOS Simulator, arch:x86_64, id:DDDF818B-76D9-4480-BD5A-DA9B802269E2, OS:18.3.1, name:iPhone SE (3rd generation) }
ComputePackagePrebuildTargetDependencyGraph

Prepare packages

CreateBuildRequest

SendProjectDescription

CreateBuildOperation

ComputeTargetDependencyGraph
note: Building targets in dependency order
note: Target dependency graph (4 targets)
    Target 'IOSReader' in project 'IOSReader'
        ➜ Explicit dependency on target 'ZIPFoundation' in project 'ZIPFoundation'
    Target 'ZIPFoundation' in project 'ZIPFoundation'
        ➜ Explicit dependency on target 'ZIPFoundation' in project 'ZIPFoundation'
        ➜ Explicit dependency on target 'ZIPFoundation_ZIPFoundation' in project 'ZIPFoundation'
    Target 'ZIPFoundation' in project 'ZIPFoundation'
        ➜ Explicit dependency on target 'ZIPFoundation_ZIPFoundation' in project 'ZIPFoundation'
    Target 'ZIPFoundation_ZIPFoundation' in project 'ZIPFoundation' (no dependencies)

GatherProvisioningInputs

CreateBuildDescription

ExecuteExternalTool /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -v -E -dM -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.4.sdk -x c -c /dev/null

ExecuteExternalTool /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc --version

ExecuteExternalTool /Applications/Xcode.app/Contents/Developer/usr/bin/ibtool --version --output-format xml1

ExecuteExternalTool /Applications/Xcode.app/Contents/Developer/usr/bin/actool --print-asset-tag-combinations --output-format xml1 /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Assets.xcassets /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Preview Content/Preview Assets.xcassets

ExecuteExternalTool /Applications/Xcode.app/Contents/Developer/usr/bin/actool --version --output-format xml1

ExecuteExternalTool /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ld -version_details

Build description signature: ed51863b2b0719ffa18f535ebe252112
Build description path: /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/XCBuildData/ed51863b2b0719ffa18f535ebe252112.xcbuilddata
ClangStatCache /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.4.sdk /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphoneos18.4-22E235-a09501eccf75f892bc376f81961b27ba.sdkstatcache
    cd /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader.xcodeproj
    /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.4.sdk -o /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphoneos18.4-22E235-a09501eccf75f892bc376f81961b27ba.sdkstatcache

WriteAuxiliaryFile /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphoneos/IOSReader.build/Objects-normal/arm64/IOSReader.SwiftFileList (in target 'IOSReader' from project 'IOSReader')
    cd /Users/<USER>/Desktop/Coding/IOSReader/IOSReader
    write-file /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphoneos/IOSReader.build/Objects-normal/arm64/IOSReader.SwiftFileList

WriteAuxiliaryFile /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphoneos/IOSReader.build/Objects-normal/arm64/IOSReader.SwiftConstValuesFileList (in target 'IOSReader' from project 'IOSReader')
    cd /Users/<USER>/Desktop/Coding/IOSReader/IOSReader
    write-file /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphoneos/IOSReader.build/Objects-normal/arm64/IOSReader.SwiftConstValuesFileList

WriteAuxiliaryFile /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphoneos/IOSReader.build/Objects-normal/arm64/IOSReader.LinkFileList (in target 'IOSReader' from project 'IOSReader')
    cd /Users/<USER>/Desktop/Coding/IOSReader/IOSReader
    write-file /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphoneos/IOSReader.build/Objects-normal/arm64/IOSReader.LinkFileList

ProcessInfoPlistFile /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Products/Debug-iphoneos/ZIPFoundation_ZIPFoundation.bundle/Info.plist /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/ZIPFoundation.build/Debug-iphoneos/ZIPFoundation_ZIPFoundation.build/empty-ZIPFoundation_ZIPFoundation.plist (in target 'ZIPFoundation_ZIPFoundation' from project 'ZIPFoundation')
    cd /Users/<USER>/Downloads/ZIPFoundation-0.9.19
    builtin-infoPlistUtility /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/ZIPFoundation.build/Debug-iphoneos/ZIPFoundation_ZIPFoundation.build/empty-ZIPFoundation_ZIPFoundation.plist -producttype com.apple.product-type.bundle -expandbuildsettings -format binary -platform iphoneos -requiredArchitecture arm64 -o /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Products/Debug-iphoneos/ZIPFoundation_ZIPFoundation.bundle/Info.plist

ProcessProductPackaging /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/IOSReader.entitlements /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphoneos/IOSReader.build/IOSReader.app.xcent (in target 'IOSReader' from project 'IOSReader')
    cd /Users/<USER>/Desktop/Coding/IOSReader/IOSReader
    
    Entitlements:
    
    {
    "application-identifier" = "L5ZCXC64Q2.none.IOSReader";
    "com.apple.developer.team-identifier" = L5ZCXC64Q2;
    "get-task-allow" = 1;
}
    
    builtin-productPackagingUtility /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/IOSReader.entitlements -entitlements -format xml -o /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphoneos/IOSReader.build/IOSReader.app.xcent

ProcessProductPackagingDER /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphoneos/IOSReader.build/IOSReader.app.xcent /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphoneos/IOSReader.build/IOSReader.app.xcent.der (in target 'IOSReader' from project 'IOSReader')
    cd /Users/<USER>/Desktop/Coding/IOSReader/IOSReader
    /usr/bin/derq query -f xml -i /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphoneos/IOSReader.build/IOSReader.app.xcent -o /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphoneos/IOSReader.build/IOSReader.app.xcent.der --raw

GenerateAssetSymbols /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Assets.xcassets /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Preview\ Content/Preview\ Assets.xcassets (in target 'IOSReader' from project 'IOSReader')
    cd /Users/<USER>/Desktop/Coding/IOSReader/IOSReader
    /Applications/Xcode.app/Contents/Developer/usr/bin/actool /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Assets.xcassets /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Preview\ Content/Preview\ Assets.xcassets --compile /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Products/Debug-iphoneos/IOSReader.app --output-format human-readable-text --notices --warnings --export-dependency-info /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphoneos/IOSReader.build/assetcatalog_dependencies --output-partial-info-plist /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphoneos/IOSReader.build/assetcatalog_generated_info.plist --app-icon AppIcon --accent-color AccentColor --compress-pngs --enable-on-demand-resources YES --development-region zh-Hans --target-device iphone --target-device ipad --minimum-deployment-target 18.2 --platform iphoneos --bundle-identifier none.IOSReader --generate-swift-asset-symbols /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphoneos/IOSReader.build/DerivedSources/GeneratedAssetSymbols.swift --generate-objc-asset-symbols /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphoneos/IOSReader.build/DerivedSources/GeneratedAssetSymbols.h --generate-asset-symbol-index /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphoneos/IOSReader.build/DerivedSources/GeneratedAssetSymbols-Index.plist
/* com.apple.actool.compilation-results */
/Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphoneos/IOSReader.build/DerivedSources/GeneratedAssetSymbols-Index.plist
/Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphoneos/IOSReader.build/DerivedSources/GeneratedAssetSymbols.h
/Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphoneos/IOSReader.build/DerivedSources/GeneratedAssetSymbols.swift


CompileAssetCatalogVariant thinned /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Products/Debug-iphoneos/IOSReader.app /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Assets.xcassets /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Preview\ Content/Preview\ Assets.xcassets (in target 'IOSReader' from project 'IOSReader')
    cd /Users/<USER>/Desktop/Coding/IOSReader/IOSReader
    /Applications/Xcode.app/Contents/Developer/usr/bin/actool /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Assets.xcassets /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Preview\ Content/Preview\ Assets.xcassets --compile /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphoneos/IOSReader.build/assetcatalog_output/thinned --output-format human-readable-text --notices --warnings --export-dependency-info /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphoneos/IOSReader.build/assetcatalog_dependencies_thinned --output-partial-info-plist /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphoneos/IOSReader.build/assetcatalog_generated_info.plist_thinned --app-icon AppIcon --accent-color AccentColor --compress-pngs --enable-on-demand-resources YES --development-region zh-Hans --target-device iphone --target-device ipad --minimum-deployment-target 18.2 --platform iphoneos

SwiftDriver IOSReader normal arm64 com.apple.xcode.tools.swift.compiler (in target 'IOSReader' from project 'IOSReader')
    cd /Users/<USER>/Desktop/Coding/IOSReader/IOSReader
    builtin-SwiftDriver -- /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc -module-name IOSReader -Onone -enforce-exclusivity\=checked @/Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphoneos/IOSReader.build/Objects-normal/arm64/IOSReader.SwiftFileList -DDEBUG -enable-experimental-feature DebugDescriptionMacro -sdk /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.4.sdk -target arm64-apple-ios18.2 -g -module-cache-path /Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex -Xfrontend -serialize-debugging-options -profile-coverage-mapping -profile-generate -enable-testing -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Index.noindex/DataStore -swift-version 6 -I /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Products/Debug-iphoneos -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Products/Debug-iphoneos/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Products/Debug-iphoneos -emit-localized-strings -emit-localized-strings-path /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphoneos/IOSReader.build/Objects-normal/arm64 -c -j10 -disable-batch-mode -incremental -Xcc -ivfsstatcache -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphoneos18.4-22E235-a09501eccf75f892bc376f81961b27ba.sdkstatcache -output-file-map /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphoneos/IOSReader.build/Objects-normal/arm64/IOSReader-OutputFileMap.json -use-frontend-parseable-output -save-temps -no-color-diagnostics -serialize-diagnostics -emit-dependencies -emit-module -emit-module-path /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphoneos/IOSReader.build/Objects-normal/arm64/IOSReader.swiftmodule -validate-clang-modules-once -clang-build-session-file /Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphoneos/IOSReader.build/swift-overrides.hmap -emit-const-values -Xfrontend -const-gather-protocols-file -Xfrontend /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphoneos/IOSReader.build/Objects-normal/arm64/IOSReader_const_extract_protocols.json -Xcc -iquote -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphoneos/IOSReader.build/IOSReader-generated-files.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphoneos/IOSReader.build/IOSReader-own-target-headers.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphoneos/IOSReader.build/IOSReader-all-non-framework-target-headers.hmap -Xcc -ivfsoverlay -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphoneos/IOSReader-a3beb3f0916461e73b6d69f5c8becc9c-VFS-iphoneos/all-product-headers.yaml -Xcc -iquote -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphoneos/IOSReader.build/IOSReader-project-headers.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Products/Debug-iphoneos/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphoneos/IOSReader.build/DerivedSources-normal/arm64 -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphoneos/IOSReader.build/DerivedSources/arm64 -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphoneos/IOSReader.build/DerivedSources -Xcc -DDEBUG\=1 -emit-objc-header -emit-objc-header-path /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphoneos/IOSReader.build/Objects-normal/arm64/IOSReader-Swift.h -working-directory /Users/<USER>/Desktop/Coding/IOSReader/IOSReader -experimental-emit-module-separately -disable-cmo

SwiftCompile normal arm64 Compiling\ readersettingsview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/readersettingsview.swift (in target 'IOSReader' from project 'IOSReader')

SwiftCompile normal arm64 /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/readersettingsview.swift (in target 'IOSReader' from project 'IOSReader')
    cd /Users/<USER>/Desktop/Coding/IOSReader/IOSReader
    
/Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/readersettingsview.swift:26:22: warning: 'onChange(of:perform:)' was deprecated in iOS 17.0: Use `onChange` with a two or zero parameter action closure instead.
                    .onChange(of: settings.fontSize) { _ in
                     ^
/Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/readersettingsview.swift:62:22: warning: 'onChange(of:perform:)' was deprecated in iOS 17.0: Use `onChange` with a two or zero parameter action closure instead.
                    .onChange(of: selectedTab) { newValue in
                     ^

SwiftCompile normal arm64 Compiling\ BookListViewModel.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/ViewModels/BookListViewModel.swift (in target 'IOSReader' from project 'IOSReader')

SwiftCompile normal arm64 /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/ViewModels/BookListViewModel.swift (in target 'IOSReader' from project 'IOSReader')
    cd /Users/<USER>/Desktop/Coding/IOSReader/IOSReader
    

SwiftCompile normal arm64 Compiling\ imagecache.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Utils/imagecache.swift (in target 'IOSReader' from project 'IOSReader')

SwiftCompile normal arm64 /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Utils/imagecache.swift (in target 'IOSReader' from project 'IOSReader')
    cd /Users/<USER>/Desktop/Coding/IOSReader/IOSReader
    

SwiftCompile normal arm64 Compiling\ AccountManager.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Managers/AccountManager.swift (in target 'IOSReader' from project 'IOSReader')

SwiftCompile normal arm64 /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Managers/AccountManager.swift (in target 'IOSReader' from project 'IOSReader')
    cd /Users/<USER>/Desktop/Coding/IOSReader/IOSReader
    

SwiftCompile normal arm64 Compiling\ RegisterView.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/RegisterView.swift (in target 'IOSReader' from project 'IOSReader')
SwiftCompile normal arm64 /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/RegisterView.swift (in target 'IOSReader' from project 'IOSReader')
    cd /Users/<USER>/Desktop/Coding/IOSReader/IOSReader
    

SwiftCompile normal arm64 Compiling\ chapterlistview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/chapterlistview.swift (in target 'IOSReader' from project 'IOSReader')

SwiftCompile normal arm64 /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/chapterlistview.swift (in target 'IOSReader' from project 'IOSReader')
    cd /Users/<USER>/Desktop/Coding/IOSReader/IOSReader
    

SwiftCompile normal arm64 Compiling\ readercontentview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/readercontentview.swift (in target 'IOSReader' from project 'IOSReader')

SwiftCompile normal arm64 /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/readercontentview.swift (in target 'IOSReader' from project 'IOSReader')
    cd /Users/<USER>/Desktop/Coding/IOSReader/IOSReader
    

SwiftCompile normal arm64 Compiling\ BookSourceView.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/BookSourceView.swift (in target 'IOSReader' from project 'IOSReader')
SwiftCompile normal arm64 /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/BookSourceView.swift (in target 'IOSReader' from project 'IOSReader')
    cd /Users/<USER>/Desktop/Coding/IOSReader/IOSReader
    

SwiftEmitModule normal arm64 Emitting\ module\ for\ IOSReader (in target 'IOSReader' from project 'IOSReader')
EmitSwiftModule normal arm64 (in target 'IOSReader' from project 'IOSReader')
    cd /Users/<USER>/Desktop/Coding/IOSReader/IOSReader
    

SwiftCompile normal arm64 Compiling\ chapterparser.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Parsers/chapterparser.swift (in target 'IOSReader' from project 'IOSReader')

SwiftCompile normal arm64 /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Parsers/chapterparser.swift (in target 'IOSReader' from project 'IOSReader')
    cd /Users/<USER>/Desktop/Coding/IOSReader/IOSReader
    
/Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Parsers/chapterparser.swift:80:13: warning: initialization of variable 'lastChapterEndPosition' was never used; consider replacing with assignment to '_' or removing it
        var lastChapterEndPosition = 0
        ~~~~^~~~~~~~~~~~~~~~~~~~~~
        _
/Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Parsers/chapterparser.swift:142:24: warning: value 'match' was defined but never used; consider replacing with boolean test
                if let match = regex.firstMatch(in: trimmedLine, options: [], range: range) {
                   ~~~~^~~~~~~~
                                                                                            != nil

SwiftDriverJobDiscovery normal arm64 Compiling imagecache.swift (in target 'IOSReader' from project 'IOSReader')

SwiftCompile normal arm64 Compiling\ htmlparserutils.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Utils/htmlparserutils.swift (in target 'IOSReader' from project 'IOSReader')

SwiftCompile normal arm64 /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Utils/htmlparserutils.swift (in target 'IOSReader' from project 'IOSReader')
    cd /Users/<USER>/Desktop/Coding/IOSReader/IOSReader
    

SwiftDriverJobDiscovery normal arm64 Compiling chapterlistview.swift (in target 'IOSReader' from project 'IOSReader')

SwiftCompile normal arm64 Compiling\ thememodeview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/thememodeview.swift (in target 'IOSReader' from project 'IOSReader')
Failed frontend command:
/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swift-frontend -frontend -c /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Utils/imagecache.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/readersettingsview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Managers/AccountManager.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/ViewModels/BookListViewModel.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Extensions/arrayextensions.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/RegisterView.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/readercontentview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Utils/xmlhandlers.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/chapterlistview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/BookSourceView.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Components/SharedReaderComponents.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Coordinators/ReaderCoordinator.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/readingrecordview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/backupview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Core/readercore.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/contentview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Core/readerrenderer.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Parsers/epubparser.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Item.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/webserviceview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Components/ReaderToolbars.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/SharedViewStyles.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Extensions/htmlparserutils+extension.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Services/booksourcefetcher.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/BookStoreView.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Services/bookstorage.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Services/chapterstorage.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Models/Book.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Models/Bookmark.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Parsers/documentparser.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Parsers/htmlparser.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/bookmarkview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/BookDetailView.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/BookShelfView.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/templateeditorview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/UnifiedReaderView.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/dictionaryview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Utils/htmlparserutils.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/AccountManagementView.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/IOSReaderApp.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/BookSourceSelectorView.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/AdvancedFeatures.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Extensions/colorextensions.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/ReadingRecord.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/AddBookView.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/SourceManagerView.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/multipleselectionrow.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Models/User.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/txtruleview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/readingcontentstack.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/ReadingMode.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/filemanagerview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Managers/booksourcemanager.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Extensions/ImageExtensions.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/PasswordResetView.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Extensions/stringextensions.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/pdfkitpageview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/settingsview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Services/contentfetcher.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/LoginView.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Models/ReadingProgress.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Utils/stringbuilder.swift -primary-file /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/thememodeview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/ReaderView.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/themesettingsview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/ReaderTypes.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Parsers/chapterparser.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Managers/backupmanager.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/ViewModels/ReaderViewModel.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Managers/memorymanager.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/replacementview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Models/BookSource.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Models/Chapter.swift /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphoneos/IOSReader.build/DerivedSources/GeneratedAssetSymbols.swift -emit-dependencies-path /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphoneos/IOSReader.build/Objects-normal/arm64/thememodeview.d -emit-const-values-path /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphoneos/IOSReader.build/Objects-normal/arm64/thememodeview.swiftconstvalues -emit-reference-dependencies-path /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphoneos/IOSReader.build/Objects-normal/arm64/thememodeview.swiftdeps -serialize-diagnostics-path /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphoneos/IOSReader.build/Objects-normal/arm64/thememodeview.dia -emit-localized-strings -emit-localized-strings-path /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphoneos/IOSReader.build/Objects-normal/arm64 -target arm64-apple-ios18.2 -Xllvm -aarch64-use-tbi -enable-objc-interop -stack-check -sdk /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.4.sdk -I /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Products/Debug-iphoneos -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Products/Debug-iphoneos/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Products/Debug-iphoneos -no-color-diagnostics -enable-testing -g -debug-info-format\=dwarf -dwarf-version\=5 -module-cache-path /Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex -profile-generate -profile-coverage-mapping -swift-version 6 -enforce-exclusivity\=checked -Onone -D DEBUG -serialize-debugging-options -const-gather-protocols-file /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphoneos/IOSReader.build/Objects-normal/arm64/IOSReader_const_extract_protocols.json -enable-experimental-feature DebugDescriptionMacro -empty-abi-descriptor -validate-clang-modules-once -clang-build-session-file /Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation -Xcc -working-directory -Xcc /Users/<USER>/Desktop/Coding/IOSReader/IOSReader -resource-dir /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift -enable-anonymous-context-mangled-names -file-compilation-dir /Users/<USER>/Desktop/Coding/IOSReader/IOSReader -Xcc -ivfsstatcache -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphoneos18.4-22E235-a09501eccf75f892bc376f81961b27ba.sdkstatcache -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphoneos/IOSReader.build/swift-overrides.hmap -Xcc -iquote -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphoneos/IOSReader.build/IOSReader-generated-files.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphoneos/IOSReader.build/IOSReader-own-target-headers.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphoneos/IOSReader.build/IOSReader-all-non-framework-target-headers.hmap -Xcc -ivfsoverlay -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphoneos/IOSReader-a3beb3f0916461e73b6d69f5c8becc9c-VFS-iphoneos/all-product-headers.yaml -Xcc -iquote -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphoneos/IOSReader.build/IOSReader-project-headers.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Products/Debug-iphoneos/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphoneos/IOSReader.build/DerivedSources-normal/arm64 -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphoneos/IOSReader.build/DerivedSources/arm64 -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphoneos/IOSReader.build/DerivedSources -Xcc -DDEBUG\=1 -module-name IOSReader -frontend-parseable-output -disable-clang-spi -target-sdk-version 18.4 -target-sdk-name iphoneos18.4 -external-plugin-path /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/lib/swift/host/plugins\#/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/bin/swift-plugin-server -external-plugin-path /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/local/lib/swift/host/plugins\#/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/bin/swift-plugin-server -in-process-plugin-server-path /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/host/libSwiftInProcPluginServer.dylib -plugin-path /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/host/plugins -plugin-path /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/local/lib/swift/host/plugins -o /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphoneos/IOSReader.build/Objects-normal/arm64/thememodeview.o -index-unit-output-path /IOSReader.build/Debug-iphoneos/IOSReader.build/Objects-normal/arm64/thememodeview.o -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Index.noindex/DataStore -index-system-modules

SwiftCompile normal arm64 /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/thememodeview.swift (in target 'IOSReader' from project 'IOSReader')
    cd /Users/<USER>/Desktop/Coding/IOSReader/IOSReader
    
/Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/RegisterView.swift:631:1: error: no exact matches in call to macro 'Preview'
#Preview {
^
/Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/RegisterView.swift:632:5: note: closure containing control flow statement cannot be used with result builder 'PreviewMacroBodyBuilder'
    do {
    ^
/Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/RegisterView.swift:632:5: note: closure containing control flow statement cannot be used with result builder 'PreviewMacroBodyBuilder'
    do {
    ^
/Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/BookSourceSelectorView.swift:114:1: error: no exact matches in call to macro 'Preview'
#Preview {
^
/Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/BookSourceSelectorView.swift:115:5: note: closure containing control flow statement cannot be used with result builder 'PreviewMacroBodyBuilder'
    do {
    ^
/Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/BookSourceSelectorView.swift:115:5: note: closure containing control flow statement cannot be used with result builder 'PreviewMacroBodyBuilder'
    do {
    ^
/Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/SourceManagerView.swift:392:1: error: no exact matches in call to macro 'Preview'
#Preview {
^
/Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/SourceManagerView.swift:393:5: note: closure containing control flow statement cannot be used with result builder 'PreviewMacroBodyBuilder'
    do {
    ^
/Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/SourceManagerView.swift:393:5: note: closure containing control flow statement cannot be used with result builder 'PreviewMacroBodyBuilder'
    do {
    ^

SwiftDriverJobDiscovery normal arm64 Compiling readercontentview.swift (in target 'IOSReader' from project 'IOSReader')

SwiftCompile normal arm64 Compiling\ dictionaryview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/dictionaryview.swift (in target 'IOSReader' from project 'IOSReader')

SwiftCompile normal arm64 /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/dictionaryview.swift (in target 'IOSReader' from project 'IOSReader')
    cd /Users/<USER>/Desktop/Coding/IOSReader/IOSReader
    

SwiftDriverJobDiscovery normal arm64 Compiling AccountManager.swift (in target 'IOSReader' from project 'IOSReader')

SwiftCompile normal arm64 Compiling\ booksourcemanager.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Managers/booksourcemanager.swift (in target 'IOSReader' from project 'IOSReader')

SwiftCompile normal arm64 /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Managers/booksourcemanager.swift (in target 'IOSReader' from project 'IOSReader')
    cd /Users/<USER>/Desktop/Coding/IOSReader/IOSReader
    

SwiftDriverJobDiscovery normal arm64 Compiling BookListViewModel.swift (in target 'IOSReader' from project 'IOSReader')

SwiftCompile normal arm64 Compiling\ ImageExtensions.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Extensions/ImageExtensions.swift (in target 'IOSReader' from project 'IOSReader')

SwiftCompile normal arm64 /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Extensions/ImageExtensions.swift (in target 'IOSReader' from project 'IOSReader')
    cd /Users/<USER>/Desktop/Coding/IOSReader/IOSReader
    

SwiftDriverJobDiscovery normal arm64 Compiling readersettingsview.swift (in target 'IOSReader' from project 'IOSReader')

SwiftCompile normal arm64 Compiling\ Chapter.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Models/Chapter.swift (in target 'IOSReader' from project 'IOSReader')

SwiftCompile normal arm64 /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Models/Chapter.swift (in target 'IOSReader' from project 'IOSReader')
    cd /Users/<USER>/Desktop/Coding/IOSReader/IOSReader
    

SwiftDriverJobDiscovery normal arm64 Compiling chapterparser.swift (in target 'IOSReader' from project 'IOSReader')

SwiftCompile normal arm64 Compiling\ readercore.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Core/readercore.swift (in target 'IOSReader' from project 'IOSReader')

SwiftDriverJobDiscovery normal arm64 Compiling ImageExtensions.swift (in target 'IOSReader' from project 'IOSReader')

SwiftCompile normal arm64 Compiling\ themesettingsview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/themesettingsview.swift (in target 'IOSReader' from project 'IOSReader')
Command SwiftCompile failed with a nonzero exit code

** BUILD FAILED **


The following build commands failed:
	SwiftCompile normal arm64 Compiling\ thememodeview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/thememodeview.swift (in target 'IOSReader' from project 'IOSReader')
	SwiftCompile normal arm64 /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/thememodeview.swift (in target 'IOSReader' from project 'IOSReader')
	SwiftCompile normal arm64 Compiling\ themesettingsview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/themesettingsview.swift (in target 'IOSReader' from project 'IOSReader')
	Building project IOSReader with scheme IOSReader
(4 failures)
