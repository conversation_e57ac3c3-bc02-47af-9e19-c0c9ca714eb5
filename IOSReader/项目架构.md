# IOSReader 项目架构

## 1. 项目结构

### 1.1 核心模块
- **ReaderCoordinator**: 阅读器核心协调器
  - 管理全局状态和设置
  - 协调子模块工作流程
  - 处理用户交互和业务逻辑
  - 提供统一API

- **ReaderCore**: 阅读引擎核心
  - 文本处理和分段
  - 内存管理
  - 分页算法

- **ReaderRenderer**: 渲染引擎
  - 页面渲染
  - 布局计算
  - 性能优化

### 1.2 数据管理
- **BookSourceManager**: 书源管理
  - 书源配置
  - 数据抓取

- **BookStorage**: 书籍存储
  - 本地缓存
  - 数据持久化

- **BackupManager**: 备份管理
  - 数据备份
  - 恢复功能

### 1.3 UI组件
- **视图组件**
  - UnifiedReaderView: 统一阅读视图
  - BookShelfView: 书架视图
  - SettingsView: 设置视图
  - BookmarkView: 书签视图

- **工具组件**
  - ToolbarButton: 工具栏按钮
  - MultipleSelectionRow: 多选行
  - ThemeSettingsView: 主题设置

### 1.4 工具类
- **ContentFetcher**: 内容获取
- **MemoryManager**: 内存管理
- **DocumentParser**: 文档解析
- **HTMLParser**: HTML解析

## 2. 模块依赖关系

```mermaid
graph TD
    A[ReaderCoordinator] --> B[ReaderCore]
    A --> C[ReaderRenderer]
    A --> D[BookSourceManager]
    A --> E[BookStorage]
    
    B --> F[MemoryManager]
    C --> F
    
    D --> G[ContentFetcher]
    E --> H[BackupManager]
    
    I[UnifiedReaderView] --> A
    J[BookShelfView] --> A
    K[SettingsView] --> A
```

## 3. 代码复用优化

### 3.1 共享组件
- SharedReaderComponents: 复用的阅读器组件
- ReaderTypes: 通用类型定义
- ReadingMode: 阅读模式配置

### 3.2 工具函数
- 文本处理
- 文件操作
- 内存管理
- 渲染优化

### 3.3 接口规范
- 统一的数据模型
- 标准化的API设计
- 一致的错误处理

## 4. 性能优化

### 4.1 渲染优化
- 异步渲染
- 分批加载
- 内存复用

### 4.2 内存管理
- 自动清理
- 缓存控制
- 内存监控

### 4.3 并发处理
- 任务队列
- 并行渲染
- 异步加载


## 5. 当前项目状态

### 5.1 已实现功能
- **阅读器核心功能**
  - TXT分章节解析
  - EPUB格式支持
  - 基础PDF渲染
  - 文本分段和分页算法

- **书籍管理**
  - 本地书籍导入
  - 基础分类功能
  - 阅读进度记录

- **用户体验**
  - 主题切换
  - 字体大小调整
  - 日间/夜间模式

- **数据安全**
  - 本地备份功能
  - 基础数据加密

### 5.2 近期优化方向
1. **阅读体验提升**
   - 增强PDF渲染性能
   - 支持MOBI格式
   - 优化分章节准确性

2. **社交功能**
   - 笔记分享
   - 书评系统

3. **云端同步**
   - 阅读进度同步
   - 跨设备数据同步

项目架构稳定，功能持续完善中。