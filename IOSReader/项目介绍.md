# IOSReader 项目介绍

## 框架概述
IOSReader 是一个基于 SwiftUI 的电子书阅读器应用，采用现代化的 MVVM 架构设计。核心框架特点包括：

- **响应式状态管理**：使用 Combine 框架实现数据流管理
- **模块化设计**：各功能模块解耦，便于扩展和维护
- **跨平台支持**：适配 iOS 和 macOS 平台

## 核心组件

### ReaderCoordinator
作为应用的核心协调器，主要职责包括：

- 管理阅读器的全局状态和设置
- 协调各个子模块的工作流程
- 处理用户交互和业务逻辑
- 提供统一的 API 供 UI 层调用

### 主要功能模块

1. **书源管理**
   - 支持多种书源导入和管理
   - 提供书源测试功能

2. **阅读设置**
   - 字体大小调整
   - 主题颜色选择
   - 日间/夜间模式切换

3. **文件管理**
   - 本地文件浏览和导入
   - 文件内容解析

4. **备份恢复**
   - 本地备份
   - WebDAV 同步

## 页面功能

（此处预留截图位置）

## 技术栈

- **UI框架**: SwiftUI
- **状态管理**: Combine
- **文件解析**: PDFKit + EPUB解析 + 自定义TXT解析器
- **持久化**: SwiftData + CoreData
- **网络请求**: URLSession + Alamofire
- **图片缓存**: Kingfisher
- **PDF渲染**: PDFKit + 自定义优化渲染器

## 安装与运行

1. 克隆项目仓库
2. 使用 Xcode 打开项目
3. 选择目标设备运行

## 贡献指南

欢迎通过 Issues 或 Pull Request 参与项目贡献。