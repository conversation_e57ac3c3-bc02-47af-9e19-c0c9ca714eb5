== DATE:

    Wednesday, May 28, 2025 at 14:50:43 China Standard Time
    
    2025-05-28T06:50:43Z



== PREVIEW UPDATE ERROR:

    SchemeBuildError: Failed to build the scheme “IOSReader”
    
    sending value of non-Sendable type '() async -> ()' risks causing data races
    
    Build target IOSReader:
    note: Disabling hardened runtime with ad-hoc codesigning. (in target 'IOSReader' from project 'IOSReader')
    
    
    Compiling BookShelfView.swift:
    Failed frontend command:
    /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swift-frontend -frontend -c /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/SharedViewStyles.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/AccountManagementView.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/settingsview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Models/Book.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/UnifiedReaderView.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/chapterlistview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/ReaderTypes.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Managers/booksourcemanager.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Parsers/epubparser.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/LoginView.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Utils/imagecache.swift -primary-file /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/BookShelfView.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Item.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/ReaderView.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Extensions/stringextensions.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Services/contentfetcher.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/backupview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/pdfkitpageview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/templateeditorview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Extensions/colorextensions.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/BookSourceSelectorView.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Models/BookSource.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Services/booksourcefetcher.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/ViewModels/ReaderViewModel.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/AdvancedFeatures.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/dictionaryview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Models/User.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/ReadingRecord.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Managers/AccountManager.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/replacementview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Core/readerrenderer.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Utils/xmlhandlers.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/txtruleview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/BookStoreView.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Coordinators/ReaderCoordinator.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Managers/backupmanager.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/RegisterView.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Parsers/documentparser.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Utils/htmlparserutils.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/bookmarkview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/readersettingsview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Components/ReaderToolbars.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/themesettingsview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Extensions/ImageExtensions.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Models/ReadingProgress.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Services/chapterstorage.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/AddBookView.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/multipleselectionrow.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/SourceManagerView.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Extensions/arrayextensions.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Models/Bookmark.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/ViewModels/BookListViewModel.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/PasswordResetView.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/webserviceview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/contentview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/ReadingMode.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/readingrecordview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Core/readercore.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Managers/memorymanager.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Parsers/htmlparser.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Utils/stringbuilder.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/BookSourceView.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/readingcontentstack.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Parsers/chapterparser.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/BookDetailView.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/IOSReaderApp.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/readercontentview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Components/SharedReaderComponents.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/thememodeview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Extensions/htmlparserutils+extension.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Models/Chapter.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Services/bookstorage.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/filemanagerview.swift /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/DerivedSources/GeneratedAssetSymbols.swift -emit-dependencies-path /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/Objects-normal/arm64/BookShelfView.d -emit-const-values-path /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/Objects-normal/arm64/BookShelfView.swiftconstvalues -emit-reference-dependencies-path /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/Objects-normal/arm64/BookShelfView.swiftdeps -serialize-diagnostics-path /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/Objects-normal/arm64/BookShelfView.dia -emit-localized-strings -emit-localized-strings-path /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/Objects-normal/arm64 -target arm64-apple-ios18.2-simulator -Xllvm -aarch64-use-tbi -enable-objc-interop -sdk /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk -I /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Products/Debug-iphonesimulator -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Products/Debug-iphonesimulator -no-color-diagnostics -enable-testing -g -debug-info-format\=dwarf -dwarf-version\=5 -module-cache-path /Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex -profile-generate -profile-coverage-mapping -swift-version 6 -enforce-exclusivity\=checked -Onone -D DEBUG -serialize-debugging-options -const-gather-protocols-file /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/Objects-normal/arm64/IOSReader_const_extract_protocols.json -enable-experimental-feature DebugDescriptionMacro -empty-abi-descriptor -validate-clang-modules-once -clang-build-session-file /Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation -Xcc -working-directory -Xcc /Users/<USER>/Desktop/Coding/IOSReader/IOSReader -resource-dir /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift -enable-anonymous-context-mangled-names -file-compilation-dir /Users/<USER>/Desktop/Coding/IOSReader/IOSReader -Xcc -ivfsstatcache -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphonesimulator18.4-22E235-71825a6d136aa4733edcbe86ece5525b.sdkstatcache -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/swift-overrides.hmap -Xcc -iquote -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/IOSReader-generated-files.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/IOSReader-own-target-headers.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/IOSReader-all-non-framework-target-headers.hmap -Xcc -ivfsoverlay -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader-a3beb3f0916461e73b6d69f5c8becc9c-VFS-iphonesimulator/all-product-headers.yaml -Xcc -iquote -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/IOSReader-project-headers.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Products/Debug-iphonesimulator/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/DerivedSources-normal/arm64 -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/DerivedSources/arm64 -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/DerivedSources -Xcc -DDEBUG\=1 -module-name IOSReader -frontend-parseable-output -disable-clang-spi -target-sdk-version 18.4 -target-sdk-name iphonesimulator18.4 -external-plugin-path /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/lib/swift/host/plugins\#/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/bin/swift-plugin-server -external-plugin-path /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/local/lib/swift/host/plugins\#/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/bin/swift-plugin-server -in-process-plugin-server-path /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/host/libSwiftInProcPluginServer.dylib -plugin-path /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/host/plugins -plugin-path /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/local/lib/swift/host/plugins -o /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/Objects-normal/arm64/BookShelfView.o -index-unit-output-path /IOSReader.build/Debug-iphonesimulator/IOSReader.build/Objects-normal/arm64/BookShelfView.o -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Index.noindex/DataStore -index-system-modules
    
    
    Compile BookShelfView.swift (arm64):
    /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/BookShelfView.swift:211:27: error: sending value of non-Sendable type '() async -> ()' risks causing data races
                        group.addTask { @MainActor in
                        ~~~~~~^~~~~~~~~~~~~~~~~~~~~~~
    /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/BookShelfView.swift:211:27: note: Passing main actor-isolated value of non-Sendable type '() async -> ()' as a 'sending' parameter risks causing races inbetween main actor-isolated uses and uses reachable from the callee
                        group.addTask { @MainActor in
                              ^
    
    
    Compiling SourceManagerView.swift:
    Failed frontend command:
    /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swift-frontend -frontend -c /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/SharedViewStyles.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/AccountManagementView.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/settingsview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Models/Book.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/UnifiedReaderView.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/chapterlistview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/ReaderTypes.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Managers/booksourcemanager.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Parsers/epubparser.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/LoginView.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Utils/imagecache.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/BookShelfView.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Item.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/ReaderView.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Extensions/stringextensions.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Services/contentfetcher.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/backupview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/pdfkitpageview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/templateeditorview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Extensions/colorextensions.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/BookSourceSelectorView.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Models/BookSource.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Services/booksourcefetcher.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/ViewModels/ReaderViewModel.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/AdvancedFeatures.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/dictionaryview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Models/User.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/ReadingRecord.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Managers/AccountManager.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/replacementview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Core/readerrenderer.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Utils/xmlhandlers.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/txtruleview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/BookStoreView.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Coordinators/ReaderCoordinator.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Managers/backupmanager.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/RegisterView.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Parsers/documentparser.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Utils/htmlparserutils.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/bookmarkview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/readersettingsview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Components/ReaderToolbars.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/themesettingsview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Extensions/ImageExtensions.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Models/ReadingProgress.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Services/chapterstorage.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/AddBookView.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/multipleselectionrow.swift -primary-file /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/SourceManagerView.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Extensions/arrayextensions.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Models/Bookmark.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/ViewModels/BookListViewModel.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/PasswordResetView.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/webserviceview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/contentview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/ReadingMode.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/readingrecordview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Core/readercore.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Managers/memorymanager.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Parsers/htmlparser.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Utils/stringbuilder.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/BookSourceView.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/readingcontentstack.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Parsers/chapterparser.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/BookDetailView.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/IOSReaderApp.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/readercontentview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Components/SharedReaderComponents.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/thememodeview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Extensions/htmlparserutils+extension.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Models/Chapter.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Services/bookstorage.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/filemanagerview.swift /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/DerivedSources/GeneratedAssetSymbols.swift -emit-dependencies-path /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/Objects-normal/arm64/SourceManagerView.d -emit-const-values-path /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/Objects-normal/arm64/SourceManagerView.swiftconstvalues -emit-reference-dependencies-path /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/Objects-normal/arm64/SourceManagerView.swiftdeps -serialize-diagnostics-path /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/Objects-normal/arm64/SourceManagerView.dia -emit-localized-strings -emit-localized-strings-path /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/Objects-normal/arm64 -target arm64-apple-ios18.2-simulator -Xllvm -aarch64-use-tbi -enable-objc-interop -sdk /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk -I /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Products/Debug-iphonesimulator -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Products/Debug-iphonesimulator -no-color-diagnostics -enable-testing -g -debug-info-format\=dwarf -dwarf-version\=5 -module-cache-path /Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex -profile-generate -profile-coverage-mapping -swift-version 6 -enforce-exclusivity\=checked -Onone -D DEBUG -serialize-debugging-options -const-gather-protocols-file /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/Objects-normal/arm64/IOSReader_const_extract_protocols.json -enable-experimental-feature DebugDescriptionMacro -empty-abi-descriptor -validate-clang-modules-once -clang-build-session-file /Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation -Xcc -working-directory -Xcc /Users/<USER>/Desktop/Coding/IOSReader/IOSReader -resource-dir /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift -enable-anonymous-context-mangled-names -file-compilation-dir /Users/<USER>/Desktop/Coding/IOSReader/IOSReader -Xcc -ivfsstatcache -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphonesimulator18.4-22E235-71825a6d136aa4733edcbe86ece5525b.sdkstatcache -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/swift-overrides.hmap -Xcc -iquote -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/IOSReader-generated-files.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/IOSReader-own-target-headers.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/IOSReader-all-non-framework-target-headers.hmap -Xcc -ivfsoverlay -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader-a3beb3f0916461e73b6d69f5c8becc9c-VFS-iphonesimulator/all-product-headers.yaml -Xcc -iquote -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/IOSReader-project-headers.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Products/Debug-iphonesimulator/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/DerivedSources-normal/arm64 -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/DerivedSources/arm64 -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/DerivedSources -Xcc -DDEBUG\=1 -module-name IOSReader -frontend-parseable-output -disable-clang-spi -target-sdk-version 18.4 -target-sdk-name iphonesimulator18.4 -external-plugin-path /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/lib/swift/host/plugins\#/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/bin/swift-plugin-server -external-plugin-path /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/local/lib/swift/host/plugins\#/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/bin/swift-plugin-server -in-process-plugin-server-path /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/host/libSwiftInProcPluginServer.dylib -plugin-path /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/host/plugins -plugin-path /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/local/lib/swift/host/plugins -o /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/Objects-normal/arm64/SourceManagerView.o -index-unit-output-path /IOSReader.build/Debug-iphonesimulator/IOSReader.build/Objects-normal/arm64/SourceManagerView.o -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Index.noindex/DataStore -index-system-modules
    
    
    Compile SourceManagerView.swift (arm64):
    /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/SourceManagerView.swift:713:14: error: ambiguous use of 'toolbar(content:)'
                .toolbar {
                 ^
    SwiftUI.View.toolbar:2:25: note: found this candidate in module 'SwiftUI'
    nonisolated public func toolbar<Content>(@ViewBuilder content: () -> Content) -> some View where Content : View
                            ^
    SwiftUI.View.toolbar:2:25: note: found this candidate in module 'SwiftUI'
    nonisolated public func toolbar<Content>(@ToolbarContentBuilder content: () -> Content) -> some View where Content : ToolbarContent
                            ^
    



== PREVIEW UPDATE ERROR:

    SchemeBuildError: Failed to build the scheme “IOSReader”
    
    sending value of non-Sendable type '() async -> ()' risks causing data races
    
    Build target IOSReader:
    note: Disabling hardened runtime with ad-hoc codesigning. (in target 'IOSReader' from project 'IOSReader')
    
    
    Compiling BookShelfView.swift:
    Failed frontend command:
    /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swift-frontend -frontend -c /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/SharedViewStyles.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/AccountManagementView.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/settingsview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Models/Book.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/UnifiedReaderView.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/chapterlistview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/ReaderTypes.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Managers/booksourcemanager.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Parsers/epubparser.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/LoginView.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Utils/imagecache.swift -primary-file /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/BookShelfView.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Item.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/ReaderView.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Extensions/stringextensions.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Services/contentfetcher.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/backupview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/pdfkitpageview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/templateeditorview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Extensions/colorextensions.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/BookSourceSelectorView.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Models/BookSource.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Services/booksourcefetcher.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/ViewModels/ReaderViewModel.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/AdvancedFeatures.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/dictionaryview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Models/User.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/ReadingRecord.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Managers/AccountManager.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/replacementview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Core/readerrenderer.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Utils/xmlhandlers.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/txtruleview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/BookStoreView.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Coordinators/ReaderCoordinator.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Managers/backupmanager.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/RegisterView.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Parsers/documentparser.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Utils/htmlparserutils.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/bookmarkview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/readersettingsview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Components/ReaderToolbars.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/themesettingsview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Extensions/ImageExtensions.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Models/ReadingProgress.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Services/chapterstorage.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/AddBookView.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/multipleselectionrow.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/SourceManagerView.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Extensions/arrayextensions.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Models/Bookmark.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/ViewModels/BookListViewModel.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/PasswordResetView.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/webserviceview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/contentview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/ReadingMode.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/readingrecordview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Core/readercore.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Managers/memorymanager.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Parsers/htmlparser.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Utils/stringbuilder.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/BookSourceView.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/readingcontentstack.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Parsers/chapterparser.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/BookDetailView.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/IOSReaderApp.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/readercontentview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Components/SharedReaderComponents.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/thememodeview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Extensions/htmlparserutils+extension.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Models/Chapter.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Services/bookstorage.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/filemanagerview.swift /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/DerivedSources/GeneratedAssetSymbols.swift -emit-dependencies-path /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/Objects-normal/arm64/BookShelfView.d -emit-const-values-path /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/Objects-normal/arm64/BookShelfView.swiftconstvalues -emit-reference-dependencies-path /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/Objects-normal/arm64/BookShelfView.swiftdeps -serialize-diagnostics-path /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/Objects-normal/arm64/BookShelfView.dia -emit-localized-strings -emit-localized-strings-path /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/Objects-normal/arm64 -target arm64-apple-ios18.2-simulator -Xllvm -aarch64-use-tbi -enable-objc-interop -sdk /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk -I /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Products/Debug-iphonesimulator -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Products/Debug-iphonesimulator -no-color-diagnostics -enable-testing -g -debug-info-format\=dwarf -dwarf-version\=5 -module-cache-path /Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex -profile-generate -profile-coverage-mapping -swift-version 6 -enforce-exclusivity\=checked -Onone -D DEBUG -serialize-debugging-options -const-gather-protocols-file /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/Objects-normal/arm64/IOSReader_const_extract_protocols.json -enable-experimental-feature DebugDescriptionMacro -empty-abi-descriptor -validate-clang-modules-once -clang-build-session-file /Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation -Xcc -working-directory -Xcc /Users/<USER>/Desktop/Coding/IOSReader/IOSReader -resource-dir /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift -enable-anonymous-context-mangled-names -file-compilation-dir /Users/<USER>/Desktop/Coding/IOSReader/IOSReader -Xcc -ivfsstatcache -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphonesimulator18.4-22E235-71825a6d136aa4733edcbe86ece5525b.sdkstatcache -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/swift-overrides.hmap -Xcc -iquote -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/IOSReader-generated-files.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/IOSReader-own-target-headers.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/IOSReader-all-non-framework-target-headers.hmap -Xcc -ivfsoverlay -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader-a3beb3f0916461e73b6d69f5c8becc9c-VFS-iphonesimulator/all-product-headers.yaml -Xcc -iquote -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/IOSReader-project-headers.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Products/Debug-iphonesimulator/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/DerivedSources-normal/arm64 -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/DerivedSources/arm64 -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/DerivedSources -Xcc -DDEBUG\=1 -module-name IOSReader -frontend-parseable-output -disable-clang-spi -target-sdk-version 18.4 -target-sdk-name iphonesimulator18.4 -external-plugin-path /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/lib/swift/host/plugins\#/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/bin/swift-plugin-server -external-plugin-path /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/local/lib/swift/host/plugins\#/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/bin/swift-plugin-server -in-process-plugin-server-path /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/host/libSwiftInProcPluginServer.dylib -plugin-path /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/host/plugins -plugin-path /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/local/lib/swift/host/plugins -o /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/Objects-normal/arm64/BookShelfView.o -index-unit-output-path /IOSReader.build/Debug-iphonesimulator/IOSReader.build/Objects-normal/arm64/BookShelfView.o -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Index.noindex/DataStore -index-system-modules
    
    
    Compile BookShelfView.swift (arm64):
    /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/BookShelfView.swift:211:27: error: sending value of non-Sendable type '() async -> ()' risks causing data races
                        group.addTask { @MainActor in
                        ~~~~~~^~~~~~~~~~~~~~~~~~~~~~~
    /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/BookShelfView.swift:211:27: note: Passing main actor-isolated value of non-Sendable type '() async -> ()' as a 'sending' parameter risks causing races inbetween main actor-isolated uses and uses reachable from the callee
                        group.addTask { @MainActor in
                              ^
    
    
    Compiling SourceManagerView.swift:
    Failed frontend command:
    /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swift-frontend -frontend -c /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/SharedViewStyles.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/AccountManagementView.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/settingsview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Models/Book.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/UnifiedReaderView.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/chapterlistview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/ReaderTypes.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Managers/booksourcemanager.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Parsers/epubparser.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/LoginView.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Utils/imagecache.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/BookShelfView.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Item.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/ReaderView.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Extensions/stringextensions.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Services/contentfetcher.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/backupview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/pdfkitpageview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/templateeditorview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Extensions/colorextensions.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/BookSourceSelectorView.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Models/BookSource.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Services/booksourcefetcher.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/ViewModels/ReaderViewModel.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/AdvancedFeatures.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/dictionaryview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Models/User.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/ReadingRecord.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Managers/AccountManager.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/replacementview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Core/readerrenderer.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Utils/xmlhandlers.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/txtruleview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/BookStoreView.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Coordinators/ReaderCoordinator.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Managers/backupmanager.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/RegisterView.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Parsers/documentparser.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Utils/htmlparserutils.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/bookmarkview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/readersettingsview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Components/ReaderToolbars.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/themesettingsview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Extensions/ImageExtensions.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Models/ReadingProgress.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Services/chapterstorage.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/AddBookView.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/multipleselectionrow.swift -primary-file /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/SourceManagerView.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Extensions/arrayextensions.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Models/Bookmark.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/ViewModels/BookListViewModel.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/PasswordResetView.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/webserviceview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/contentview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/ReadingMode.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/readingrecordview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Core/readercore.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Managers/memorymanager.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Parsers/htmlparser.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Utils/stringbuilder.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/BookSourceView.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/readingcontentstack.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Parsers/chapterparser.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/BookDetailView.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/IOSReaderApp.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/readercontentview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Components/SharedReaderComponents.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/thememodeview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Extensions/htmlparserutils+extension.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Models/Chapter.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Services/bookstorage.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/filemanagerview.swift /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/DerivedSources/GeneratedAssetSymbols.swift -emit-dependencies-path /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/Objects-normal/arm64/SourceManagerView.d -emit-const-values-path /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/Objects-normal/arm64/SourceManagerView.swiftconstvalues -emit-reference-dependencies-path /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/Objects-normal/arm64/SourceManagerView.swiftdeps -serialize-diagnostics-path /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/Objects-normal/arm64/SourceManagerView.dia -emit-localized-strings -emit-localized-strings-path /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/Objects-normal/arm64 -target arm64-apple-ios18.2-simulator -Xllvm -aarch64-use-tbi -enable-objc-interop -sdk /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk -I /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Products/Debug-iphonesimulator -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Products/Debug-iphonesimulator -no-color-diagnostics -enable-testing -g -debug-info-format\=dwarf -dwarf-version\=5 -module-cache-path /Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex -profile-generate -profile-coverage-mapping -swift-version 6 -enforce-exclusivity\=checked -Onone -D DEBUG -serialize-debugging-options -const-gather-protocols-file /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/Objects-normal/arm64/IOSReader_const_extract_protocols.json -enable-experimental-feature DebugDescriptionMacro -empty-abi-descriptor -validate-clang-modules-once -clang-build-session-file /Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation -Xcc -working-directory -Xcc /Users/<USER>/Desktop/Coding/IOSReader/IOSReader -resource-dir /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift -enable-anonymous-context-mangled-names -file-compilation-dir /Users/<USER>/Desktop/Coding/IOSReader/IOSReader -Xcc -ivfsstatcache -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphonesimulator18.4-22E235-71825a6d136aa4733edcbe86ece5525b.sdkstatcache -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/swift-overrides.hmap -Xcc -iquote -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/IOSReader-generated-files.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/IOSReader-own-target-headers.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/IOSReader-all-non-framework-target-headers.hmap -Xcc -ivfsoverlay -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader-a3beb3f0916461e73b6d69f5c8becc9c-VFS-iphonesimulator/all-product-headers.yaml -Xcc -iquote -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/IOSReader-project-headers.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Products/Debug-iphonesimulator/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/DerivedSources-normal/arm64 -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/DerivedSources/arm64 -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/DerivedSources -Xcc -DDEBUG\=1 -module-name IOSReader -frontend-parseable-output -disable-clang-spi -target-sdk-version 18.4 -target-sdk-name iphonesimulator18.4 -external-plugin-path /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/lib/swift/host/plugins\#/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/bin/swift-plugin-server -external-plugin-path /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/local/lib/swift/host/plugins\#/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/bin/swift-plugin-server -in-process-plugin-server-path /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/host/libSwiftInProcPluginServer.dylib -plugin-path /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/host/plugins -plugin-path /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/local/lib/swift/host/plugins -o /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/Objects-normal/arm64/SourceManagerView.o -index-unit-output-path /IOSReader.build/Debug-iphonesimulator/IOSReader.build/Objects-normal/arm64/SourceManagerView.o -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Index.noindex/DataStore -index-system-modules
    
    
    Compile SourceManagerView.swift (arm64):
    /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/SourceManagerView.swift:713:14: error: ambiguous use of 'toolbar(content:)'
                .toolbar {
                 ^
    SwiftUI.View.toolbar:2:25: note: found this candidate in module 'SwiftUI'
    nonisolated public func toolbar<Content>(@ViewBuilder content: () -> Content) -> some View where Content : View
                            ^
    SwiftUI.View.toolbar:2:25: note: found this candidate in module 'SwiftUI'
    nonisolated public func toolbar<Content>(@ToolbarContentBuilder content: () -> Content) -> some View where Content : ToolbarContent
                            ^
    



== VERSION INFO:

    Tools: 16E140
    OS:    24F74
    PID:   987
    Model: MacBook Pro
    Arch:  arm64e



== EXECUTION MODE PROPERTIES:

    Automatically Refresh Previews: true
    JIT Mode User Enabled: true
    Falling back to Dynamic Replacement: false



== ENVIRONMENT:

    openFiles = [
        /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/thememodeview.swift
    ]
    wantsNewBuildSystem = true
    newBuildSystemAvailable = true
    activeScheme = IOSReader
    activeRunDestination = iPhone 16 Pro variant iphonesimulator arm64
    workspaceArena = [x]
    buildArena = [x]
    buildableEntries = [
        IOSReader.app
    ]
    runMode = JIT Executor



== SELECTED RUN DESTINATION:

    Simulator - iOS 18.4 | iphonesimulator | arm64 | iPhone 16 Pro | no proxy



== PACKAGE RESOLUTION ERRORS:

    



== REFERENCED SOURCE PACKAGES:

    <IDESwiftPackageCore.IDESwiftPackageDependency:0x349639d70 path:'/Users/<USER>/Downloads/ZIPFoundation-0.9.19'>



== JIT LINKAGE:

    



== SESSION GROUP 8825:

    workspace identifier: WorkspaceIdentifier(identifier: 400FBB16-747E-4A12-9FA7-862D2993103C)
    providers: [
        Preview Provider | Registry-thememodeview.swift#1[preview] [Editor(8788)]
    ]
    translation units: [
        /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/thememodeview.swift
    ]
    attributes: [
        Editor(8788):     []
    ]
    session: 8826
    request sessions: [
        Registry[Registry-thememodeview.swift#1[preview] (line 22)]: not completed
    ]



== UPDATE SESSION 8826:

    Start Date: Wednesday, May 28, 2025 at 14:50:35 China Standard Time
    Timing {
        Elapsed Time: 2.3971880674362183s
        39e9265b1ae68b1c,770107835.753768,2.3971880674362183s,,PreviewUpdateSession,id:8826
        f950efc3b481dea,770107835.824664,0.0005700588226318359s,,MakeBuildGraph,
        11b80355246741c5,770107835.827071,2.3230479955673218s,,WorkspaceBuild,
    }
    Preview Preflight {
        UpdaterStore {
            updaterLimit: single
            expectedAbandonedIdentifiers: [4882, 4816]
        }
        Simulator {
            platform: iphonesimulator
            device: 9D65155D-655C-4E8E-91EF-CED7602892C2 iPhone 16 Pro
            buildNumber: 22E238
            runtimePath: /Library/Developer/CoreSimulator/Volumes/iOS_22E238/Library/Developer/CoreSimulator/Profiles/Runtimes/iOS 18.4.simruntime
        }
    }
    Preview Provider {
        UpdaterStore {
            updaterLimit: single
            expectedAbandonedIdentifiers: [4882, 4816]
        }
        Simulator {
            platform: iphonesimulator
            device: 9D65155D-655C-4E8E-91EF-CED7602892C2 iPhone 16 Pro
            buildNumber: 22E238
            runtimePath: /Library/Developer/CoreSimulator/Volumes/iOS_22E238/Library/Developer/CoreSimulator/Profiles/Runtimes/iOS 18.4.simruntime
        }
    }
    Build Graph {
        IOSReader.app (#5)
           ZIPFoundation (#2)
              ZIPFoundation (#1)
           sourceFile(file:///Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/thememodeview.swift -> thememodeview.swift) (#3)
           thememodeview.swift (#4)
    }
    Update Plan {
        iOS [arm64 iphonesimulator18.4 iphonesimulator] (iPhone 16 Pro, 2B0FFA9A-AE1F-4DE6-9CA1-8232400B407D-iphonesimulator18.4-arm64-iphonesimulator), [], thinning disabled, thunking enabled) {
            Destination: iPhone 16 Pro 2B0FFA9A-AE1F-4DE6-9CA1-8232400B407D | default device for iphonesimulator [
                IOSReader app - Previews {
                    execution point packs [
                        [source: thememodeview.swift, role: Previews] (in IOSReader)
                    ]
                    translation units [
                        thememodeview.swift (in IOSReader.app)
                    ]
                    modules [
                        IOSReader.app
                        ZIPFoundation
                        ZIPFoundation
                    ]
                    jit link description [
                        IOSReader.app {
                            merged static libs [
                                ZIPFoundation
                                ZIPFoundation
                            ]
                        }
                    ]
                }
            ]
        }
    }



== BUILD PRODUCTS CACHE:

    BuildCache {
        Translation Units [
            file:///Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/thememodeview.swift -> thememodeview.swift {
                workspace: iOS [arm64 iphonesimulator18.4 iphonesimulator] (iPhone 16 Pro, 2B0FFA9A-AE1F-4DE6-9CA1-8232400B407D-iphonesimulator18.4-arm64-iphonesimulator), [], thinning disabled, thunking enabled)
                Previews_thememodeview
            }
        ]
    }



== POWER STATE LOGS:

    2025/5/28, 14:31 Received power source state: Battery Powered (lowPowerMode: false, status: charged, level: 100%)
    2025/5/28, 14:31 No device power state user override user default value.Current power state: Full Power


