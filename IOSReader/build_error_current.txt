Command line invocation:
    /Applications/Xcode.app/Contents/Developer/usr/bin/xcodebuild -project IOSReader.xcodeproj -scheme IOSReader -destination "platform=iOS Simulator,name=iPhone 16 Pro,OS=18.3.1" build

Resolve Package Graph


Resolved source packages:
  ZIPFoundation: /Users/<USER>/Downloads/ZIPFoundation-0.9.19 @ local

--- xcodebuild: WARNING: Using the first of multiple matching destinations:
{ platform:iOS Simulator, arch:arm64, id:533F4D43-1B7B-4E29-A80B-58A33A21B3EF, OS:18.3.1, name:iPhone 16 Pro }
{ platform:iOS Simulator, arch:x86_64, id:533F4D43-1B7B-4E29-A80B-58A33A21B3EF, OS:18.3.1, name:iPhone 16 Pro }
ComputePackagePrebuildTargetDependencyGraph

Prepare packages

CreateBuildRequest

SendProjectDescription

CreateBuildOperation

ComputeTargetDependencyGraph
note: Building targets in dependency order
note: Target dependency graph (4 targets)
    Target 'IOSReader' in project 'IOSReader'
        ➜ Explicit dependency on target 'ZIPFoundation' in project 'ZIPFoundation'
    Target 'ZIPFoundation' in project 'ZIPFoundation'
        ➜ Explicit dependency on target 'ZIPFoundation' in project 'ZIPFoundation'
        ➜ Explicit dependency on target 'ZIPFoundation_ZIPFoundation' in project 'ZIPFoundation'
    Target 'ZIPFoundation' in project 'ZIPFoundation'
        ➜ Explicit dependency on target 'ZIPFoundation_ZIPFoundation' in project 'ZIPFoundation'
    Target 'ZIPFoundation_ZIPFoundation' in project 'ZIPFoundation' (no dependencies)

GatherProvisioningInputs

CreateBuildDescription

ExecuteExternalTool /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc --version

ExecuteExternalTool /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang -v -E -dM -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk -x c -c /dev/null

ExecuteExternalTool /Applications/Xcode.app/Contents/Developer/usr/bin/ibtool --version --output-format xml1

ExecuteExternalTool /Applications/Xcode.app/Contents/Developer/usr/bin/actool --print-asset-tag-combinations --output-format xml1 /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Assets.xcassets /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Preview Content/Preview Assets.xcassets

ExecuteExternalTool /Applications/Xcode.app/Contents/Developer/usr/bin/actool --version --output-format xml1

ExecuteExternalTool /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/ld -version_details

Build description signature: 52082495385d2a802c7aef2022d56e00
Build description path: /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/XCBuildData/52082495385d2a802c7aef2022d56e00.xcbuilddata
ClangStatCache /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphonesimulator18.4-22E235-71825a6d136aa4733edcbe86ece5525b.sdkstatcache
    cd /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader.xcodeproj
    /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang-stat-cache /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk -o /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphonesimulator18.4-22E235-71825a6d136aa4733edcbe86ece5525b.sdkstatcache

WriteAuxiliaryFile /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/Objects-normal/arm64/IOSReader.SwiftConstValuesFileList (in target 'IOSReader' from project 'IOSReader')
    cd /Users/<USER>/Desktop/Coding/IOSReader/IOSReader
    write-file /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/Objects-normal/arm64/IOSReader.SwiftConstValuesFileList

WriteAuxiliaryFile /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/Objects-normal/arm64/IOSReader.SwiftFileList (in target 'IOSReader' from project 'IOSReader')
    cd /Users/<USER>/Desktop/Coding/IOSReader/IOSReader
    write-file /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/Objects-normal/arm64/IOSReader.SwiftFileList

WriteAuxiliaryFile /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/Objects-normal/arm64/IOSReader.LinkFileList (in target 'IOSReader' from project 'IOSReader')
    cd /Users/<USER>/Desktop/Coding/IOSReader/IOSReader
    write-file /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/Objects-normal/arm64/IOSReader.LinkFileList

ProcessInfoPlistFile /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Products/Debug-iphonesimulator/ZIPFoundation_ZIPFoundation.bundle/Info.plist /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/ZIPFoundation.build/Debug-iphonesimulator/ZIPFoundation_ZIPFoundation.build/empty-ZIPFoundation_ZIPFoundation.plist (in target 'ZIPFoundation_ZIPFoundation' from project 'ZIPFoundation')
    cd /Users/<USER>/Downloads/ZIPFoundation-0.9.19
    builtin-infoPlistUtility /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/ZIPFoundation.build/Debug-iphonesimulator/ZIPFoundation_ZIPFoundation.build/empty-ZIPFoundation_ZIPFoundation.plist -producttype com.apple.product-type.bundle -expandbuildsettings -format binary -platform iphonesimulator -o /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Products/Debug-iphonesimulator/ZIPFoundation_ZIPFoundation.bundle/Info.plist

ProcessProductPackaging /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/IOSReader.entitlements /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/IOSReader.app.xcent (in target 'IOSReader' from project 'IOSReader')
    cd /Users/<USER>/Desktop/Coding/IOSReader/IOSReader
    
    Entitlements:
    
    {
}
    
    builtin-productPackagingUtility /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/IOSReader.entitlements -entitlements -format xml -o /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/IOSReader.app.xcent

ProcessProductPackaging /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/IOSReader.entitlements /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/IOSReader.app-Simulated.xcent (in target 'IOSReader' from project 'IOSReader')
    cd /Users/<USER>/Desktop/Coding/IOSReader/IOSReader
    
    Entitlements:
    
    {
    "application-identifier" = "L5ZCXC64Q2.none.IOSReader";
}
    
    builtin-productPackagingUtility /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/IOSReader.entitlements -entitlements -format xml -o /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/IOSReader.app-Simulated.xcent

ProcessProductPackagingDER /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/IOSReader.app.xcent /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/IOSReader.app.xcent.der (in target 'IOSReader' from project 'IOSReader')
    cd /Users/<USER>/Desktop/Coding/IOSReader/IOSReader
    /usr/bin/derq query -f xml -i /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/IOSReader.app.xcent -o /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/IOSReader.app.xcent.der --raw

ProcessProductPackagingDER /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/IOSReader.app-Simulated.xcent /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/IOSReader.app-Simulated.xcent.der (in target 'IOSReader' from project 'IOSReader')
    cd /Users/<USER>/Desktop/Coding/IOSReader/IOSReader
    /usr/bin/derq query -f xml -i /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/IOSReader.app-Simulated.xcent -o /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/IOSReader.app-Simulated.xcent.der --raw

GenerateAssetSymbols /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Assets.xcassets /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Preview\ Content/Preview\ Assets.xcassets (in target 'IOSReader' from project 'IOSReader')
    cd /Users/<USER>/Desktop/Coding/IOSReader/IOSReader
    /Applications/Xcode.app/Contents/Developer/usr/bin/actool /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Assets.xcassets /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Preview\ Content/Preview\ Assets.xcassets --compile /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Products/Debug-iphonesimulator/IOSReader.app --output-format human-readable-text --notices --warnings --export-dependency-info /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/assetcatalog_dependencies --output-partial-info-plist /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/assetcatalog_generated_info.plist --app-icon AppIcon --accent-color AccentColor --compress-pngs --enable-on-demand-resources YES --development-region zh-Hans --target-device iphone --target-device ipad --minimum-deployment-target 18.2 --platform iphonesimulator --bundle-identifier none.IOSReader --generate-swift-asset-symbols /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/DerivedSources/GeneratedAssetSymbols.swift --generate-objc-asset-symbols /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/DerivedSources/GeneratedAssetSymbols.h --generate-asset-symbol-index /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/DerivedSources/GeneratedAssetSymbols-Index.plist
/* com.apple.actool.compilation-results */
/Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/DerivedSources/GeneratedAssetSymbols-Index.plist
/Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/DerivedSources/GeneratedAssetSymbols.h
/Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/DerivedSources/GeneratedAssetSymbols.swift


CompileAssetCatalogVariant thinned /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Products/Debug-iphonesimulator/IOSReader.app /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Assets.xcassets /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Preview\ Content/Preview\ Assets.xcassets (in target 'IOSReader' from project 'IOSReader')
    cd /Users/<USER>/Desktop/Coding/IOSReader/IOSReader
    /Applications/Xcode.app/Contents/Developer/usr/bin/actool /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Assets.xcassets /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Preview\ Content/Preview\ Assets.xcassets --compile /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/assetcatalog_output/thinned --output-format human-readable-text --notices --warnings --export-dependency-info /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/assetcatalog_dependencies_thinned --output-partial-info-plist /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/assetcatalog_generated_info.plist_thinned --app-icon AppIcon --accent-color AccentColor --compress-pngs --enable-on-demand-resources YES --filter-for-thinning-device-configuration iPhone17,1 --filter-for-device-os-version 18.3.1 --development-region zh-Hans --target-device iphone --target-device ipad --minimum-deployment-target 18.2 --platform iphonesimulator

SwiftDriver IOSReader normal arm64 com.apple.xcode.tools.swift.compiler (in target 'IOSReader' from project 'IOSReader')
    cd /Users/<USER>/Desktop/Coding/IOSReader/IOSReader
    builtin-SwiftDriver -- /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc -module-name IOSReader -Onone -enforce-exclusivity\=checked @/Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/Objects-normal/arm64/IOSReader.SwiftFileList -DDEBUG -enable-experimental-feature DebugDescriptionMacro -sdk /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk -target arm64-apple-ios18.2-simulator -g -module-cache-path /Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex -Xfrontend -serialize-debugging-options -profile-coverage-mapping -profile-generate -enable-testing -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Index.noindex/DataStore -swift-version 6 -I /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Products/Debug-iphonesimulator -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Products/Debug-iphonesimulator -emit-localized-strings -emit-localized-strings-path /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/Objects-normal/arm64 -c -j10 -disable-batch-mode -incremental -Xcc -ivfsstatcache -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphonesimulator18.4-22E235-71825a6d136aa4733edcbe86ece5525b.sdkstatcache -output-file-map /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/Objects-normal/arm64/IOSReader-OutputFileMap.json -use-frontend-parseable-output -save-temps -no-color-diagnostics -serialize-diagnostics -emit-dependencies -emit-module -emit-module-path /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/Objects-normal/arm64/IOSReader.swiftmodule -validate-clang-modules-once -clang-build-session-file /Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/swift-overrides.hmap -emit-const-values -Xfrontend -const-gather-protocols-file -Xfrontend /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/Objects-normal/arm64/IOSReader_const_extract_protocols.json -Xcc -iquote -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/IOSReader-generated-files.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/IOSReader-own-target-headers.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/IOSReader-all-non-framework-target-headers.hmap -Xcc -ivfsoverlay -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader-a3beb3f0916461e73b6d69f5c8becc9c-VFS-iphonesimulator/all-product-headers.yaml -Xcc -iquote -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/IOSReader-project-headers.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Products/Debug-iphonesimulator/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/DerivedSources-normal/arm64 -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/DerivedSources/arm64 -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/DerivedSources -Xcc -DDEBUG\=1 -emit-objc-header -emit-objc-header-path /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/Objects-normal/arm64/IOSReader-Swift.h -working-directory /Users/<USER>/Desktop/Coding/IOSReader/IOSReader -experimental-emit-module-separately -disable-cmo

SwiftCompile normal arm64 Compiling\ pdfkitpageview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/pdfkitpageview.swift (in target 'IOSReader' from project 'IOSReader')

SwiftCompile normal arm64 /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/pdfkitpageview.swift (in target 'IOSReader' from project 'IOSReader')
    cd /Users/<USER>/Desktop/Coding/IOSReader/IOSReader
    

SwiftCompile normal arm64 Compiling\ stringextensions.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Extensions/stringextensions.swift (in target 'IOSReader' from project 'IOSReader')

SwiftCompile normal arm64 /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Extensions/stringextensions.swift (in target 'IOSReader' from project 'IOSReader')
    cd /Users/<USER>/Desktop/Coding/IOSReader/IOSReader
    

SwiftCompile normal arm64 Compiling\ htmlparserutils+extension.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Extensions/htmlparserutils+extension.swift (in target 'IOSReader' from project 'IOSReader')

SwiftCompile normal arm64 /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Extensions/htmlparserutils+extension.swift (in target 'IOSReader' from project 'IOSReader')
    cd /Users/<USER>/Desktop/Coding/IOSReader/IOSReader
    

SwiftCompile normal arm64 Compiling\ PasswordResetView.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/PasswordResetView.swift (in target 'IOSReader' from project 'IOSReader')
SwiftCompile normal arm64 /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/PasswordResetView.swift (in target 'IOSReader' from project 'IOSReader')
    cd /Users/<USER>/Desktop/Coding/IOSReader/IOSReader
    

SwiftCompile normal arm64 Compiling\ ReadingMode.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/ReadingMode.swift (in target 'IOSReader' from project 'IOSReader')

SwiftCompile normal arm64 /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/ReadingMode.swift (in target 'IOSReader' from project 'IOSReader')
    cd /Users/<USER>/Desktop/Coding/IOSReader/IOSReader
    

SwiftCompile normal arm64 Compiling\ chapterparser.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Parsers/chapterparser.swift (in target 'IOSReader' from project 'IOSReader')

SwiftCompile normal arm64 /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Parsers/chapterparser.swift (in target 'IOSReader' from project 'IOSReader')
    cd /Users/<USER>/Desktop/Coding/IOSReader/IOSReader
    
/Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Parsers/chapterparser.swift:80:13: warning: initialization of variable 'lastChapterEndPosition' was never used; consider replacing with assignment to '_' or removing it
        var lastChapterEndPosition = 0
        ~~~~^~~~~~~~~~~~~~~~~~~~~~
        _
/Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Parsers/chapterparser.swift:142:24: warning: value 'match' was defined but never used; consider replacing with boolean test
                if let match = regex.firstMatch(in: trimmedLine, options: [], range: range) {
                   ~~~~^~~~~~~~
                                                                                            != nil

SwiftCompile normal arm64 Compiling\ SharedViewStyles.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/SharedViewStyles.swift (in target 'IOSReader' from project 'IOSReader')

SwiftCompile normal arm64 /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/SharedViewStyles.swift (in target 'IOSReader' from project 'IOSReader')
    cd /Users/<USER>/Desktop/Coding/IOSReader/IOSReader
    

SwiftCompile normal arm64 Compiling\ multipleselectionrow.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/multipleselectionrow.swift (in target 'IOSReader' from project 'IOSReader')

SwiftCompile normal arm64 /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/multipleselectionrow.swift (in target 'IOSReader' from project 'IOSReader')
    cd /Users/<USER>/Desktop/Coding/IOSReader/IOSReader
    

SwiftCompile normal arm64 Compiling\ SharedReaderComponents.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Components/SharedReaderComponents.swift (in target 'IOSReader' from project 'IOSReader')

SwiftCompile normal arm64 /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Components/SharedReaderComponents.swift (in target 'IOSReader' from project 'IOSReader')
    cd /Users/<USER>/Desktop/Coding/IOSReader/IOSReader
    

SwiftEmitModule normal arm64 Emitting\ module\ for\ IOSReader (in target 'IOSReader' from project 'IOSReader')
EmitSwiftModule normal arm64 (in target 'IOSReader' from project 'IOSReader')
    cd /Users/<USER>/Desktop/Coding/IOSReader/IOSReader
    

SwiftCompile normal arm64 Compiling\ BookSourceSelectorView.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/BookSourceSelectorView.swift (in target 'IOSReader' from project 'IOSReader')

SwiftCompile normal arm64 /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/BookSourceSelectorView.swift (in target 'IOSReader' from project 'IOSReader')
    cd /Users/<USER>/Desktop/Coding/IOSReader/IOSReader
    

SwiftDriverJobDiscovery normal arm64 Compiling multipleselectionrow.swift (in target 'IOSReader' from project 'IOSReader')

SwiftDriverJobDiscovery normal arm64 Compiling pdfkitpageview.swift (in target 'IOSReader' from project 'IOSReader')

SwiftCompile normal arm64 Compiling\ readercontentview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/readercontentview.swift (in target 'IOSReader' from project 'IOSReader')

SwiftCompile normal arm64 /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/readercontentview.swift (in target 'IOSReader' from project 'IOSReader')
    cd /Users/<USER>/Desktop/Coding/IOSReader/IOSReader
    

SwiftDriverJobDiscovery normal arm64 Compiling stringextensions.swift (in target 'IOSReader' from project 'IOSReader')

SwiftCompile normal arm64 Compiling\ BookSourceView.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/BookSourceView.swift (in target 'IOSReader' from project 'IOSReader')

SwiftCompile normal arm64 /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/BookSourceView.swift (in target 'IOSReader' from project 'IOSReader')
    cd /Users/<USER>/Desktop/Coding/IOSReader/IOSReader
    

SwiftDriverJobDiscovery normal arm64 Compiling ReadingMode.swift (in target 'IOSReader' from project 'IOSReader')

SwiftCompile normal arm64 Compiling\ IOSReaderApp.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/IOSReaderApp.swift (in target 'IOSReader' from project 'IOSReader')

SwiftCompile normal arm64 /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/IOSReaderApp.swift (in target 'IOSReader' from project 'IOSReader')
    cd /Users/<USER>/Desktop/Coding/IOSReader/IOSReader
    

SwiftDriverJobDiscovery normal arm64 Compiling htmlparserutils+extension.swift (in target 'IOSReader' from project 'IOSReader')

SwiftCompile normal arm64 Compiling\ bookmarkview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/bookmarkview.swift (in target 'IOSReader' from project 'IOSReader')

SwiftCompile normal arm64 /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/bookmarkview.swift (in target 'IOSReader' from project 'IOSReader')
    cd /Users/<USER>/Desktop/Coding/IOSReader/IOSReader
    
/Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/bookmarkview.swift:179:19: warning: value 'book' was defined but never used; consider replacing with boolean test
        guard let book = coordinator.currentBook else { return }
              ~~~~^~~~~~~
                                                 != nil

SwiftDriverJobDiscovery normal arm64 Compiling SharedViewStyles.swift (in target 'IOSReader' from project 'IOSReader')

SwiftCompile normal arm64 Compiling\ backupview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/backupview.swift (in target 'IOSReader' from project 'IOSReader')

SwiftCompile normal arm64 /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/backupview.swift (in target 'IOSReader' from project 'IOSReader')
    cd /Users/<USER>/Desktop/Coding/IOSReader/IOSReader
    

SwiftDriverJobDiscovery normal arm64 Compiling chapterparser.swift (in target 'IOSReader' from project 'IOSReader')

SwiftCompile normal arm64 Compiling\ settingsview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/settingsview.swift (in target 'IOSReader' from project 'IOSReader')
SwiftCompile normal arm64 /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/settingsview.swift (in target 'IOSReader' from project 'IOSReader')
    cd /Users/<USER>/Desktop/Coding/IOSReader/IOSReader
    

SwiftDriverJobDiscovery normal arm64 Compiling SharedReaderComponents.swift (in target 'IOSReader' from project 'IOSReader')

SwiftCompile normal arm64 Compiling\ txtruleview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/txtruleview.swift (in target 'IOSReader' from project 'IOSReader')

SwiftCompile normal arm64 /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/txtruleview.swift (in target 'IOSReader' from project 'IOSReader')
    cd /Users/<USER>/Desktop/Coding/IOSReader/IOSReader
    

SwiftDriverJobDiscovery normal arm64 Compiling readercontentview.swift (in target 'IOSReader' from project 'IOSReader')

SwiftCompile normal arm64 Compiling\ ReaderView.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/ReaderView.swift (in target 'IOSReader' from project 'IOSReader')
Failed frontend command:
/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swift-frontend -frontend -c /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/ViewModels/ReaderViewModel.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Parsers/epubparser.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/AddBookView.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/pdfkitpageview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/replacementview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/thememodeview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/multipleselectionrow.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/backupview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/AccountManagementView.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Models/Book.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Services/chapterstorage.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/readersettingsview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/ViewModels/BookListViewModel.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Parsers/documentparser.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/PasswordResetView.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/settingsview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/LoginView.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Utils/xmlhandlers.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Models/Bookmark.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/themesettingsview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Extensions/ImageExtensions.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Extensions/colorextensions.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Extensions/stringextensions.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Models/User.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Services/bookstorage.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/readercontentview.swift -primary-file /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/ReaderView.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Parsers/chapterparser.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Item.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/filemanagerview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Utils/stringbuilder.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/SourceManagerView.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Extensions/htmlparserutils+extension.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Managers/booksourcemanager.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/SharedViewStyles.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/txtruleview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Services/booksourcefetcher.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/chapterlistview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/contentview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Managers/AccountManager.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/readingcontentstack.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Models/BookSource.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Models/Chapter.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/dictionaryview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/IOSReaderApp.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Utils/imagecache.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/templateeditorview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Managers/backupmanager.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/ReadingRecord.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Components/SharedReaderComponents.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Managers/memorymanager.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/BookShelfView.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/UnifiedReaderView.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/BookStoreView.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/AdvancedFeatures.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/BookDetailView.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/readingrecordview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Utils/htmlparserutils.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Models/ReadingProgress.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Extensions/arrayextensions.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/ReadingMode.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Components/ReaderToolbars.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/bookmarkview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/BookSourceView.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/webserviceview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/RegisterView.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Parsers/htmlparser.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/ReaderTypes.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Core/readercore.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Core/readerrenderer.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/BookSourceSelectorView.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Services/contentfetcher.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Coordinators/ReaderCoordinator.swift /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/DerivedSources/GeneratedAssetSymbols.swift -emit-dependencies-path /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/Objects-normal/arm64/ReaderView.d -emit-const-values-path /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/Objects-normal/arm64/ReaderView.swiftconstvalues -emit-reference-dependencies-path /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/Objects-normal/arm64/ReaderView.swiftdeps -serialize-diagnostics-path /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/Objects-normal/arm64/ReaderView.dia -emit-localized-strings -emit-localized-strings-path /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/Objects-normal/arm64 -target arm64-apple-ios18.2-simulator -Xllvm -aarch64-use-tbi -enable-objc-interop -sdk /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator18.4.sdk -I /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Products/Debug-iphonesimulator -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Products/Debug-iphonesimulator/PackageFrameworks -F /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Products/Debug-iphonesimulator -no-color-diagnostics -enable-testing -g -debug-info-format\=dwarf -dwarf-version\=5 -module-cache-path /Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex -profile-generate -profile-coverage-mapping -swift-version 6 -enforce-exclusivity\=checked -Onone -D DEBUG -serialize-debugging-options -const-gather-protocols-file /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/Objects-normal/arm64/IOSReader_const_extract_protocols.json -enable-experimental-feature DebugDescriptionMacro -empty-abi-descriptor -validate-clang-modules-once -clang-build-session-file /Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation -Xcc -working-directory -Xcc /Users/<USER>/Desktop/Coding/IOSReader/IOSReader -resource-dir /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift -enable-anonymous-context-mangled-names -file-compilation-dir /Users/<USER>/Desktop/Coding/IOSReader/IOSReader -Xcc -ivfsstatcache -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/SDKStatCaches.noindex/iphonesimulator18.4-22E235-71825a6d136aa4733edcbe86ece5525b.sdkstatcache -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/swift-overrides.hmap -Xcc -iquote -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/IOSReader-generated-files.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/IOSReader-own-target-headers.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/IOSReader-all-non-framework-target-headers.hmap -Xcc -ivfsoverlay -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader-a3beb3f0916461e73b6d69f5c8becc9c-VFS-iphonesimulator/all-product-headers.yaml -Xcc -iquote -Xcc /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/IOSReader-project-headers.hmap -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Products/Debug-iphonesimulator/include -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/DerivedSources-normal/arm64 -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/DerivedSources/arm64 -Xcc -I/Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/DerivedSources -Xcc -DDEBUG\=1 -module-name IOSReader -frontend-parseable-output -disable-clang-spi -target-sdk-version 18.4 -target-sdk-name iphonesimulator18.4 -external-plugin-path /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/lib/swift/host/plugins\#/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/bin/swift-plugin-server -external-plugin-path /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/local/lib/swift/host/plugins\#/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/bin/swift-plugin-server -in-process-plugin-server-path /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/host/libSwiftInProcPluginServer.dylib -plugin-path /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/host/plugins -plugin-path /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/local/lib/swift/host/plugins -o /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Build/Intermediates.noindex/IOSReader.build/Debug-iphonesimulator/IOSReader.build/Objects-normal/arm64/ReaderView.o -index-unit-output-path /IOSReader.build/Debug-iphonesimulator/IOSReader.build/Objects-normal/arm64/ReaderView.o -index-store-path /Users/<USER>/Library/Developer/Xcode/DerivedData/IOSReader-egyakxmfybxrjrfylpdgpbisvtdz/Index.noindex/DataStore -index-system-modules

SwiftCompile normal arm64 /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/ReaderView.swift (in target 'IOSReader' from project 'IOSReader')
    cd /Users/<USER>/Desktop/Coding/IOSReader/IOSReader
    
/Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/ReaderView.swift:208:18: error: consecutive statements on a line must be separated by ';'
                } label {
                 ^
                 ;
/Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/ReaderView.swift:208:19: error: cannot find 'label' in scope
                } label {
                  ^~~~~
/Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/ReaderView.swift:204:24: error: trailing closure passed to parameter of type 'PrimitiveButtonStyleConfiguration' that does not accept a closure
                Button {
                       ^
SwiftUI.Button.init:3:22: note: 'init(_:)' declared here
  nonisolated public init(_ configuration: PrimitiveButtonStyleConfiguration)}
                     ^

SwiftDriverJobDiscovery normal arm64 Compiling IOSReaderApp.swift (in target 'IOSReader' from project 'IOSReader')

SwiftCompile normal arm64 Compiling\ BookShelfView.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/BookShelfView.swift (in target 'IOSReader' from project 'IOSReader')

SwiftCompile normal arm64 /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/BookShelfView.swift (in target 'IOSReader' from project 'IOSReader')
    cd /Users/<USER>/Desktop/Coding/IOSReader/IOSReader
    

SwiftDriverJobDiscovery normal arm64 Compiling BookSourceSelectorView.swift (in target 'IOSReader' from project 'IOSReader')

SwiftCompile normal arm64 Compiling\ User.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Models/User.swift (in target 'IOSReader' from project 'IOSReader')

SwiftCompile normal arm64 /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Models/User.swift (in target 'IOSReader' from project 'IOSReader')
    cd /Users/<USER>/Desktop/Coding/IOSReader/IOSReader
    

SwiftDriverJobDiscovery normal arm64 Compiling bookmarkview.swift (in target 'IOSReader' from project 'IOSReader')

SwiftCompile normal arm64 Compiling\ htmlparserutils.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Utils/htmlparserutils.swift (in target 'IOSReader' from project 'IOSReader')

SwiftCompile normal arm64 /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Utils/htmlparserutils.swift (in target 'IOSReader' from project 'IOSReader')
    cd /Users/<USER>/Desktop/Coding/IOSReader/IOSReader
    

SwiftDriverJobDiscovery normal arm64 Compiling BookSourceView.swift (in target 'IOSReader' from project 'IOSReader')

SwiftCompile normal arm64 Compiling\ readingrecordview.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/readingrecordview.swift (in target 'IOSReader' from project 'IOSReader')

SwiftDriverJobDiscovery normal arm64 Compiling backupview.swift (in target 'IOSReader' from project 'IOSReader')

note: Disabling hardened runtime with ad-hoc codesigning. (in target 'IOSReader' from project 'IOSReader')
** BUILD FAILED **


The following build commands failed:
	SwiftCompile normal arm64 Compiling\ ReaderView.swift /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/ReaderView.swift (in target 'IOSReader' from project 'IOSReader')
	SwiftCompile normal arm64 /Users/<USER>/Desktop/Coding/IOSReader/IOSReader/IOSReader/Views/ReaderView.swift (in target 'IOSReader' from project 'IOSReader')
	Building project IOSReader with scheme IOSReader
(3 failures)
