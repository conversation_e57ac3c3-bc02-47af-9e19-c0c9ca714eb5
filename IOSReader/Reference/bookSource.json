[{"bookSourceComment": "", "bookSourceGroup": "-------", "bookSourceName": "书城🎃", "bookSourceType": 0, "bookSourceUrl": "https://bookshelf.html5.qq.com#🎃", "bookUrlPattern": "", "customOrder": 83, "enabled": true, "enabledCookieJar": false, "enabledExplore": true, "header": "{\"referer\":\"https://bookshelf.html5.qq.com/?t=native&sc_id=1\"}", "lastUpdateTime": 1659971789230, "loginUrl": "", "respondTime": 7833, "ruleBookInfo": {"tocUrl": "https://bookshelf.html5.qq.com/api/migration/list_charpter?resourceid={$.rows.*.resourceid}&start=1&serialnum={$.rows.*.serialnum}"}, "ruleContent": {"content": "$.rows..serialcontent##(.)(.)##$2$1"}, "ruleExplore": {}, "ruleSearch": {"author": "$.author", "bookList": "$.rows", "bookUrl": "https://bookshelf.html5.qq.com/api/novel/book/get?bookid={$.resourceid}", "coverUrl": "$.picurl", "intro": "$.summary", "kind": "$.subject&&$.subtype", "lastChapter": "$.lastserialname", "name": "$.resourcename"}, "ruleToc": {"chapterList": "$.rows", "chapterName": "$.serialname", "chapterUrl": "https://bookshelf.html5.qq.com/api/migration/show_bookdetail?encrypt=1&resourceid={$.resourceid}&serialid={$.serialid}&serialurl=&autopay=0&ch=", "nextTocUrl": "<js>\nvar i,n,url0,url1,now,all;\nvar url=[];\nurl0=String(baseUrl).replace(/(.*start=).*/,\"$1\");\nurl1=String(baseUrl).replace(/.*start=\\d+(.*)/,\"$1\");\nnow=String(baseUrl).replace(/.*start=(\\d+).*/,\"$1\");\nall=String(baseUrl).replace(/.*serialnum=(\\d+).*/,\"$1\");\nn=parseInt(all)/10+1;\n//url[0]=url0+\"1\"+url1;\nif (n>now){\nfor(i=2;i<=n;i++)\n url[i]=url0+i+url1;\n}\nurl\n</js>"}, "searchUrl": "https://bookshelf.html5.qq.com/api/op/search/?count=60&resourcename={{key}}", "weight": 0}]