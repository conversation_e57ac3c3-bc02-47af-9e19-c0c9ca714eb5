```markdown
# 阅友小说App需求文档

## 一、界面结构需求

### 1. 主界面（图1）
```markdown
### 界面层级
1. **状态栏**
   - 时间：02:26
   - 信号强度：57

2. **导航栏**
   - 左侧：搜索栏 + 菜单图标
   - 中间：分类标签（`重磅推荐` `男生必读` `女生爱看`）
   - 当前选中：重磅推荐

3. **内容区域**
   - 分类标签：`小`（疑似筛选标识）
   - 书籍列表（6项）：
     | 封面 | 书名 | 作者 | 简介 | 标签 | 操作 |
     |------|------|------|------|------|------|
     | 《无上神帝》 | 蜗牛狂奔 | 千万大世界... | 古言榜第一 | `+` |
     | 《神医毒妃不好惹》 | 不好惹 | 穿越复仇 | 古言榜第一 | `+` |
     | ...（其他4本结构相同） |

4. **底部导航**
   - `书架` `发现` `我的`
```

### 2. 书源管理界面（图2）
```markdown
### 界面层级
1. **分类标签**
   - `重磅推荐` `男生必读` `女生爱看`

2. **内容筛选**
   - 选项卡：`小说` `漫画`（当前选中小说）
   - 书源列表（两列布局）：
     | 书源名称 | 状态标识 |
     |----------|----------|
     | 轻菠萝包 | `O` |
     | 阅友小说 | `O` |
     | 黑岩网吧 | `O` |
     | ...（其他书源结构相同） |
```

### 3. 书籍详情页（图3）
```markdown
### 界面层级
1. **头部区域**
   - 模糊背景 + 书名《神》
   - 标签：`玄幻奇幻`

2. **内容展示**
   - 故事简介：
     ```
     万千大世界...主宰诸天万界！
     ```
   - 书籍信息：
     ```yaml
     作者：蜗牛狂奔
     来源：阅友小说
     字数：1608.04万字
     章节：共6353章（最新：第6353章 古战场）
     ```

3. **操作区域**
   - 功能按钮：
     - `换源`（URL：http://m.suixkan.com）
     - `缓存` `清空书籍内容`
     - `加入书架` `立即阅读`
   - 辅助功能：
     - `搜作者` `小说`（分类导航）
     - 目录导航（共6353章）
```

## 二、交互逻辑需求

### 1. 主界面交互
```markdown
1. **分类切换**
   - 点击`男生必读`/`女生爱看`时：
     - 加载对应分类书籍列表
     - 保持滚动位置记忆

2. **书籍操作**
   - 点击封面：跳转详情页
   - 长按书籍：显示书源切换菜单
   - 点击`+`按钮：
     - 已收藏：显示"已收藏"状态
     - 未收藏：弹出确认框（"加入书架？"）
```

### 2. 书源管理交互
```markdown
1. **书源操作**
   - 点击眼睛图标：显示书源URL
   - 长按书源项：
     - 弹出菜单（切换默认/删除/测试连接）
   - 切换小说/漫画标签时：
     - 自动刷新对应分类书源列表
```

### 3. 详情页交互
```markdown
1. **功能操作**
   - 点击`换源`：弹出书源选择器（带搜索功能）
   - 点击`立即阅读`：
     - 记录阅读进度（默认从最新章节开始）
     - 跳转阅读器页面（带缓存预加载）
   - `缓存`操作：异步下载最近3章内容

2. **导航行为**
   - 点击作者名：跳转作者专区
   - 点击目录章节：平滑滚动到对应位置
```

## 三、视觉规范
```markdown
### 1. 基础样式
- 字体：
  - 标题：18pt 加粗（书名）
  - 副标题：14pt 深灰色（作者/简介）
- 颜色：
  - 主按钮：#FF4500（橙红）
  - 禁用状态：#CCCCCC（灰）
  - 背景：#FFFFFF（白）

### 2. 动效规范
- 分类切换：0.3s 平滑过渡
- 按钮点击：水波纹扩散效果
- 列表滚动：视差加载效果（封面渐进式显示）
```

## 四、数据结构
```markdown
### 书籍数据模型
{
  id: String,
  title: String,
  author: String,
  coverUrl: URL,
  description: String,
  tags: [String],
  source: {
    name: String,
    url: URL,
    status: enum(online/offline)
  },
  metadata: {
    wordCount: Float,  # 1608.04万字
    chapterCount: Int, # 6353章
    latestChapter: String
  }
}

### 扩展媒体类型（预留）
{
  mediaType: enum(novel/comic/audio/video),
  videoURL: URL?,
  audioURL: URL?,
  playProgress: Double
}
```

## 五、异常处理需求
```markdown
### 1. 错误场景处理
| 错误类型 | 提示方式 | 解决方案 |
|----------|----------|----------|
| 书源失效 | 红色Toast | 自动切换备用书源 |
| 内容加载失败 | 置灰显示 | 提供"重试"按钮 |
| 无网络连接 | 对话框提示 | 显示离线阅读入口 |

### 2. 内容安全策略
- 敏感词过滤（DFA算法实时检测）
- 图片懒加载（仅显示可视区域）
- 防爬虫机制（请求频率限制）
```

## 六、扩展性设计
```markdown
### 1. 功能扩展预留
```swift
// 视频内容支持协议
protocol MediaContent {
    var videoURL: URL? { get }
    var duration: TimeInterval { get }
    var playProgress: Double { get set }
}

// 音频内容支持协议
protocol AudioContent {
    var audioURL: URL { get }
    var duration: TimeInterval { get }
    var playbackHistory: [Date] { get }
}
```

### 2. 性能优化点
1. 虚拟列表技术（支持10万+书籍列表）
2. 图片预加载策略（提前加载下3个封面）
3. 内存预警机制（超过200MB自动清理缓存）
```