Command line invocation:
    /Applications/Xcode.app/Contents/Developer/usr/bin/xcodebuild -project IOSReader.xcodeproj -scheme IOSReader -destination "platform=iOS Simulator,name=iPhone 15" -configuration Debug build

Resolve Package Graph


Resolved source packages:
  ZIPFoundation: /Users/<USER>/Downloads/ZIPFoundation-0.9.19 @ local

2025-05-28 11:05:28.724 xcodebuild[78565:5472683] Writing error result bundle to /var/folders/8l/6xzx3nbx0xv65sycg8vw7j9h0000gn/T/ResultBundle_2025-28-05_11-05-0028.xcresult
xcodebuild: error: Unable to find a device matching the provided destination specifier:
		{ platform:iOS Simulator, OS:latest, name:iPhone 15 }

	The requested device could not be found because no available devices matched the request.

	Available destinations for the "IOSReader" scheme:
		{ platform:iOS, id:dvtdevice-DVTiPhonePlaceholder-iphoneos:placeholder, name:Any iOS Device }
		{ platform:iOS Simulator, id:dvtdevice-DVTiOSDeviceSimulatorPlaceholder-iphonesimulator:placeholder, name:Any iOS Simulator Device }
		{ platform:iOS Simulator, arch:arm64, id:26BA3052-B0C9-40D7-8368-78F043E0B1AB, OS:18.3.1, name:iPad (10th generation) }
		{ platform:iOS Simulator, arch:x86_64, id:26BA3052-B0C9-40D7-8368-78F043E0B1AB, OS:18.3.1, name:iPad (10th generation) }
		{ platform:iOS Simulator, arch:arm64, id:02C04E86-06DE-4A47-92F7-114BD7D8493B, OS:18.3.1, name:iPad (A16) }
		{ platform:iOS Simulator, arch:x86_64, id:02C04E86-06DE-4A47-92F7-114BD7D8493B, OS:18.3.1, name:iPad (A16) }
		{ platform:iOS Simulator, arch:arm64, id:91B38C46-F402-44A6-A642-38C0A00B13EC, OS:18.4, name:iPad (A16) }
		{ platform:iOS Simulator, arch:x86_64, id:91B38C46-F402-44A6-A642-38C0A00B13EC, OS:18.4, name:iPad (A16) }
		{ platform:iOS Simulator, arch:arm64, id:D30A328A-D100-4C3E-B1FF-0A27EC4BC5A3, OS:18.3.1, name:iPad Air 11-inch (M2) }
		{ platform:iOS Simulator, arch:x86_64, id:D30A328A-D100-4C3E-B1FF-0A27EC4BC5A3, OS:18.3.1, name:iPad Air 11-inch (M2) }
		{ platform:iOS Simulator, arch:arm64, id:30378D85-DD63-4A62-AE57-2A6E7E605592, OS:18.3.1, name:iPad Air 11-inch (M3) }
		{ platform:iOS Simulator, arch:x86_64, id:30378D85-DD63-4A62-AE57-2A6E7E605592, OS:18.3.1, name:iPad Air 11-inch (M3) }
		{ platform:iOS Simulator, arch:arm64, id:A03414AE-50F9-45FE-ACD1-D83E899B153D, OS:18.4, name:iPad Air 11-inch (M3) }
		{ platform:iOS Simulator, arch:x86_64, id:A03414AE-50F9-45FE-ACD1-D83E899B153D, OS:18.4, name:iPad Air 11-inch (M3) }
		{ platform:iOS Simulator, arch:arm64, id:FB825B78-7420-45BE-A047-843DE91C2F9D, OS:18.3.1, name:iPad Air 13-inch (M2) }
		{ platform:iOS Simulator, arch:x86_64, id:FB825B78-7420-45BE-A047-843DE91C2F9D, OS:18.3.1, name:iPad Air 13-inch (M2) }
		{ platform:iOS Simulator, arch:arm64, id:77C30C41-C70A-4915-A9A2-B17066545256, OS:18.3.1, name:iPad Air 13-inch (M3) }
		{ platform:iOS Simulator, arch:x86_64, id:77C30C41-C70A-4915-A9A2-B17066545256, OS:18.3.1, name:iPad Air 13-inch (M3) }
		{ platform:iOS Simulator, arch:arm64, id:3C14D5DE-B181-4404-834C-7B4EA57AC07A, OS:18.4, name:iPad Air 13-inch (M3) }
		{ platform:iOS Simulator, arch:x86_64, id:3C14D5DE-B181-4404-834C-7B4EA57AC07A, OS:18.4, name:iPad Air 13-inch (M3) }
		{ platform:iOS Simulator, arch:arm64, id:261233AC-463F-4EB0-9327-F61382F946AD, OS:18.3.1, name:iPad Pro 11-inch (M4) }
		{ platform:iOS Simulator, arch:x86_64, id:261233AC-463F-4EB0-9327-F61382F946AD, OS:18.3.1, name:iPad Pro 11-inch (M4) }
		{ platform:iOS Simulator, arch:arm64, id:958B9E47-3926-4358-85FD-C19036966A21, OS:18.4, name:iPad Pro 11-inch (M4) }
		{ platform:iOS Simulator, arch:x86_64, id:958B9E47-3926-4358-85FD-C19036966A21, OS:18.4, name:iPad Pro 11-inch (M4) }
		{ platform:iOS Simulator, arch:arm64, id:038122A3-90F6-491B-957A-D5DC3FA9996F, OS:18.3.1, name:iPad Pro 13-inch (M4) }
		{ platform:iOS Simulator, arch:x86_64, id:038122A3-90F6-491B-957A-D5DC3FA9996F, OS:18.3.1, name:iPad Pro 13-inch (M4) }
		{ platform:iOS Simulator, arch:arm64, id:9BBBC176-5F82-422A-BBB4-17F5980AC23C, OS:18.4, name:iPad Pro 13-inch (M4) }
		{ platform:iOS Simulator, arch:x86_64, id:9BBBC176-5F82-422A-BBB4-17F5980AC23C, OS:18.4, name:iPad Pro 13-inch (M4) }
		{ platform:iOS Simulator, arch:arm64, id:593A8463-C8EB-4E6E-8933-18E8C1FE7DB9, OS:18.3.1, name:iPad mini (A17 Pro) }
		{ platform:iOS Simulator, arch:x86_64, id:593A8463-C8EB-4E6E-8933-18E8C1FE7DB9, OS:18.3.1, name:iPad mini (A17 Pro) }
		{ platform:iOS Simulator, arch:arm64, id:FC076344-C3ED-43D2-913D-2F52558A8772, OS:18.4, name:iPad mini (A17 Pro) }
		{ platform:iOS Simulator, arch:x86_64, id:FC076344-C3ED-43D2-913D-2F52558A8772, OS:18.4, name:iPad mini (A17 Pro) }
		{ platform:iOS Simulator, arch:arm64, id:7301571E-B16D-4EDA-A3B7-3FF100E2524E, OS:18.3.1, name:iPhone 15 Pro }
		{ platform:iOS Simulator, arch:x86_64, id:7301571E-B16D-4EDA-A3B7-3FF100E2524E, OS:18.3.1, name:iPhone 15 Pro }
		{ platform:iOS Simulator, arch:arm64, id:7E9475B9-1259-4648-BFEA-72318699F235, OS:18.3.1, name:iPhone 16 }
		{ platform:iOS Simulator, arch:x86_64, id:7E9475B9-1259-4648-BFEA-72318699F235, OS:18.3.1, name:iPhone 16 }
		{ platform:iOS Simulator, arch:arm64, id:0347A8A6-1BD2-4761-A300-1548354AF787, OS:18.4, name:iPhone 16 }
		{ platform:iOS Simulator, arch:x86_64, id:0347A8A6-1BD2-4761-A300-1548354AF787, OS:18.4, name:iPhone 16 }
		{ platform:iOS Simulator, arch:arm64, id:85538649-CB1B-443C-B839-CDF8735C6A12, OS:18.3.1, name:iPhone 16 Plus }
		{ platform:iOS Simulator, arch:x86_64, id:85538649-CB1B-443C-B839-CDF8735C6A12, OS:18.3.1, name:iPhone 16 Plus }
		{ platform:iOS Simulator, arch:arm64, id:ACC45F86-E040-4EB7-ABFD-63EC299618C7, OS:18.4, name:iPhone 16 Plus }
		{ platform:iOS Simulator, arch:x86_64, id:ACC45F86-E040-4EB7-ABFD-63EC299618C7, OS:18.4, name:iPhone 16 Plus }
		{ platform:iOS Simulator, arch:arm64, id:533F4D43-1B7B-4E29-A80B-58A33A21B3EF, OS:18.3.1, name:iPhone 16 Pro }
		{ platform:iOS Simulator, arch:x86_64, id:533F4D43-1B7B-4E29-A80B-58A33A21B3EF, OS:18.3.1, name:iPhone 16 Pro }
		{ platform:iOS Simulator, arch:arm64, id:2B0FFA9A-AE1F-4DE6-9CA1-8232400B407D, OS:18.4, name:iPhone 16 Pro }
		{ platform:iOS Simulator, arch:x86_64, id:2B0FFA9A-AE1F-4DE6-9CA1-8232400B407D, OS:18.4, name:iPhone 16 Pro }
		{ platform:iOS Simulator, arch:arm64, id:D5D87DC9-29BA-4443-B1A8-AAAEF7707895, OS:18.3.1, name:iPhone 16 Pro Max }
		{ platform:iOS Simulator, arch:x86_64, id:D5D87DC9-29BA-4443-B1A8-AAAEF7707895, OS:18.3.1, name:iPhone 16 Pro Max }
		{ platform:iOS Simulator, arch:arm64, id:6012CD14-12FB-4961-BC0F-B83B5CD07F76, OS:18.4, name:iPhone 16 Pro Max }
		{ platform:iOS Simulator, arch:x86_64, id:6012CD14-12FB-4961-BC0F-B83B5CD07F76, OS:18.4, name:iPhone 16 Pro Max }
		{ platform:iOS Simulator, arch:arm64, id:7CDA8EDC-8261-4CDA-9226-32A7AECEBDC6, OS:18.3.1, name:iPhone 16e }
		{ platform:iOS Simulator, arch:x86_64, id:7CDA8EDC-8261-4CDA-9226-32A7AECEBDC6, OS:18.3.1, name:iPhone 16e }
		{ platform:iOS Simulator, arch:arm64, id:BD53E19C-1FB7-4A5C-A30E-534BE5B865AD, OS:18.4, name:iPhone 16e }
		{ platform:iOS Simulator, arch:x86_64, id:BD53E19C-1FB7-4A5C-A30E-534BE5B865AD, OS:18.4, name:iPhone 16e }
		{ platform:iOS Simulator, arch:arm64, id:DDDF818B-76D9-4480-BD5A-DA9B802269E2, OS:18.3.1, name:iPhone SE (3rd generation) }
		{ platform:iOS Simulator, arch:x86_64, id:DDDF818B-76D9-4480-BD5A-DA9B802269E2, OS:18.3.1, name:iPhone SE (3rd generation) }
