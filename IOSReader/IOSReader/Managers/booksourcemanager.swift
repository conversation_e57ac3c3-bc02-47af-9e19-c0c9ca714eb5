import SwiftUI
import Combine
// Kanna依赖移除，使用原生Foundation替代
// 使用HTMLParserUtils替代Kanna
import UniformTypeIdentifiers // 导入 UniformTypeIdentifiers 用于文件导入
import SwiftData // 确保 SwiftData 已导入
import JavaScriptCore // 确保 JavaScriptCore 已导入
// 导入BookSource模型
import Foundation

// MARK: - 搜索结果结构体
/// 书源搜索结果结构体
struct SearchResult: Identifiable {
    let id = UUID()
    let title: String
    let author: String?
    let coverURL: URL?
    let introduction: String?
    let latestChapter: String?
    let bookURL: URL // URL to the book details page on the source website
    let sourceId: UUID // 关联的书源ID
    
    // 转换为Book对象的方法
    func toBook() -> Book {
        return Book(
            title: title,
            author: author ?? "未知作者",
            coverUrl: coverURL,
            introduction: introduction,
            latestChapter: latestChapter,
            url: bookURL,
            sourceId: sourceId,
            bookType: nil, // 新增
            filePath: nil, // 新增
            lastReadTimestamp: nil, // 新增
            coverImageData: nil // 新增
        )
    }
}

/// 书源管理器
/// 负责处理书源的解析、导入、测试和同步功能
/// 主要功能包括：
/// 1. 解析开源阅读JSON书源v3标准
/// 2. 本地/远程书源导入
/// 3. 书源测试
/// 4. GitHub仓库同步
/// 5. 书源数据持久化(SwiftData)
/// 6. 支持XPath/JSONPath/JS三种规则解析方式

// 将 actor 修改为 @MainActor class
@MainActor
class BookSourceManager: ObservableObject, @unchecked Sendable { // 添加 ObservableObject
    // MARK: - Properties
    private let modelContainer: ModelContainer
    private let modelContext: ModelContext
    private let fetcher = BookSourceFetcher() // Moved fetcher inside the class
    
    // 添加 @Published 属性以供 SwiftUI 视图观察
    @Published var bookSources: [BookSource] = []
    @Published var isLoading: Bool = false // 通用加载状态，可考虑细化
    @Published var errorMessage: String? = nil
    @Published var currentSource: BookSource? = nil // 添加当前选中的书源
    @Published var availableCategories: [String] = [] // 新增：所有书源的可用分类列表
    @Published var currentSourceCategories: [String] = [] // 新增：当前选中书源的分类列表

    // 书源导入状态
    @Published var isImportingNewSources: Bool = false
    @Published var importProgressTotal: Int = 0
    @Published var importProgressCurrent: Int = 0
    @Published var importSuccessCount: Int = 0
    @Published var importFailureCount: Int = 0
    @Published var lastImportErrorMessage: String? = nil

    /// 重置导入相关的统计数据和状态
    @MainActor
    func resetImportStats() {
        self.isImportingNewSources = false
        self.importProgressTotal = 0
        self.importProgressCurrent = 0
        self.importSuccessCount = 0
        self.importFailureCount = 0
        self.lastImportErrorMessage = nil
        // self.errorMessage = nil // 视情况决定是否清除通用错误信息
        print("Import statistics have been reset.")
    }
    
    // MARK: - Initialization
    init(modelContainer: ModelContainer) {
        self.modelContainer = modelContainer
        self.modelContext = ModelContext(modelContainer)
        fetcher.initialize() // Initialize fetcher
        print("BookSourceManager initialized with ModelContainer.")
        // 初始化时获取一次书源
        Task {
            await fetchBookSources()
        }
    }
    
    // Convenience initializer for previews or testing without full container setup
    // 注意：预览模式下的初始化可能需要调整以正确处理异步获取
    init() {
        // Attempt to create a default container, handle potential errors
        do {
            // 为预览创建内存数据库
            let schema = Schema([BookSource.self])
            let configuration = ModelConfiguration(isStoredInMemoryOnly: true)
            self.modelContainer = try ModelContainer(for: schema, configurations: [configuration])
            self.modelContext = ModelContext(modelContainer)
        } catch {
            // Fallback or error handling if container creation fails
            fatalError("Could not create ModelContainer: \(error)")
        }
        fetcher.initialize()
        print("BookSourceManager initialized with default in-memory ModelContainer for preview.")
        // 预览模式下也尝试获取一次（虽然可能是空的）
        Task {
            await fetchBookSources()
        }
    }
    
    // MARK: - Book Source Management
    
    /// 构建请求
    /// - Parameters:
    ///   - source: 书源
    ///   - page: 页码
    ///   - category: 分类
    ///   - keyword: 搜索关键词
    /// - Returns: URLRequest
    func buildRequest(source: BookSource, page: Int, category: String? = nil, keyword: String? = nil) async throws -> URLRequest {
        // 获取搜索URL
        guard let searchUrl = source.searchUrl else {
            throw NSError(domain: "BookSourceManager", code: -1, userInfo: [NSLocalizedDescriptionKey: "书源未配置搜索URL"])
        }
        
        // 构建URL
        var urlString = searchUrl
        
        // 替换页码占位符
        urlString = urlString.replacingOccurrences(of: "{{page}}", with: String(page))
        
        // 替换关键词占位符
        if let keyword = keyword {
            urlString = urlString.replacingOccurrences(of: "{{keyword}}", with: keyword)
        }
        
        // 替换分类占位符
        if let category = category {
            urlString = urlString.replacingOccurrences(of: "{{category}}", with: category)
        }
        
        // 创建URL
        guard let url = URL(string: urlString) else {
            throw NSError(domain: "BookSourceManager", code: -2, userInfo: [NSLocalizedDescriptionKey: "无效的URL"])
        }
        
        // 创建请求
        var request = URLRequest(url: url)
        request.httpMethod = "GET"
        
        // 添加请求头
        if let header = source.header {
            let headerFields = try JSONDecoder().decode([String: String].self, from: header.data(using: .utf8) ?? Data())
            for (key, value) in headerFields {
                request.setValue(value, forHTTPHeaderField: key)
            }
        }
        
        return request
    }
    
    /// 选择当前书源
    /// - Parameter source: 要选择的书源
    func selectSource(_ source: BookSource) {
        print("[BookSourceManager] Attempting to select source: \(source.name) (ID: \(source.id))")
        self.currentSource = source
        print("[BookSourceManager] Did set currentSource to: \(self.currentSource?.name ?? "nil") (ID: \(self.currentSource?.id.uuidString ?? "nil"))")
        updateCurrentSourceCategories() // 更新当前书源的分类
        print("[BookSourceManager] Selected book source: \(source.name) and updated categories.")
    }
    
    /// 清除当前选中的书源
    func clearCurrentSource() {
        self.currentSource = nil
        updateCurrentSourceCategories() // 清空当前书源的分类
        print("Cleared current book source selection")
    }
    
    /// 从 JSON 数据导入书源
    /// - Parameter jsonData: 包含书源数组的 JSON 数据
    /// - Throws: 如果 JSON 解析或数据转换失败
    func importBookSources(from jsonData: Data) async throws {
        print("[BookSourceManager] Starting importBookSources(from jsonData: Data)")
        // 更新导入状态变量
        await MainActor.run {
            print("[BookSourceManager] importBookSources - Resetting import stats and setting isImportingNewSources to true")
            self.isImportingNewSources = true
            self.importProgressTotal = 0
            self.importProgressCurrent = 0
            self.importSuccessCount = 0
            self.importFailureCount = 0
            self.lastImportErrorMessage = nil
            self.errorMessage = nil // 清除旧的通用错误信息
        }
        
        let decoder = JSONDecoder()
        let v3Sources = try decoder.decode([BookSourceV3].self, from: jsonData)
        await MainActor.run { 
            self.importProgressTotal = v3Sources.count
            print("[BookSourceManager] importBookSources - Calculated importProgressTotal: \(self.importProgressTotal)")
        }
        
        var localImportSuccessCount = 0
        var localImportFailureCount = 0
        var accumulatedErrorMessages: [String] = []
        
        for (index, v3Source) in v3Sources.enumerated() {
            print("[BookSourceManager] importBookSources - Processing source \(index + 1) of \(v3Sources.count): \(v3Source.bookSourceName)")
            await MainActor.run { 
                self.importProgressCurrent = index + 1
                print("[BookSourceManager] importBookSources - Updated importProgressCurrent: \(self.importProgressCurrent)")
            }
            do {
                // 检查书源是否已存在 (基于 URL)
                let urlPredicate = #Predicate<BookSource> { $0.url == v3Source.bookSourceUrl }
                let descriptor = FetchDescriptor(predicate: urlPredicate)
                let existingSources = try modelContext.fetch(descriptor)
                
                if existingSources.first != nil {
                    // 更新现有书源逻辑 (如果需要，或者跳过)
                    print("Skipping existing book source: \(v3Source.bookSourceName)")
                } else {
                    let newSource = try BookSource(from: v3Source) // Use the throwing initializer
                    modelContext.insert(newSource)
                    localImportSuccessCount += 1
                    print("Successfully prepared for insertion: \(newSource.name)")
                }
            } catch {
                localImportFailureCount += 1
                let errorDetail = "导入 \(v3Source.bookSourceName) 失败: \(error.localizedDescription)"
                print(errorDetail)
                accumulatedErrorMessages.append(errorDetail)
            }
        }
        
        // Save changes after processing all sources
        if modelContext.hasChanges {
            try modelContext.save()
        }

        // 更新最终导入结果的 @Published 属性
        await MainActor.run {
            print("[BookSourceManager] importBookSources - Updating final import results. Success: \(localImportSuccessCount), Failure: \(localImportFailureCount)")
            self.importSuccessCount = localImportSuccessCount
            self.importFailureCount = localImportFailureCount
            if !accumulatedErrorMessages.isEmpty {
                self.lastImportErrorMessage = accumulatedErrorMessages.joined(separator: "\n")
            }
            self.isImportingNewSources = false
            // 可以选择在这里设置一个通用的errorMessage，或者让UI根据具体计数值和lastImportErrorMessage来显示结果
            if localImportFailureCount > 0 {
                self.errorMessage = "导入完成，部分书源导入失败。成功 \(localImportSuccessCount) 条，失败 \(localImportFailureCount) 条。"
            } else {
                self.errorMessage = "导入成功 \(localImportSuccessCount) 条。"
            }
        }
        print("[BookSourceManager] Finished importing sources. Imported: \(localImportSuccessCount), Errors: \(localImportFailureCount)")
        // 导入后刷新列表
        await fetchBookSources()
        print("[BookSourceManager] importBookSources completed and fetched book sources.")
    }
    
    /// 获取所有书源并更新 @Published 属性
    func fetchBookSources() async {
        isLoading = true
        errorMessage = nil
        let descriptor = FetchDescriptor<BookSource>(sortBy: [SortDescriptor(\BookSource.name)])
        do {
            let sources = try modelContext.fetch(descriptor)
            // 在主线程更新 @Published 属性
            self.bookSources = sources
            updateAvailableCategories() // 更新可用分类
            print("Fetched \(sources.count) book sources from SwiftData.")
        } catch {
            print("Error fetching book sources: \(error)")
            errorMessage = "获取书源列表失败: \(error.localizedDescription)"
        }
        isLoading = false
    }

    /// 更新可用分类列表
    private func updateAvailableCategories() {
        let categories = Set(bookSources.compactMap { $0.sourceGroup }.filter { !$0.isEmpty })
        self.availableCategories = Array(categories).sorted()
        print("Updated available categories (all sources): \(self.availableCategories)")
    }

    /// 更新当前选中书源的分类列表
    private func updateCurrentSourceCategories() {
        guard let source = currentSource else {
            self.currentSourceCategories = []
            print("Current source is nil, cleared current source categories.")
            return
        }

        // 优先尝试从 sourceGroup 获取分类，如果为空，则尝试从 ruleExplore 解析
        if let group = source.sourceGroup, !group.isEmpty {
            // 假设 sourceGroup 是单个分类名或以特定分隔符分隔的多个分类名
            // 这里简单处理，直接使用 sourceGroup 作为分类。如果需要更复杂的解析，可以在这里实现。
            // 例如，如果 sourceGroup 是 "分类A,分类B"，则可以分割字符串
            self.currentSourceCategories = [group] // 简化处理，直接将整个 group 视为一个分类
        } else if let exploreData = source.ruleExploreData,
                   let exploreRule = try? JSONDecoder().decode([String: String].self, from: exploreData),
                   let exploreUrlString = exploreRule["url"], // Assuming 'url' is the key for the explore URL string
                   !exploreUrlString.isEmpty {
            // TODO: Implement category extraction from exploreUrlString
            // For example, if exploreUrlString is "http://example.com/category/{cateId}/books.json"
            // and there's a way to get category names and their corresponding cateIds.
            // Or, if the exploreUrlString itself contains patterns for categories.
            // TODO: 实现从 ruleExplore.url 解析分类的逻辑
            // 这是一个复杂的过程，可能需要解析URL模板中的占位符或特定结构
            // 例如，如果URL是 "http://example.com/category/{cateId}/books.json"，
            // 并且有地方定义了 cateId 和分类名的映射，或者 ruleExplore.url 本身就包含了分类列表的模式
            // 暂时置空，表示需要实现
            print("TODO: Implement category extraction from exploreUrlString for source: \(source.name). Explore URL: \(exploreUrlString)")
            self.currentSourceCategories = [] // 暂时设为空，直到实现解析逻辑
        } else {
            self.currentSourceCategories = []
        }
        print("Updated current source categories: \(self.currentSourceCategories) for source: \(source.name)")
    }
    
    /// 根据 ID 删除书源
    /// - Parameter id: 要删除的书源的 UUID
    func deleteBookSource(by id: UUID) async throws {
        let sourceId = id // 捕获 id 到局部变量
        let predicate = #Predicate<BookSource> { $0.id == sourceId }
        let descriptor = FetchDescriptor(predicate: predicate)
        if let sourceToDelete = try modelContext.fetch(descriptor).first {
            modelContext.delete(sourceToDelete)
            try modelContext.save()
            print("Deleted book source with ID: \(id)")
            // 删除后刷新列表
            await fetchBookSources()
        } else {
            print("Book source with ID \(id) not found.")
            errorMessage = "未找到要删除的书源。"
            // Optionally throw an error if the source must exist
            // throw BookSourceError.notFound
        }
    }
    
    /// 更新书源信息 (主要用于启用/禁用等状态更新)
    /// - Parameter updatedSource: 包含更新信息的 BookSource 对象
    /// - Note: 确保 updatedSource 是从 modelContext 获取的受管理对象
    func updateBookSource(_ sourceToUpdate: BookSource) async throws {
        // 假设 sourceToUpdate 是从 fetchBookSources 获取的受管理对象
        // SwiftData 会自动跟踪更改，只需保存
        if modelContext.hasChanges {
            do {
                try modelContext.save()
                print("Updated book source: \(sourceToUpdate.name)")
                // 可以选择性地刷新列表，或者依赖 SwiftUI 的自动更新
                // await fetchBookSources() // 如果 UI 未自动更新，则取消注释
            } catch {
                print("Error saving updated book source: \(error)")
                errorMessage = "更新书源失败: \(error.localizedDescription)"
                throw error
            }
        } else {
            print("No changes detected for book source: \(sourceToUpdate.name)")
        }
    }
    
    /// 切换书源的启用状态
    func toggleBookSourceEnabled(_ source: BookSource) async {
        source.isEnabled.toggle()
        do {
            try await updateBookSource(source) // 使用更新方法来保存
            print("Toggled enabled state for \(source.name) to \(source.isEnabled)")
        } catch {
            // updateBookSource 内部已处理错误打印和 errorMessage 设置
            // 可以选择回滚状态
            source.isEnabled.toggle() // Revert toggle on error
        }
    }
    
    /// 切换书源的启用状态 - 供SourceManagerView使用的同步方法
    func toggleSource(_ source: BookSource) {
        Task {
            await toggleBookSourceEnabled(source)
        }
    }
    
    /// 根据索引集合删除书源
    /// - Parameter indexSet: 要删除的书源在 bookSources 数组中的索引集合
    func deleteSources(at indexSet: IndexSet) {
        isLoading = true
        Task {
            defer { isLoading = false }
            let sourcesToDelete = indexSet.map { bookSources[$0] }
            for source in sourcesToDelete {
                do {
                    try await deleteBookSource(by: source.id)
                } catch {
                    print("Error deleting source \(source.name): \(error)")
                    // 可以考虑在这里更新 errorMessage
                }
            }
            // 批量删除后，fetchBookSources 会在最后一个 deleteBookSource 调用时刷新
            // 如果需要确保在所有删除操作完成后统一刷新，可以在这里调用
            // await fetchBookSources()
        }
    }
    
    /// 删除单个书源
    /// - Parameter source: 要删除的书源
    func deleteSource(_ source: BookSource) {
        isLoading = true
        Task {
            defer { isLoading = false }
            do {
                try await deleteBookSource(by: source.id)
            } catch {
                print("Error deleting source \(source.name): \(error)")
                errorMessage = "删除书源失败: \(error.localizedDescription)"
            }
        }
    }
    
    /// 设置当前选中的书源
    /// - Parameter source: 要设置为当前的书源
    func setCurrentSource(_ source: BookSource) {
        currentSource = source
        print("Current source set to: \(source.name)")
    }

    /// 从 URL 导入书源
    /// - Parameter url: 包含书源数据的 URL
    /// - Throws: 如果下载或导入失败
    func importFromURL(_ url: URL) async throws {
        isLoading = true
        errorMessage = nil
        defer { isLoading = false }

        do {
            let (data, _) = try await URLSession.shared.data(from: url)
            try await importBookSources(from: data)
        } catch {
            print("Error importing from URL \(url): \(error)")
            errorMessage = "从 URL 导入失败: \(error.localizedDescription)"
            throw error // 将错误向上抛出，以便调用方处理
        }
    }

    /// 从字符串内容导入书源
    /// - Parameter content: 包含书源数据的字符串
    /// - Throws: 如果字符串转换或导入失败
    func importFromString(_ content: String) async throws {
        isLoading = true
        errorMessage = nil
        defer { isLoading = false }

        guard let jsonData = content.data(using: .utf8) else {
            let error = NSError(domain: "BookSourceManager", code: 0, userInfo: [NSLocalizedDescriptionKey: "无法将字符串内容转换为数据"])
            print("Error converting string to data: \(error.localizedDescription)")
            errorMessage = error.localizedDescription
            throw error
        }

        do {
            try await importBookSources(from: jsonData)
        } catch {
            print("Error importing from string: \(error)")
            // importBookSources 内部可能会设置 errorMessage，或者在这里统一设置
            if errorMessage == nil { // 避免覆盖更具体的错误信息
                errorMessage = "从字符串导入失败: \(error.localizedDescription)"
            }
            throw error // 将错误向上抛出
        }
    }

    // MARK: - Rule Parsing and Execution (Placeholder)
    
    // MARK: - 网络请求相关方法
    
    /// 构建网络请求
    /// - Parameters:
    ///   - source: 书源
    ///   - page: 页码
    ///   - category: 可选的分类名称
    ///   - searchText: 可选的搜索文本
    /// - Returns: URLRequest
    func buildRequest(for source: BookSource, page: Int, category: String? = nil, searchText: String? = nil) async throws -> URLRequest {
        // 检查书源是否启用
        guard source.isEnabled else {
            throw NSError(domain: "BookSourceManager", code: 1, userInfo: [NSLocalizedDescriptionKey: "书源未启用"])
        }
        
        // 优先使用探索URL（如果提供了分类且探索URL存在）
        // 假设探索URL模板类似于：https://api.example.com/explore?category={category}&page={page}
        // 或者 https://api.example.com/category/{category}/page/{page}
        // 这里的实现会比较简单，实际可能需要更复杂的规则来处理不同书源的URL结构
        var urlStringToUse = source.url // 默认使用书源的基础URL
        var isExploreUrl = false

        if let categoryName = category, !categoryName.isEmpty {
            // 尝试从 BookSourceV3 结构中获取 exploreUrl 模板
            // 注意：BookSource 模型目前没有直接存储原始的 exploreUrl 字符串，而是存储在 ruleExploreData 中
            // 为了简化，我们假设如果 ruleExploreData 存在，则可能包含分类相关的URL逻辑
            // 更健壮的做法是在 BookSource 中存储解析后的 exploreUrl 模板
            // 或者在 BookSourceV3 中直接使用 exploreUrl 字段（如果它代表分类列表的URL）
            
            // 尝试从 ruleExploreData 解析出 exploreUrl (这是一个简化的假设)
            // 实际的 exploreUrl 可能在 ruleExplore.url 中
            if let ruleExploreData = source.ruleExploreData, 
               let ruleExplore = try? JSONDecoder().decode([String: String].self, from: ruleExploreData), 
               let exploreBaseUrl = ruleExplore["url"] { // 假设规则中 'url' 键存储了探索页的URL模板
                
                // 替换分类占位符，这里假设占位符是 {category} 或 {cateId}
                // 实际占位符需要根据书源规则确定
                var tempUrl = exploreBaseUrl
                if tempUrl.contains("{category}") {
                    tempUrl = tempUrl.replacingOccurrences(of: "{category}", with: categoryName.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? categoryName)
                } else if tempUrl.contains("{cateId}") {
                    // 如果书源使用cateId，我们这里简单地用分类名代替，实际可能需要映射
                    tempUrl = tempUrl.replacingOccurrences(of: "{cateId}", with: categoryName.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? categoryName)
                }
                // 如果替换成功且URL有效，则使用此URL
                if URL(string: tempUrl) != nil {
                    urlStringToUse = tempUrl
                    isExploreUrl = true
                    print("Using explore URL for category '\(categoryName)': \(urlStringToUse)")
                }
            }
        }

        guard let baseUrl = URL(string: urlStringToUse) else {
            throw NSError(domain: "BookSourceManager", code: 2, userInfo: [NSLocalizedDescriptionKey: "无效的书源URL: \(urlStringToUse)"])
        }
        
        // 创建请求
        var request = URLRequest(url: baseUrl)
        request.httpMethod = "GET"
        
        // 添加通用头部
        request.setValue("application/json", forHTTPHeaderField: "Accept")
        request.setValue("Mozilla/5.0", forHTTPHeaderField: "User-Agent")
        
        // 如果有自定义header，解析并添加
        if let headerString = source.header {
            let headerPairs = headerString.components(separatedBy: .newlines)
            for pair in headerPairs {
                let components = pair.components(separatedBy: ":")
                if components.count == 2 {
                    let key = components[0].trimmingCharacters(in: .whitespacesAndNewlines)
                    let value = components[1].trimmingCharacters(in: .whitespacesAndNewlines)
                    request.setValue(value, forHTTPHeaderField: key)
                }
            }
        }

        // 此处移除了重复的错误代码块
        
        // 添加分页参数
        // 如果使用的是探索URL，并且探索URL模板已经包含了分页逻辑（例如 .../page/{pageNumber}），
        // 则这里的通用分页参数添加可能需要调整或移除，取决于模板的具体格式。
        // 为简化，我们假设分页参数总是可以作为查询参数添加，或者模板中用 {page} 占位符
        var urlComponents = URLComponents(url: baseUrl, resolvingAgainstBaseURL: true)
        var queryItems = urlComponents?.queryItems ?? []

        if urlStringToUse.contains("{page}") {
            // 如果URL模板中包含 {page} 占位符，则替换它
            // 这通常意味着分页不是通过查询参数实现的
            if let newUrlString = urlComponents?.url?.absoluteString.replacingOccurrences(of: "{page}", with: String(page)) {
                urlComponents = URLComponents(string: newUrlString)
            } else {
                 // 如果替换失败，作为后备，尝试添加为查询参数
                queryItems.append(URLQueryItem(name: "page", value: String(page)))
            }
        } else if urlStringToUse.contains("{{page}}") { // 兼容 {{page}} 格式
             if let newUrlString = urlComponents?.url?.absoluteString.replacingOccurrences(of: "{{page}}", with: String(page)) {
                urlComponents = URLComponents(string: newUrlString)
            } else {
                queryItems.append(URLQueryItem(name: "page", value: String(page)))
            }
        } else {
            // 默认作为查询参数添加
            queryItems.append(URLQueryItem(name: "page", value: String(page)))
        }
        
        // 处理搜索文本参数
        if let searchText = searchText, !searchText.isEmpty {
            // 检查URL模板中是否包含搜索占位符
            if urlStringToUse.contains("{searchKey}") {
                // 如果URL模板中包含 {searchKey} 占位符，则替换它
                if let currentUrlString = urlComponents?.url?.absoluteString,
                   let encodedSearchText = searchText.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) {
                    let newUrlString = currentUrlString.replacingOccurrences(of: "{searchKey}", with: encodedSearchText)
                    urlComponents = URLComponents(string: newUrlString)
                }
            } else if urlStringToUse.contains("{{searchKey}}") {
                // 兼容 {{searchKey}} 格式
                if let currentUrlString = urlComponents?.url?.absoluteString,
                   let encodedSearchText = searchText.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) {
                    let newUrlString = currentUrlString.replacingOccurrences(of: "{{searchKey}}", with: encodedSearchText)
                    urlComponents = URLComponents(string: newUrlString)
                }
            } else {
                // 默认作为查询参数添加
                queryItems.append(URLQueryItem(name: "q", value: searchText))
                queryItems.append(URLQueryItem(name: "keyword", value: searchText))
                queryItems.append(URLQueryItem(name: "search", value: searchText))
            }
        }
        
        if !queryItems.isEmpty {
            urlComponents?.queryItems = queryItems
        }
        
        if let finalUrl = urlComponents?.url {
            request.url = finalUrl
        }
        
        return request
    }
    
    /// 搜索书籍
    /// - Parameters:
    ///   - source: 书源
    ///   - keyword: 搜索关键词
    /// - Returns: 搜索结果数组
    func searchBooks(using source: BookSource, keyword: String) async throws -> [SearchResult] {
        // 检查书源是否启用
        guard source.isEnabled else {
            print("Source \(source.name) is disabled.")
            return []
        }
        
        // 构建搜索URL
        guard let searchUrl = source.searchUrl else {
            throw NSError(domain: "BookSourceManager", code: 3, userInfo: [NSLocalizedDescriptionKey: "书源未配置搜索URL"])
        }
        
        // 替换关键词占位符
        let finalSearchUrl = searchUrl.replacingOccurrences(of: "{keyword}", with: keyword.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? keyword)
        guard let url = URL(string: finalSearchUrl) else {
            throw NSError(domain: "BookSourceManager", code: 4, userInfo: [NSLocalizedDescriptionKey: "无效的搜索URL"])
        }
        
        // 创建并发送请求
        var request = URLRequest(url: url)
        request.httpMethod = "GET"
        
        // 添加自定义header
        if let headerString = source.header {
            let headerPairs = headerString.components(separatedBy: .newlines)
            for pair in headerPairs {
                let components = pair.components(separatedBy: ":")
                if components.count == 2 {
                    let key = components[0].trimmingCharacters(in: .whitespacesAndNewlines)
                    let value = components[1].trimmingCharacters(in: .whitespacesAndNewlines)
                    request.setValue(value, forHTTPHeaderField: key)
                }
            }
        }

        // 此处移除了重复的错误代码块
        
        // 设置通用header
        if request.value(forHTTPHeaderField: "User-Agent") == nil {
            request.setValue("Mozilla/5.0 (iPhone; CPU iPhone OS 16_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.0 Mobile/15E148 Safari/604.1", forHTTPHeaderField: "User-Agent")
        }
        
        // 发送请求
        let (data, response) = try await URLSession.shared.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse,
              (200...299).contains(httpResponse.statusCode) else {
            throw NSError(domain: "BookSourceManager", code: 5, userInfo: [NSLocalizedDescriptionKey: "网络请求失败: 状态码 \((response as? HTTPURLResponse)?.statusCode ?? 0)"])
        }
        
        // 解析书籍列表
        let books = try await parseBookList(data: data, source: source)
        
        // 将Book对象转换为SearchResult对象
        let results = books.map { book -> SearchResult in
            return SearchResult(
                title: book.title,
                author: book.author,
                coverURL: book.coverUrl,
                introduction: book.introduction,
                latestChapter: book.latestChapter,
                bookURL: book.url,
                sourceId: book.sourceId
            )
        }
        
        return results
    }
    
    /// 解析书籍列表数据
    /// - Parameters:
    ///   - data: 响应数据
    ///   - source: 书源
    /// - Returns: 书籍数组
    func parseBookList(data: Data, source: BookSource) async throws -> [Book] {
        // 解析规则
        guard let ruleSearchData = source.ruleSearchData,
              let searchRules = try? JSONDecoder().decode([String: String].self, from: ruleSearchData) else {
            throw NSError(domain: "BookSourceManager", code: 7, userInfo: [NSLocalizedDescriptionKey: "解析搜索规则失败"])
        }
        
        // 将响应数据转换为字符串
        guard let htmlString = String(data: data, encoding: .utf8) else {
            throw NSError(domain: "BookSourceManager", code: 8, userInfo: [NSLocalizedDescriptionKey: "响应数据编码错误"])
        }
        
        // 使用HTMLParserUtils解析HTML
        guard let document = HTMLParserUtils.parse(html: htmlString) else {
            throw NSError(domain: "BookSourceManager", code: 9, userInfo: [NSLocalizedDescriptionKey: "HTML解析失败"])
        }
        
        var books: [Book] = []
        
        // 获取书籍列表规则
        let listRule = searchRules["bookList"] ?? searchRules["list"] ?? "//div[@class='book-list']/div"
        
        do {
            // 使用XPath提取书籍列表
            let elements = try document.xpath(listRule)
            
            for element in elements {
                do {
                    // 提取书籍信息，使用更安全的方式处理可能的错误
                    let nameRule = searchRules["name"] ?? ".//h2/text()"
                    let authorRule = searchRules["author"] ?? ".//p[@class='author']/text()"
                    let introRule = searchRules["intro"] ?? ".//p[@class='intro']/text()"
                    let coverUrlRule = searchRules["coverUrl"] ?? ".//img/@src"
                    let bookUrlRule = searchRules["bookUrl"] ?? ".//a/@href"
                    let latestChapterRule = searchRules["lastChapter"] ?? ".//p[@class='chapter']/text()"
                    
                    // 提取文本内容
                    let nameNodes = try element.xpath(nameRule)
                    let authorNodes = try element.xpath(authorRule)
                    let introNodes = try element.xpath(introRule)
                    let coverUrlNodes = try element.xpath(coverUrlRule)
                    let bookUrlNodes = try element.xpath(bookUrlRule)
                    let latestChapterNodes = try element.xpath(latestChapterRule)
                    
                    // 获取文本内容
                    let name = nameNodes.first?.text.trimmingCharacters(in: .whitespacesAndNewlines) ?? "未知书名"
                    let author = authorNodes.first?.text.trimmingCharacters(in: .whitespacesAndNewlines)
                    let intro = introNodes.first?.text.trimmingCharacters(in: .whitespacesAndNewlines)
                    let coverUrlString = coverUrlNodes.first?.text.trimmingCharacters(in: .whitespacesAndNewlines)
                    let bookUrlString = bookUrlNodes.first?.text.trimmingCharacters(in: .whitespacesAndNewlines)
                    let latestChapter = latestChapterNodes.first?.text.trimmingCharacters(in: .whitespacesAndNewlines)
                    
                    // 处理相对URL
                    var finalBookUrlString = bookUrlString
                    var finalCoverUrlString = coverUrlString
                    
                    // 如果是相对URL，则拼接源URL
                    if let bookUrl = bookUrlString, !bookUrl.isEmpty, !bookUrl.hasPrefix("http") {
                        if let sourceURL = URL(string: source.url) {
                            var tempBaseUrl = sourceURL.absoluteString
                            if tempBaseUrl.hasSuffix("/") {
                                tempBaseUrl.removeLast()
                            }
                            finalBookUrlString = tempBaseUrl + "/" + bookUrl
                        }
                    }
                    
                    if let coverUrl = coverUrlString, !coverUrl.isEmpty, !coverUrl.hasPrefix("http") {
                        if let sourceURL = URL(string: source.url) {
                            var tempBaseUrl = sourceURL.absoluteString
                            if tempBaseUrl.hasSuffix("/") {
                                tempBaseUrl.removeLast()
                            }
                            finalCoverUrlString = tempBaseUrl + "/" + coverUrl
                        }
                    }
                    
                    // 转换URL字符串为URL对象
                    guard let bookURL = URL(string: finalBookUrlString ?? "") else { continue }
                    let coverURL = finalCoverUrlString != nil ? URL(string: finalCoverUrlString!) : nil
                    
                    // 创建Book对象
                    let book = Book(
                        title: name,
                        author: author ?? "未知作者",
                        coverUrl: coverURL,
                        introduction: intro,
                        latestChapter: latestChapter,
                        url: bookURL,
                        sourceId: source.id
                    )
                    
                    books.append(book)
                } catch {
                    print("解析书籍元素失败: \(error)")
                    // 继续处理下一个元素
                    continue
                }
            }
        } catch {
            print("XPath解析失败: \(error)")
            throw NSError(domain: "BookSourceManager", code: 10, userInfo: [NSLocalizedDescriptionKey: "XPath解析失败: \(error.localizedDescription)"])
        }
        
        return books
    }
    
    // MARK: - 章节内容获取
    
    /// 获取章节内容
    /// - Parameters:
    ///   - chapterURL: 章节URL
    ///   - source: 书源
    /// - Returns: 章节内容
    func getChapterContent(from chapterURL: URL, using source: BookSource) async throws -> String {
        // 检查书源是否启用
        guard source.isEnabled else {
            throw NSError(domain: "BookSourceManager", code: 11, userInfo: [NSLocalizedDescriptionKey: "书源未启用"])
        }
        
        // 创建请求
        var request = URLRequest(url: chapterURL)
        request.httpMethod = "GET"
        
        // 添加自定义header
        if let headerString = source.header {
            let headerPairs = headerString.components(separatedBy: .newlines)
            for pair in headerPairs {
                let components = pair.components(separatedBy: ":")
                if components.count == 2 {
                    let key = components[0].trimmingCharacters(in: .whitespacesAndNewlines)
                    let value = components[1].trimmingCharacters(in: .whitespacesAndNewlines)
                    request.setValue(value, forHTTPHeaderField: key)
                }
            }
        }

        // 此处移除了重复的错误代码块
        
        // 设置通用header
        if request.value(forHTTPHeaderField: "User-Agent") == nil {
            request.setValue("Mozilla/5.0 (iPhone; CPU iPhone OS 16_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.0 Mobile/15E148 Safari/604.1", forHTTPHeaderField: "User-Agent")
        }
        
        // 发送请求
        let (data, response) = try await URLSession.shared.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse,
              (200...299).contains(httpResponse.statusCode) else {
            throw NSError(domain: "BookSourceManager", code: 12, userInfo: [NSLocalizedDescriptionKey: "获取章节内容失败: 状态码 \((response as? HTTPURLResponse)?.statusCode ?? 0)"])
        }
        
        // 将响应数据转换为字符串
        guard let htmlString = String(data: data, encoding: .utf8) else {
            throw NSError(domain: "BookSourceManager", code: 13, userInfo: [NSLocalizedDescriptionKey: "章节内容编码错误"])
        }
        
        // 解析章节内容
        return try await parseChapterContent(html: htmlString, source: source)
    }
    
    /// 解析章节内容
    /// - Parameters:
    ///   - html: HTML字符串
    ///   - source: 书源
    /// - Returns: 解析后的章节内容
    private func parseChapterContent(html: String, source: BookSource) async throws -> String {
        // 解析规则
        guard let ruleContentData = source.ruleContentData,
              let contentRules = try? JSONDecoder().decode([String: String].self, from: ruleContentData) else {
            throw NSError(domain: "BookSourceManager", code: 14, userInfo: [NSLocalizedDescriptionKey: "解析章节内容规则失败"])
        }
        
        // 使用HTMLParserUtils解析HTML
        guard let document = HTMLParserUtils.parse(html: html) else {
            throw NSError(domain: "BookSourceManager", code: 15, userInfo: [NSLocalizedDescriptionKey: "HTML解析失败"])
        }
        
        // 获取内容规则
        let contentRule = contentRules["content"] ?? "//div[@id='content']"
        
        do {
            // 使用XPath提取内容
            let contentNodes = try document.xpath(contentRule)
            
            if let contentNode = contentNodes.first {
                // 获取内容文本
                var content = contentNode.text.trimmingCharacters(in: .whitespacesAndNewlines)
                
                // 清理内容
                content = HTMLParserUtils.decodeHTMLEntities(content)
                
                // 替换常见的广告和无用内容
                let adPatterns = [
                    "请记住本站：.*",
                    ".*?[tT][xX][tT].*?",
                    ".*?[cC][oO][mM].*?",
                    "\\s*-->>.*?<<--\\s*",
                    "\\s*推荐下，.*",
                    "\\s*[\\[【].*?[\\]】]\\s*"
                ]
                
                for pattern in adPatterns {
                    if let regex = try? NSRegularExpression(pattern: pattern, options: []) {
                        content = regex.stringByReplacingMatches(in: content, options: [], range: NSRange(location: 0, length: content.count), withTemplate: "")
                    }
                }
                
                // 规范化换行
                content = content.replacingOccurrences(of: "\r\n", with: "\n")
                content = content.replacingOccurrences(of: "\r", with: "\n")
                
                // 移除连续空行
                while content.contains("\n\n\n") {
                    content = content.replacingOccurrences(of: "\n\n\n", with: "\n\n")
                }
                
                return content
            } else {
                throw NSError(domain: "BookSourceManager", code: 16, userInfo: [NSLocalizedDescriptionKey: "未找到章节内容"])
            }
        } catch {
            print("解析章节内容失败: \(error)")
            throw NSError(domain: "BookSourceManager", code: 17, userInfo: [NSLocalizedDescriptionKey: "解析章节内容失败: \(error.localizedDescription)"])
        }
    }
    
    // MARK: - Helper Methods (Private)
    
    /// 解码规则数据
    private func decodeRule<T: Decodable>(data: Data?) throws -> T? {
        guard let data = data else { return nil }
        let decoder = JSONDecoder()
        return try decoder.decode(T.self, from: data)
    }
    
    /// 评估规则
    private func evaluateRule(rule: String?, content: String, context: Any) -> String? {
        guard let rule = rule else { return nil }
        
        // 根据规则类型选择解析方式
        if rule.hasPrefix("//") {
            // XPath规则
            return evaluateXPathRule(rule, content: content)
        } else if rule.hasPrefix("$.") {
            // JSONPath规则
            return evaluateJSONPathRule(rule, content: content)
        } else if rule.hasPrefix("@js:") {
            // JavaScript规则
            return evaluateJavaScriptRule(rule, content: content, context: context)
        }
        
        return nil
    }
    
    /// 评估XPath规则
    private func evaluateXPathRule(_ rule: String, content: String) -> String? {
        guard let document = HTMLParserUtils.parse(html: content) else { return nil }
        return try? document.xpath(rule).first?.text
    }
    
    /// 评估JSONPath规则
    private func evaluateJSONPathRule(_ rule: String, content: String) -> String? {
        // TODO: 实现JSONPath解析
        return nil
    }
    
    /// 评估JavaScript规则
    private func evaluateJavaScriptRule(_ rule: String, content: String, context: Any) -> String? {
        guard let jsContext = JSContext() else { return nil }
        
        // 移除@js:前缀
        let jsCode = String(rule.dropFirst(4))
        
        // 设置上下文变量
        jsContext.setObject(content, forKeyedSubscript: "content" as NSString)
        if let contextDict = context as? [String: Any] {
            for (key, value) in contextDict {
                jsContext.setObject(value, forKeyedSubscript: key as NSString)
            }
        }
        
        // 执行JavaScript代码
        guard let result = jsContext.evaluateScript(jsCode)?.toString() else { return nil }
        
        return result
    }
    
    // MARK: - Methods for SourceManagerView
    
    /// 从一组索引中删除书源
    /// - Parameter indexSet: 要删除的书源的索引集合
    // 注意：deleteSources方法已在前面定义，这里不再重复定义
    
    // 注意：importFromURL方法已在前面定义，这里不再重复定义
    
    // 注意：importFromString方法已在前面定义，这里不再重复定义
    
    // MARK: - Search Result Structure
    // 注意：SearchResult结构体已在文件顶部定义，这里不再重复定义
    
    // MARK: - 章节列表获取
    
    /// 获取书籍章节列表
    /// - Parameters:
    ///   - book: 书籍对象
    ///   - source: 书源
    /// - Returns: 章节列表
    func getChapterList(for book: Book, using source: BookSource) async throws -> [Chapter] {
        // 检查书源是否启用
        guard source.isEnabled else {
            throw NSError(domain: "BookSourceManager", code: 18, userInfo: [NSLocalizedDescriptionKey: "书源未启用"])
        }
        
        // 创建请求
        var request = URLRequest(url: book.url)
        request.httpMethod = "GET"
        
        // 添加自定义header
        if let headerString = source.header {
            let headerPairs = headerString.components(separatedBy: .newlines)
            for pair in headerPairs {
                let components = pair.components(separatedBy: ":")
                if components.count == 2 {
                    let key = components[0].trimmingCharacters(in: .whitespacesAndNewlines)
                    let value = components[1].trimmingCharacters(in: .whitespacesAndNewlines)
                    request.setValue(value, forHTTPHeaderField: key)
                }
            }
        }

        // TODO: 实现实际的章节列表获取和解析逻辑
        // 目前，抛出占位符错误或返回空数组
        print("Fetching chapter list for \(book.title) using source \(source.name)")
        // 示例：模拟网络请求和解析
        // let (data, _) = try await URLSession.shared.data(for: request)
        // let chapters = try parseChapterList(from: data, for: source) // 假设有一个解析函数
        // return chapters
        
        // 根据函数签名，它应该返回 [Chapter] 或抛出错误
        // 如果是异步的，并且可能没有立即的结果，可以返回空数组或特定的错误
        // 考虑到其他 fetcher 方法返回 Optional 或 [] 作为占位符，这里也返回 []
        // 不过，由于函数声明为 throws, 我们应该抛出一个错误或者实际获取数据
        // 为了编译通过，暂时抛出错误
        throw BookSourceError.notFound // 占位符
    }
}


// MARK: - Error Handling
enum BookSourceError: Error, LocalizedError {
    case invalidURL
    case networkError(Error)
    case parsingError(String)
    case ruleNotFound(String)
    case javascriptError(String)
    case notFound
    case importFailed(String)
    case contentRuleMissing(String) // 新增，用于指示内容规则缺失
    case chapterRuleMissing(String) // 新增，用于指示章节规则缺失

    var errorDescription: String? {
        switch self {
        case .invalidURL:
            return "无效的URL"
        case .networkError(let error):
            return "网络错误: \(error.localizedDescription)"
        case .parsingError(let message):
            return "解析错误: \(message)"
        case .ruleNotFound(let ruleName):
            return "未找到规则: \(ruleName)"
        case .javascriptError(let message):
            return "JavaScript执行错误: \(message)"
        case .notFound:
            return "未找到相关项目"
        case .importFailed(let message):
            return "导入失败: \(message)"
        case .contentRuleMissing(let ruleName):
            return "内容规则缺失: \(ruleName)"
        case .chapterRuleMissing(let ruleName):
            return "章节规则缺失: \(ruleName)"
        }
    }
}