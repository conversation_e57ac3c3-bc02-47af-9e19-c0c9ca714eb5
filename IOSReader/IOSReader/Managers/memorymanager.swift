import Foundation
import SwiftUI

/// 内存管理器
/// 主要功能：
/// 1. 监控应用内存使用情况
/// 2. 在内存压力大时主动清理资源
/// 3. 当内存无法控制时优雅退出应用
@MainActor
class MemoryManager: ObservableObject {
    static let shared = MemoryManager()
    
    private let memoryWarningThreshold: Double = 0.8 // 内存警告阈值（80%）
    private let memoryExitThreshold: Double = 0.9 // 内存退出阈值（90%）
    private var isMonitoring = false
    
    private init() {
        // 注册内存警告通知
        Task { @MainActor in
            for await _ in NotificationCenter.default.notifications(named: .memoryWarningReceived) {
                await handleMemoryWarning()
            }
        }
    }
    
    deinit {
        // 在deinit中不应该捕获self
        NotificationCenter.default.removeObserver(self)
    }
    
    /// 开始内存监控
    func startMonitoring() {
        guard !isMonitoring else { return }
        isMonitoring = true
        
        // 启动定期检查
        Timer.scheduledTimer(withTimeInterval: 5.0, repeats: true) { [weak self] _ in
            Task { @MainActor in
                await self?.checkMemoryUsage()
            }
        }
    }
    
    /// 停止内存监控
    func stopMonitoring() {
        isMonitoring = false
    }
    
    /// 获取当前内存使用情况
    public func getMemoryUsage() -> Double {
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size)/4
        
        let kerr: kern_return_t = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_,
                          task_flavor_t(MACH_TASK_BASIC_INFO),
                          $0,
                          &count)
            }
        }
        
        do {
            if kerr == KERN_SUCCESS {
                let usedBytes = Double(info.resident_size)
                let totalBytes = Double(ProcessInfo.processInfo.physicalMemory)
                return usedBytes / totalBytes
            } else {
                throw NSError(domain: "MemoryError", code: Int(kerr), userInfo: [NSLocalizedDescriptionKey: "Failed to get memory info"])
            }
        } catch {
            print("Error getting memory usage: \(error)")
            return 0.0
        }
    }
    
    /// 检查内存使用情况
    private func checkMemoryUsage() async {
        let memoryUsage = getMemoryUsage()
        
        if memoryUsage >= memoryExitThreshold {
            // 内存使用超过退出阈值，准备退出应用
            await handleExcessiveMemoryUsage()
        } else if memoryUsage >= memoryWarningThreshold {
            // 内存使用超过警告阈值，清理资源
            await cleanupMemory()
        }
    }
    
    /// 处理内存警告
    private func handleMemoryWarning() async {
        await cleanupMemory()
    }
    
    /// 清理内存资源
    private func cleanupMemory() async {
        do {
            // 清理图片缓存
            URLCache.shared.removeAllCachedResponses()
            
            // 清理SwiftUI视图状态
            try await cleanupViewState()
            
            // 强制进行垃圾回收
            await Task.yield()
            autoreleasepool {
                // 执行一些内存密集型操作
            }
        } catch {
            print("清理内存资源时发生错误: \(error)")
        }
    }
    
    /// 清理SwiftUI视图状态
    private func cleanupViewState() async throws {
        // 发送清理通知
        NotificationCenter.default.post(name: .memoryCleanupRequired, object: nil)
        
        // 等待清理完成
        try await Task.sleep(nanoseconds: 100_000_000) // 100ms
    }
    
    /// 处理过度内存使用
    private func handleExcessiveMemoryUsage() async {
        // 1. 尝试最后的内存清理
        await cleanupMemory()
        
        // 2. 保存必要的状态
        saveApplicationState()
        
        // 3. 显示提示并退出
        await showExitAlert()
    }
    
    /// 保存应用状态
    private func saveApplicationState() {
        // 保存用户数据和阅读进度
        NotificationCenter.default.post(
            name: Notification.Name("SaveApplicationState"),
            object: nil
        )
    }
    
    /// 显示退出提示
    private func showExitAlert() async {
        // 发送退出通知
        NotificationCenter.default.post(
            name: .applicationExitRequired,
            object: nil,
            userInfo: ["reason": "内存不足，需要退出以保护系统稳定性。请重新启动应用。"]
        )
        
        // 延迟执行以确保通知被处理
        try? await Task.sleep(nanoseconds: 500_000_000) // 500ms
        exit(0)
    }
}

// MARK: - 通知扩展

extension Notification.Name {
    static let memoryWarningReceived = Notification.Name("memoryWarningReceived")
    static let memoryCleanupRequired = Notification.Name("memoryCleanupRequired")
    static let applicationExitRequired = Notification.Name("applicationExitRequired")
}

/// 可清理内存的协议
protocol MemoryCleanable {
    /// 清理内存资源
    func cleanupMemory() async
}