//
//  BackupManager.swift
//  IOSReader
//
//  备份管理器类
//  负责处理应用数据的备份和恢复功能

import Foundation
import Combine
// 导入 Zip 库
import ZIPFoundation

/// 备份位置枚举
enum BackupLocation: String, CaseIterable, Identifiable {
    case local = "本地备份"   // 本地备份
    case webDAV = "WebDAV备份"  // WebDAV备份
    
    var id: String { rawValue }
}

/// 备份管理器类
/// 负责处理应用数据的备份和恢复操作
class BackupManager {
    // MARK: - 属性
    private let fileManager = FileManager.default
    private let documentsDirectory: URL
    private let backupDirectory: URL
    private let dataToBackupDirectory: URL // 假设需要备份的数据都在这个目录下

    // MARK: - 初始化
    init() {
        // 获取应用文档目录
        documentsDirectory = fileManager.urls(for: .documentDirectory, in: .userDomainMask).first!
        backupDirectory = documentsDirectory.appendingPathComponent("Backups", isDirectory: true)
        // 假设所有需要备份的数据都存储在 Documents/AppData 目录下
        dataToBackupDirectory = documentsDirectory.appendingPathComponent("AppData", isDirectory: true)

        // 确保备份目录和数据目录存在（如果数据目录是新建的）
        try? fileManager.createDirectory(at: backupDirectory, withIntermediateDirectories: true)
        try? fileManager.createDirectory(at: dataToBackupDirectory, withIntermediateDirectories: true)
        // TODO: 在此处创建或确认实际需要备份的文件/子目录结构
    }

    // MARK: - 公共方法

    /// 执行备份操作
    /// - Parameters:
    ///   - location: 备份位置（本地或WebDAV）
    ///   - items: 需要备份的项目 (来自 BackupView.BackupItem)
    /// - Returns: 备份是否成功
    func performBackup(to location: BackupLocation, items: Set<BackupView.BackupItem>) -> Bool {
        do {
            // 1. 创建临时备份目录
            let tempBackupDir = backupDirectory.appendingPathComponent("TempBackup_\(UUID().uuidString)", isDirectory: true)
            try fileManager.createDirectory(at: tempBackupDir, withIntermediateDirectories: true)
            defer {
                // 清理临时目录
                try? fileManager.removeItem(at: tempBackupDir)
            }

            // 2. 根据选择的项目收集数据到临时目录
            try collectDataForBackup(items: items, to: tempBackupDir)

            // 3. 创建备份文件名
            let timestamp = DateFormatter.localizedString(from: Date(), dateStyle: .short, timeStyle: .medium)
                .replacingOccurrences(of: ":", with: "-")
                .replacingOccurrences(of: "/", with: "-")
                .replacingOccurrences(of: " ", with: "_") // 处理空格
            let backupFileName = "IOSReader_Backup_\(timestamp).zip"
            let backupFileURL = backupDirectory.appendingPathComponent(backupFileName)

            // 4. 压缩临时目录内容到最终备份文件
            // 实现压缩逻辑 (使用 ZIPFoundation)
            do {
                // 使用FileManager扩展方法进行压缩
                try fileManager.zipItem(at: tempBackupDir, to: backupFileURL)
                print("已压缩备份文件至: \(backupFileURL.path)")
            } catch {
                print("压缩失败: \(error.localizedDescription)")
                throw error // 抛出错误以便上层捕获
            }
            // 移除模拟代码
            // print("模拟压缩: \(tempBackupDir.path) -> \(backupFileURL.path)") // 模拟
            // try? Data().write(to: backupFileURL)

            // 5. 根据位置处理备份文件 (本地已保存，WebDAV需上传)
            switch location {
            case .local:
                print("本地备份已保存至: (backupFileURL.path)")
                return true // 本地备份在压缩后即完成
            case .webDAV:
                return try performWebDAVUpload(backupFile: backupFileURL)
            }
        } catch {
            print("备份失败: (error.localizedDescription)")
            return false
        }
    }

    /// 执行恢复操作
    /// - Parameters:
    ///   - location: 恢复位置（本地或WebDAV）
    ///   - backupFileURL: 要恢复的本地备份文件URL (本地恢复时提供)
    /// - Returns: 恢复是否成功
    func performRestore(from location: BackupLocation, backupFileURL: URL? = nil) -> Bool {
        do {
            var sourceBackupURL: URL

            // 1. 获取备份文件URL
            switch location {
            case .local:
                guard let url = backupFileURL else {
                    print("本地恢复错误: 未提供备份文件URL")
                    return false
                }
                // 确认本地文件存在
                guard fileManager.fileExists(atPath: url.path) else {
                    print("本地恢复错误: 备份文件不存在于 \(url.path)")
                    return false
                }
                sourceBackupURL = url
                print("准备从本地文件恢复: \(sourceBackupURL.path)")

            case .webDAV:
                // TODO: 实现从WebDAV下载备份文件的逻辑
                // 需要用户选择或指定要下载的文件名
                // sourceBackupURL = try performWebDAVDownload(fileName: "chosen_backup.zip")
                print("WebDAV恢复尚未实现")
                return false // 暂时返回失败
            }

            // 2. 创建临时解压目录
            let tempRestoreDir = documentsDirectory.appendingPathComponent("TempRestore_\(UUID().uuidString)", isDirectory: true)
            try fileManager.createDirectory(at: tempRestoreDir, withIntermediateDirectories: true)
            defer {
                // 清理临时目录
                try? fileManager.removeItem(at: tempRestoreDir)
            }

            // 3. 解压备份文件到临时目录
            // 实现解压逻辑 (使用 ZIPFoundation)
            do {
                // 使用FileManager扩展方法进行解压
                try fileManager.unzipItem(at: sourceBackupURL, to: tempRestoreDir)
                print("已解压备份文件到: \(tempRestoreDir.path)")
            } catch {
                print("解压失败: \(error.localizedDescription)")
                throw error // 抛出错误
            }
            // 移除模拟代码
            // print("模拟解压: \(sourceBackupURL.path) -> \(tempRestoreDir.path)") // 模拟
            // try? "SimulatedData".data(using: .utf8)?.write(to: tempRestoreDir.appendingPathComponent("simulated_data.txt"))

            // 4. 清理当前数据目录 (或根据恢复策略合并)
            // **警告:** 这是一个危险操作，确保用户知晓会覆盖现有数据
            // 可以考虑先备份当前数据
            print("警告: 即将覆盖现有应用数据!")
            // 实际清理操作 - 谨慎使用
            // try? fileManager.removeItem(at: dataToBackupDirectory)
            // try? fileManager.createDirectory(at: dataToBackupDirectory, withIntermediateDirectories: true)
            print("模拟清理当前数据目录: \(dataToBackupDirectory.path)") // 保留模拟清理，避免意外删除

            // 5. 将解压后的数据移动到实际数据目录
            // 实现将 tempRestoreDir 内容移动/复制到 dataToBackupDirectory 的逻辑
            let restoredItems = try fileManager.contentsOfDirectory(at: tempRestoreDir, includingPropertiesForKeys: nil)
            for itemURL in restoredItems {
                let destinationURL = dataToBackupDirectory.appendingPathComponent(itemURL.lastPathComponent)
                // 如果目标已存在，先删除 (确保覆盖)
                if fileManager.fileExists(atPath: destinationURL.path) {
                    // 检查是文件还是目录，目录需要递归删除
                    var isDir: ObjCBool = false
                    if fileManager.fileExists(atPath: destinationURL.path, isDirectory: &isDir) {
                        try fileManager.removeItem(at: destinationURL)
                        print("已删除现有项目: \(destinationURL.path)")
                    } else {
                        // 如果 fileExists 返回 true 但 isDirectory 检查失败，可能存在问题
                        print("警告: 无法确定现有项目类型，跳过删除: \(destinationURL.path)")
                        continue // 跳过此项的移动
                    }
                }
                try fileManager.moveItem(at: itemURL, to: destinationURL)
                print("已恢复项目: \(itemURL.lastPathComponent) -> \(destinationURL.path)")
            }

            print("恢复成功完成")
            return true

        } catch {
            print("恢复失败: \(error.localizedDescription)")
            return false
        }
    }

    // MARK: - 私有辅助方法

    /// 根据选择的项目收集数据到临时备份目录
    private func collectDataForBackup(items: Set<BackupView.BackupItem>, to tempDir: URL) throws {
        print("开始收集备份数据项: \(items.map { $0.rawValue })")

        // 定义 IOSreader 数据目录的基础路径
        let iosReaderDataDirectory = documentsDirectory.appendingPathComponent("IOSreader", isDirectory: true)
        // 用于跟踪需要复制的源 URL，避免重复
        var sourceURLsToCopy: Set<URL> = []
        // 标记 setting.json 是否需要复制
        var shouldCopySettingsJson = false

        for item in items {
            switch item {
            case .bookSources:
                // 书源文件
                sourceURLsToCopy.insert(iosReaderDataDirectory.appendingPathComponent("bookSources.json"))
            case .bookshelf:
                // 书架数据在 setting.json 中，同时需要备份书籍文件
                shouldCopySettingsJson = true
                // 假设书籍文件存储在 Document/IOSreader/Books/ 目录下
                sourceURLsToCopy.insert(iosReaderDataDirectory.appendingPathComponent("Books", isDirectory: true))
            case .readingProgress:
                // 阅读进度在 setting.json 中
                shouldCopySettingsJson = true
            case .theme:
                // 主题数据在 setting.json 中，同时需要备份主题文件
                shouldCopySettingsJson = true
                // 假设主题文件存储在 Document/IOSreader/Themes/ 目录下
                sourceURLsToCopy.insert(iosReaderDataDirectory.appendingPathComponent("Themes", isDirectory: true))
            case .readingSettings:
                // 阅读设置在 setting.json 中
                shouldCopySettingsJson = true
            case .accountSettings:
                // 账户设置在 setting.json 中
                shouldCopySettingsJson = true
            }
        }

        // 如果有任何依赖 setting.json 的项被选中，则添加 setting.json 到复制列表
        if shouldCopySettingsJson {
            sourceURLsToCopy.insert(iosReaderDataDirectory.appendingPathComponent("setting.json"))
        }

        // 遍历需要复制的 URL 并执行复制操作
        for sourceURL in sourceURLsToCopy {
            // 计算目标路径，保持相对结构
            // 例如，将 Document/IOSreader/setting.json 复制到 TempDir/setting.json
            // 将 Document/IOSreader/Books/ 复制到 TempDir/Books/
            let relativePath = sourceURL.path.replacingOccurrences(of: iosReaderDataDirectory.path, with: "")
            let destinationURL = tempDir.appendingPathComponent(relativePath)

            if fileManager.fileExists(atPath: sourceURL.path) {
                do {
                    // 确保目标目录结构存在
                    try fileManager.createDirectory(at: destinationURL.deletingLastPathComponent(), withIntermediateDirectories: true)
                    // 复制文件或目录
                    try fileManager.copyItem(at: sourceURL, to: destinationURL)
                    print("已收集: \(relativePath.isEmpty ? sourceURL.lastPathComponent : relativePath)")
                } catch {
                    print("警告: 复制失败 \(sourceURL.path) 到 \(destinationURL.path): \(error.localizedDescription)")
                    // 可以选择继续或抛出错误停止备份
                    // throw error // 如果希望一个文件失败导致整个备份失败，取消注释此行
                }
            } else {
                print("警告: 未找到备份项的数据: \(sourceURL.path)")
            }
        }

        print("数据收集完成")
    }

    /// 上传备份文件到WebDAV
    private func performWebDAVUpload(backupFile: URL) throws -> Bool {
        // TODO: 从安全存储中获取WebDAV配置 (URL, 用户名, 密码)
        guard let webDAVURLString = UserDefaults.standard.string(forKey: "webDAVURL"), // 示例：从UserDefaults获取
              let webDAVURL = URL(string: webDAVURLString),
              let username = UserDefaults.standard.string(forKey: "webDAVUsername"),
              let password = UserDefaults.standard.string(forKey: "webDAVPassword") else {
            print("WebDAV配置不完整或无效")
            throw BackupError.webDAVConfigurationMissing
        }

        // 目标URL，通常是 WebDAV URL + 文件名
        let destinationURL = webDAVURL.appendingPathComponent(backupFile.lastPathComponent)

        var request = URLRequest(url: destinationURL)
        request.httpMethod = "PUT"

        // 添加 Basic Authentication 头
        let loginString = "\(username):\(password)"
        guard let loginData = loginString.data(using: .utf8) else {
            print("无法编码WebDAV凭据")
            throw BackupError.webDAVAuthenticationError
        }
        let base64LoginString = loginData.base64EncodedString()
        request.setValue("Basic \(base64LoginString)", forHTTPHeaderField: "Authorization")

        // 设置请求体 (文件数据)
        let fileData = try Data(contentsOf: backupFile)
        request.httpBody = fileData
        request.setValue("\(fileData.count)", forHTTPHeaderField: "Content-Length")

        // 使用 URLSession 发送请求 (同步执行以简化示例，实际应用建议异步)
        let semaphore = DispatchSemaphore(value: 0)
        var uploadError: Error?
        var success = false

        let task = URLSession.shared.dataTask(with: request) { data, response, error in
            defer { semaphore.signal() }
            if let error = error {
                print("WebDAV上传错误: \(error.localizedDescription)")
                uploadError = error
                return
            }
            guard let httpResponse = response as? HTTPURLResponse else {
                print("无效的WebDAV响应")
                uploadError = BackupError.webDAVError(message: "无效的响应")
                return
            }

            // 检查状态码 (201 Created, 204 No Content 通常表示成功)
            if (200...299).contains(httpResponse.statusCode) {
                print("WebDAV上传成功: \(httpResponse.statusCode)")
                success = true
            } else {
                print("WebDAV上传失败: 状态码 \(httpResponse.statusCode)")
                // 可以尝试解析响应体获取更详细错误信息
                var errorMessage = "HTTP状态码: \(httpResponse.statusCode)"
                if let responseData = data, let responseString = String(data: responseData, encoding: .utf8) {
                    errorMessage += " - \(responseString)"
                }
                uploadError = BackupError.webDAVError(message: errorMessage)
            }
        }
        task.resume()
        semaphore.wait() // 等待任务完成

        if let error = uploadError {
            throw error
        }

        return success
    }

    /// 从WebDAV下载备份文件
    private func performWebDAVDownload(fileName: String) throws -> URL {
        // TODO: 从安全存储中获取WebDAV配置
        guard let webDAVURLString = UserDefaults.standard.string(forKey: "webDAVURL"),
              let webDAVURL = URL(string: webDAVURLString),
              let username = UserDefaults.standard.string(forKey: "webDAVUsername"),
              let password = UserDefaults.standard.string(forKey: "webDAVPassword") else {
            print("WebDAV配置不完整或无效")
            throw BackupError.webDAVConfigurationMissing
        }

        // 要下载的文件URL
        let sourceURL = webDAVURL.appendingPathComponent(fileName)
        // 本地临时存储位置
        let tempDownloadURL = backupDirectory.appendingPathComponent("Downloaded_\(fileName)")

        var request = URLRequest(url: sourceURL)
        request.httpMethod = "GET"

        // 添加 Basic Authentication 头
        let loginString = "\(username):\(password)"
        guard let loginData = loginString.data(using: .utf8) else {
            print("无法编码WebDAV凭据")
            throw BackupError.webDAVAuthenticationError
        }
        let base64LoginString = loginData.base64EncodedString()
        request.setValue("Basic \(base64LoginString)", forHTTPHeaderField: "Authorization")

        // 使用 URLSession 下载 (同步执行)
        let semaphore = DispatchSemaphore(value: 0)
        var downloadError: Error?
        var downloadedFileURL: URL?

        let task = URLSession.shared.downloadTask(with: request) { location, response, error in
            defer { semaphore.signal() }
            if let error = error {
                print("WebDAV下载错误: \(error.localizedDescription)")
                downloadError = error
                return
            }
            guard let httpResponse = response as? HTTPURLResponse, (200...299).contains(httpResponse.statusCode) else {
                let statusCode = (response as? HTTPURLResponse)?.statusCode ?? -1
                print("WebDAV下载失败: 状态码 \(statusCode)")
                downloadError = BackupError.webDAVError(message: "HTTP状态码: \(statusCode)")
                return
            }
            guard let tempLocalURL = location else {
                print("WebDAV下载错误: 未找到临时文件位置")
                downloadError = BackupError.webDAVError(message: "未找到临时文件位置")
                return
            }

            // 将下载的临时文件移动到目标位置
            do {
                // 如果目标文件已存在，先删除
                if self.fileManager.fileExists(atPath: tempDownloadURL.path) {
                    try self.fileManager.removeItem(at: tempDownloadURL)
                }
                try self.fileManager.moveItem(at: tempLocalURL, to: tempDownloadURL)
                print("WebDAV文件已下载到: \(tempDownloadURL.path)")
                downloadedFileURL = tempDownloadURL
            } catch let moveError {
                print("移动WebDAV下载文件失败: \(moveError.localizedDescription)")
                downloadError = moveError
            }
        }
        task.resume()
        semaphore.wait()

        if let error = downloadError {
            throw error
        }
        guard let finalURL = downloadedFileURL else {
            // 如果没有错误但URL为空，说明下载流程内部逻辑有问题
            throw BackupError.webDAVError(message: "下载成功但未能获取文件URL")
        }

        return finalURL
    }

    // MARK: - (旧的私有方法 - 已被重构)
    /*
    /// 执行本地备份 (旧)
    private func performLocalBackup() throws -> Bool {
        // ... 旧代码 ...
        return true
    }

    /// 执行WebDAV备份 (旧)
    private func performWebDAVBackup() throws -> Bool {
        // ... 旧代码 ...
        return true
    }

    /// 执行本地恢复 (旧)
    private func performLocalRestore() throws -> Bool {
        // ... 旧代码 ...
        return true
    }

    /// 执行WebDAV恢复 (旧)
    private func performWebDAVRestore() throws -> Bool {
        // ... 旧代码 ...
        return true
    }
     */
}

// MARK: - 错误定义
enum BackupError: Error, LocalizedError {
    case webDAVConfigurationMissing
    case webDAVAuthenticationError
    case webDAVError(message: String)
    case compressionFailed
    case decompressionFailed
    case fileOperationFailed(message: String)

    var errorDescription: String? {
        switch self {
        case .webDAVConfigurationMissing: return "WebDAV 配置不完整或无效。"
        case .webDAVAuthenticationError: return "无法编码 WebDAV 凭据。"
        case .webDAVError(let message): return "WebDAV 操作失败: \(message)"
        case .compressionFailed: return "压缩备份文件失败。"
        case .decompressionFailed: return "解压备份文件失败。"
        case .fileOperationFailed(let message): return "文件操作失败: \(message)"
        }
    }
}

// MARK: - 扩展
// 如果 BackupItem 定义在 BackupView 内部，可能需要访问
extension BackupView {
    // 如果 BackupItem 定义在 BackupView 内部，需要这样访问
    // 如果是全局或共享模型，则不需要这个扩展
}