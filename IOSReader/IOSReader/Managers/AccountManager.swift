//
//  AccountManager.swift
//  IOSReader
//
//  账号管理器
//  功能:
//  - 用户注册、登录、登出管理
//  - 密码加密和验证
//  - 用户会话管理
//  - 账号状态监控
//  - 与SwiftData集成的用户数据管理
//

import Foundation
import SwiftData
import CryptoKit
import Combine

/// 账号管理器
/// 负责用户认证和账号管理的核心逻辑
@MainActor
class AccountManager: ObservableObject, @unchecked Sendable {
    /// 当前登录用户
    private(set) var currentUser: User?
    
    /// 登录状态
    private(set) var isLoggedIn: Bool = false
    
    /// 数据模型容器
    private let modelContainer: ModelContainer
    
    /// 用户默认存储
    private let userDefaults = UserDefaults.standard
    
    /// 当前用户ID键
    private let currentUserIdKey = "CurrentUserId"
    
    /// 记住登录状态键
    private let rememberLoginKey = "RememberLogin"
    
    /// 初始化账号管理器
    /// - Parameter modelContainer: SwiftData模型容器
    init(modelContainer: ModelContainer) {
        self.modelContainer = modelContainer
        
        // 检查是否有保存的登录状态
        if userDefaults.bool(forKey: rememberLoginKey),
           let userIdString = userDefaults.string(forKey: currentUserIdKey),
           let userId = UUID(uuidString: userIdString) {
            Task {
                await loadCurrentUser(userId: userId)
            }
        }
    }
    
    // MARK: - 用户注册
    
    /// 注册新用户
    /// - Parameters:
    ///   - username: 用户名
    ///   - email: 邮箱地址
    ///   - password: 密码
    ///   - nickname: 昵称
    /// - Returns: 注册结果
    func register(username: String, email: String, password: String, nickname: String) async throws -> RegistrationResult {
        // 验证输入
        try validateRegistrationInput(username: username, email: email, password: password)
        
        // 检查用户名和邮箱是否已存在
        if await isUsernameExists(username) {
            throw AccountError.usernameAlreadyExists
        }
        
        if await isEmailExists(email) {
            throw AccountError.emailAlreadyExists
        }
        
        // 加密密码
        let passwordHash = hashPassword(password)
        
        // 创建新用户
        let newUser = User(username: username, email: email, passwordHash: passwordHash, nickname: nickname)
        
        // 保存到数据库
        let context = ModelContext(modelContainer)
        context.insert(newUser)
        
        do {
            try context.save()
            return .success(newUser)
        } catch {
            throw AccountError.registrationFailed(error.localizedDescription)
        }
    }
    
    // MARK: - 用户登录
    
    /// 用户登录
    /// - Parameters:
    ///   - identifier: 用户名或邮箱
    ///   - password: 密码
    ///   - rememberMe: 是否记住登录状态
    /// - Returns: 登录结果
    func login(identifier: String, password: String, rememberMe: Bool = false) async throws -> LoginResult {
        // 验证输入
        guard !identifier.isEmpty, !password.isEmpty else {
            throw AccountError.invalidCredentials
        }
        
        // 查找用户
        guard let user = await findUser(by: identifier) else {
            throw AccountError.userNotFound
        }
        
        // 检查账号状态
        guard user.accountStatus == .active else {
            throw AccountError.accountSuspended
        }
        
        // 验证密码
        guard verifyPassword(password, hash: user.passwordHash) else {
            throw AccountError.invalidCredentials
        }
        
        // 更新登录时间
        user.updateLastLoginDate()
        
        // 保存更新
        let context = ModelContext(modelContainer)
        try context.save()
        
        // 设置当前用户
        currentUser = user
        isLoggedIn = true
        
        // 保存登录状态
        if rememberMe {
            userDefaults.set(user.id.uuidString, forKey: currentUserIdKey)
            userDefaults.set(true, forKey: rememberLoginKey)
        }
        
        return .success(user)
    }
    
    // MARK: - 用户登出
    
    /// 用户登出
    func logout() {
        currentUser = nil
        isLoggedIn = false
        
        // 清除保存的登录状态
        userDefaults.removeObject(forKey: currentUserIdKey)
        userDefaults.set(false, forKey: rememberLoginKey)
    }
    
    // MARK: - 密码管理
    
    /// 修改密码
    /// - Parameters:
    ///   - oldPassword: 旧密码
    ///   - newPassword: 新密码
    func changePassword(oldPassword: String, newPassword: String) async throws {
        guard let user = currentUser else {
            throw AccountError.notLoggedIn
        }
        
        // 验证旧密码
        guard verifyPassword(oldPassword, hash: user.passwordHash) else {
            throw AccountError.invalidCredentials
        }
        
        // 验证新密码
        try validatePassword(newPassword)
        
        // 更新密码
        user.passwordHash = hashPassword(newPassword)
        
        // 保存更新
        let context = ModelContext(modelContainer)
        try context.save()
    }
    
    /// 重置密码（通过邮箱）
    /// - Parameter email: 邮箱地址
    func resetPassword(email: String) async throws {
        guard await isEmailExists(email) else {
            throw AccountError.emailNotFound
        }
        
        // TODO: 实现邮箱验证和密码重置逻辑
        // 这里应该发送重置邮件，包含重置链接或验证码
        print("密码重置邮件已发送到: \(email)")
    }
    
    // MARK: - 用户信息管理
    
    /// 更新用户资料
    /// - Parameters:
    ///   - nickname: 新昵称
    ///   - avatarPath: 新头像路径
    func updateProfile(nickname: String? = nil, avatarPath: String? = nil) async throws {
        guard let user = currentUser else {
            throw AccountError.notLoggedIn
        }
        
        user.updateProfile(nickname: nickname, avatarPath: avatarPath)
        
        let context = ModelContext(modelContainer)
        try context.save()
    }
    
    /// 删除账号
    func deleteAccount() async throws {
        guard let user = currentUser else {
            throw AccountError.notLoggedIn
        }
        
        let context = ModelContext(modelContainer)
        context.delete(user)
        try context.save()
        
        // 登出
        await logout()
    }
    
    /// 用户登出
    func logout() async {
        // 清除当前用户信息
        currentUser = nil
        isLoggedIn = false
        
        // 清除持久化的登录状态
        userDefaults.removeObject(forKey: currentUserIdKey)
        userDefaults.removeObject(forKey: rememberLoginKey)
        userDefaults.synchronize()
    }
    
    // MARK: - 私有方法
    
    /// 加载当前用户
    private func loadCurrentUser(userId: UUID) async {
        let context = ModelContext(modelContainer)
        let descriptor = FetchDescriptor<User>(predicate: #Predicate { $0.id == userId })
        
        do {
            let users = try context.fetch(descriptor)
            if let user = users.first {
                currentUser = user
                isLoggedIn = true
            }
        } catch {
            print("加载用户失败: \(error)")
        }
    }
    
    /// 查找用户（通过用户名或邮箱）
    private func findUser(by identifier: String) async -> User? {
        let context = ModelContext(modelContainer)
        let descriptor = FetchDescriptor<User>(
            predicate: #Predicate { user in
                user.username == identifier || user.email == identifier
            }
        )
        
        do {
            let users = try context.fetch(descriptor)
            return users.first
        } catch {
            print("查找用户失败: \(error)")
            return nil
        }
    }
    
    /// 检查用户名是否存在
    private func isUsernameExists(_ username: String) async -> Bool {
        let context = ModelContext(modelContainer)
        let descriptor = FetchDescriptor<User>(predicate: #Predicate { $0.username == username })
        
        do {
            let users = try context.fetch(descriptor)
            return !users.isEmpty
        } catch {
            return false
        }
    }
    
    /// 检查邮箱是否存在
    private func isEmailExists(_ email: String) async -> Bool {
        let context = ModelContext(modelContainer)
        let descriptor = FetchDescriptor<User>(predicate: #Predicate { $0.email == email })
        
        do {
            let users = try context.fetch(descriptor)
            return !users.isEmpty
        } catch {
            return false
        }
    }
    
    /// 密码加密
    private func hashPassword(_ password: String) -> String {
        let data = Data(password.utf8)
        let hash = SHA256.hash(data: data)
        return hash.compactMap { String(format: "%02x", $0) }.joined()
    }
    
    /// 密码验证
    private func verifyPassword(_ password: String, hash: String) -> Bool {
        return hashPassword(password) == hash
    }
    
    /// 验证注册输入
    private func validateRegistrationInput(username: String, email: String, password: String) throws {
        // 验证用户名
        guard username.count >= 3, username.count <= 20 else {
            throw AccountError.invalidUsername
        }
        
        // 验证邮箱格式
        guard isValidEmail(email) else {
            throw AccountError.invalidEmail
        }
        
        // 验证密码
        try validatePassword(password)
    }
    
    /// 验证密码强度
    private func validatePassword(_ password: String) throws {
        guard password.count >= 6 else {
            throw AccountError.passwordTooShort
        }
        
        guard password.count <= 50 else {
            throw AccountError.passwordTooLong
        }
    }
    
    /// 验证邮箱格式
    private func isValidEmail(_ email: String) -> Bool {
        let emailRegex = "^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$"
        let emailPredicate = NSPredicate(format: "SELF MATCHES %@", emailRegex)
        return emailPredicate.evaluate(with: email)
    }
}

// MARK: - 结果类型

/// 注册结果
enum RegistrationResult {
    case success(User)
    case failure(AccountError)
}

/// 登录结果
enum LoginResult {
    case success(User)
    case failure(AccountError)
}

// MARK: - 错误类型

/// 账号错误类型
enum AccountError: LocalizedError {
    case invalidUsername
    case invalidEmail
    case passwordTooShort
    case passwordTooLong
    case usernameAlreadyExists
    case emailAlreadyExists
    case userNotFound
    case emailNotFound
    case invalidCredentials
    case accountSuspended
    case notLoggedIn
    case registrationFailed(String)
    
    var errorDescription: String? {
        switch self {
        case .invalidUsername:
            return "用户名长度必须在3-20个字符之间"
        case .invalidEmail:
            return "邮箱格式不正确"
        case .passwordTooShort:
            return "密码长度不能少于6个字符"
        case .passwordTooLong:
            return "密码长度不能超过50个字符"
        case .usernameAlreadyExists:
            return "用户名已存在"
        case .emailAlreadyExists:
            return "邮箱已被注册"
        case .userNotFound:
            return "用户不存在"
        case .emailNotFound:
            return "邮箱地址不存在"
        case .invalidCredentials:
            return "用户名或密码错误"
        case .accountSuspended:
            return "账号已被暂停或封禁"
        case .notLoggedIn:
            return "请先登录"
        case .registrationFailed(let message):
            return "注册失败: \(message)"
        }
    }
}