//
// AdvancedFeatures.swift
// IOSReader
//
// 高级功能模块，主要实现以下三大核心功能：
// 1. 朗读引擎：提供本地TTS和云端TTS服务（支持Azure和Google）
// 2. RSS订阅：将RSS文章转换为EPUB格式并支持关键词预警
// 3. 数据同步：通过WebDAV协议实现本地与服务器文件同步
//
// 主要类和方法：
// - speak(text:voice:): 基础朗读功能
// - cloudTTS(text:service:apiKey:): 云TTS服务接口
// - convertRSSToEPUB(rssURL:): 将RSS转换为EPUB
// - setupKeywordAlert(keywords:rssURL:): 关键词预警通知
// - syncWithWebDAV(serverURL:credentials:): WebDAV同步
// - syncReadingPosition(bookID:paragraphIndex:): 阅读位置同步
//

import Foundation
import AVFoundation
import AVKit
import UserNotifications
import SwiftUI

// MARK: - RSS Parser and EPUB Generator (Top-Level)

class RSSEPUBParser: NSObject, XMLParserDelegate {
    var articles: [RSSArticle] = []
    var currentElement = ""
    var currentTitle = ""
    var currentContent = ""
    
    struct RSSArticle {
        var title: String
        var content: String
    }
    
    func parser(_ parser: XMLParser, didStartElement elementName: String, namespaceURI: String?, qualifiedName qName: String?, attributes attributeDict: [String : String] = [:]) {
        currentElement = elementName
        if elementName == "item" {
            currentTitle = ""
            currentContent = ""
        }
    }
    
    func parser(_ parser: XMLParser, foundCharacters string: String) {
        if currentElement == "title" {
            currentTitle += string
        } else if currentElement == "description" {
            currentContent += string
        }
    }
    
    func parser(_ parser: XMLParser, didEndElement elementName: String, namespaceURI: String?, qualifiedName qName: String?) {
        if elementName == "item" {
            articles.append(RSSArticle(title: currentTitle, content: currentContent))
        }
        currentElement = ""
    }
}

class EPUBGenerator {
    private let articles: [Article]
    
    struct Article {
        var title: String
        var content: String
    }
    
    init(articles: [Article]) {
        self.articles = articles
    }
    
    func generate(to url: URL) throws {
        // TODO: 实现EPUB生成逻辑
        // 这是一个占位符实现，你需要根据实际需求填充
        let epubContent = articles.map { "<h1>\($0.title)</h1><p>\($0.content)</p>" }.joined(separator: "<hr/>")
        let fullHTML = "<html><head><title>RSS EPUB</title></head><body>\(epubContent)</body></html>"
        try fullHTML.write(to: url, atomically: true, encoding: .utf8)
        print("Mock EPUB generated at: \(url.path)")
    }
}

class AdvancedFeatures {
    
    // MARK: - 朗读引擎
    private let speechSynthesizer = AVSpeechSynthesizer()
    
    /// 基础朗读功能
    func speak(text: String, voice: AVSpeechSynthesisVoice? = nil) {
        let utterance = AVSpeechUtterance(string: text)
        utterance.voice = voice ?? AVSpeechSynthesisVoice(language: "zh-CN")
        speechSynthesizer.speak(utterance)
    }
    
    /// 云TTS服务接口
    func cloudTTS(text: String, service: CloudTTSService, apiKey: String) {
        let session = URLSession.shared
        var request: URLRequest
        
        switch service {
        case .azure:
            let endpoint = "https://eastus.tts.speech.microsoft.com/cognitiveservices/v1"
            request = URLRequest(url: URL(string: endpoint)!)
            request.httpMethod = "POST"
            request.setValue("application/ssml+xml", forHTTPHeaderField: "Content-Type")
            request.setValue("Bearer " + apiKey, forHTTPHeaderField: "Authorization")
            
            let ssml = """
            <speak version='1.0' xml:lang='zh-CN'>
                <voice xml:lang='zh-CN' name='zh-CN-YunxiNeural'>
                    \(text)
                </voice>
            </speak>
            """
            request.httpBody = ssml.data(using: .utf8)
            
        case .google:
            let endpoint = "https://texttospeech.googleapis.com/v1/text:synthesize"
            request = URLRequest(url: URL(string: endpoint + "?key=" + apiKey)!)
            request.httpMethod = "POST"
            request.setValue("application/json", forHTTPHeaderField: "Content-Type")
            
            let body: [String: Any] = [
                "input": ["text": text],
                "voice": ["languageCode": "cmn-CN"],
                "audioConfig": ["audioEncoding": "MP3"]
            ]
            request.httpBody = try? JSONSerialization.data(withJSONObject: body)
        }
        
        let task = session.dataTask(with: request) { data, response, error in
            if let error = error {
                print("TTS请求失败: \(error.localizedDescription)")
                return
            }
            
            guard let data = data else {
                print("未收到TTS响应数据")
                return
            }
            
            // 处理TTS响应数据
            do {
                let audioFileURL = FileManager.default.temporaryDirectory.appendingPathComponent(UUID().uuidString + ".mp3")
                try data.write(to: audioFileURL)
                print("TTS音频文件保存成功: \(audioFileURL.path)")
                
                // 播放音频
                let audioPlayer = try AVAudioPlayer(contentsOf: audioFileURL)
                audioPlayer.play()
            } catch {
                print("TTS音频处理失败: \(error.localizedDescription)")
            }
        }
        task.resume()
    }
    
    enum CloudTTSService {
        case azure
        case google
    }
    
    // MARK: - RSS订阅
    // RSSEPUBParser, EPUBGenerator 和它们的内部结构体已移至文件顶层

    /// 将RSS文章转换为EPUB格式
    func convertRSSToEPUB(rssURL: URL) -> URL? {
        guard let parser = XMLParser(contentsOf: rssURL) else {
            print("无法解析RSS URL")
            return nil
        }
        
        let epubParser = RSSEPUBParser() // 直接使用顶层类
        parser.delegate = epubParser
        
        if !parser.parse() {
            print("RSS解析失败: \(parser.parserError?.localizedDescription ?? "未知错误")")
            return nil
        }
        
        // 使用顶层的 EPUBGenerator 和 Article
        let convertedArticles = epubParser.articles.map { EPUBGenerator.Article(title: $0.title, content: $0.content) } // 直接使用顶层结构体
        let epubGenerator = EPUBGenerator(articles: convertedArticles) // 直接使用顶层类
        do {
            let documentsURL = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
            let epubURL = documentsURL.appendingPathComponent("RSS_EPUB_\(Date().timeIntervalSince1970).epub")
            try epubGenerator.generate(to: epubURL)
            print("EPUB文件生成成功: \(epubURL.path)")
            return epubURL
        } catch {
            print("EPUB生成失败: \(error.localizedDescription)")
            return nil
        }
    }
    
    /// 关键词预警通知
    func setupKeywordAlert(keywords: [String], rssURL: URL) {
        guard !keywords.isEmpty else {
            print("关键词列表不能为空")
            return
        }
        
        let session = URLSession.shared
        let task = session.dataTask(with: rssURL) { data, response, error in
            if let error = error {
                print("RSS获取失败: \(error.localizedDescription)")
                return
            }
            
            guard let data = data else {
                print("未收到RSS数据")
                return
            }
            
            guard let rssContent = String(data: data, encoding: .utf8) else {
                print("RSS内容解码失败")
                return
            }
            
            // 关键词匹配
            var matchedKeywords = [String]()
            for keyword in keywords {
                if rssContent.contains(keyword) {
                    matchedKeywords.append(keyword)
                }
            }
            
            // 发送通知
            if !matchedKeywords.isEmpty {
                let content = UNMutableNotificationContent()
                content.title = "关键词预警"
                content.body = "发现匹配的关键词: \(matchedKeywords.joined(separator: ", "))"
                content.sound = UNNotificationSound.default
                
                let request = UNNotificationRequest(identifier: UUID().uuidString, 
                                                  content: content, 
                                                  trigger: nil)
                
                UNUserNotificationCenter.current().add(request) { error in
                    if let error = error {
                        print("通知发送失败: \(error.localizedDescription)")
                    } else {
                        print("已发送关键词预警通知")
                    }
                }
            }
        }
        task.resume()
    }
    
    // MARK: - 数据同步
    
    /// WebDAV同步
    func syncWithWebDAV(serverURL: URL, credentials: URLCredential) {
        let session = URLSession(configuration: .default, delegate: nil, delegateQueue: nil)
        
        // 1. 获取服务器文件列表
        var request = URLRequest(url: serverURL)
        request.httpMethod = "PROPFIND"
        request.setValue("1", forHTTPHeaderField: "Depth")
        request.httpBody = """
            <?xml version="1.0" encoding="utf-8" ?>
            <propfind xmlns="DAV:">
                <prop>
                    <getlastmodified/>
                    <getcontentlength/>
                </prop>
            </propfind>
            """.data(using: .utf8)
        
        let task = session.dataTask(with: request) { [weak self] data, response, error in
            guard let self = self else { return }
            
            if let error = error {
                DispatchQueue.main.async {
                    print("WebDAV同步失败: \(error.localizedDescription)")
                }
                return
            }
            
            guard let httpResponse = response as? HTTPURLResponse, 
                  (200...299).contains(httpResponse.statusCode) else {
                DispatchQueue.main.async {
                    print("服务器返回错误状态码: \((response as? HTTPURLResponse)?.statusCode ?? 0)")
                }
                return
            }
            
            guard let data = data, 
                  let xmlString = String(data: data, encoding: .utf8) else {
                DispatchQueue.main.async {
                    print("无法解析服务器响应")
                }
                return
            }
            
            DispatchQueue.global(qos: .userInitiated).async {
                let localFiles = self.getLocalFiles()
                let serverFiles = self.parseWebDAVResponse(xmlString)
                
                DispatchQueue.main.async {
                    self.syncFiles(localFiles: localFiles, serverFiles: serverFiles, serverURL: serverURL, credentials: credentials)
                }
            }
        }
        task.resume()
    }
    
    /// 解析WebDAV服务器的XML响应
    private func parseWebDAVResponse(_ xmlString: String) -> [(name: String, modifiedDate: Date)] {
        var files = [(name: String, modifiedDate: Date)]()
        let parser = XMLParser(data: xmlString.data(using: .utf8)!)
        let delegate = WebDAVXMLParserDelegate()
        parser.delegate = delegate
        
        if parser.parse() {
            files = delegate.files
        } else {
            print("WebDAV XML解析失败: \(parser.parserError?.localizedDescription ?? "未知错误")")
        }
        
        return files
    }
    
    class WebDAVXMLParserDelegate: NSObject, XMLParserDelegate {
        var files = [(name: String, modifiedDate: Date)]()
        var currentElement = ""
        var currentName = ""
        var currentModifiedDate: Date?
        
        func parser(_ parser: XMLParser, didStartElement elementName: String, namespaceURI: String?, qualifiedName qName: String?, attributes attributeDict: [String : String] = [:]) {
            currentElement = elementName
            if elementName == "d:response" {
                currentName = ""
                currentModifiedDate = nil
            }
        }
        
        func parser(_ parser: XMLParser, foundCharacters string: String) {
            if currentElement == "d:href" {
                currentName += string
            } else if currentElement == "d:getlastmodified" {
                let dateFormatter = DateFormatter()
                dateFormatter.locale = Locale(identifier: "en_US_POSIX")
                dateFormatter.dateFormat = "EEE, dd MMM yyyy HH:mm:ss zzz"
                currentModifiedDate = dateFormatter.date(from: string)
            }
        }
        
        func parser(_ parser: XMLParser, didEndElement elementName: String, namespaceURI: String?, qualifiedName qName: String?) {
            if elementName == "d:response" {
                if let date = currentModifiedDate {
                    files.append((name: currentName, modifiedDate: date))
                }
            }
            currentElement = ""
        }
    }
    
    /// 获取本地文件列表
    private func getLocalFiles() -> [(name: String, modifiedDate: Date)] {
        guard let documentsURL = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first else {
            return []
        }
        
        do {
            let fileURLs = try FileManager.default.contentsOfDirectory(at: documentsURL, includingPropertiesForKeys: [.contentModificationDateKey])
            return fileURLs.compactMap { url in
                do {
                    let values = try url.resourceValues(forKeys: [.contentModificationDateKey, .nameKey])
                    if let name = values.name, let modifiedDate = values.contentModificationDate {
                        return (name: name, modifiedDate: modifiedDate)
                    }
                } catch {
                    print("获取文件属性失败: \(error.localizedDescription)")
                }
                return nil
            }
        } catch {
            print("获取本地文件列表失败: \(error.localizedDescription)")
            return []
        }
    }
    
    /// 同步本地和服务器文件
    private func syncFiles(localFiles: [(name: String, modifiedDate: Date)], 
                         serverFiles: [(name: String, modifiedDate: Date)],
                         serverURL: URL,
                         credentials: URLCredential) {
        
        // 创建需要上传的文件列表
        let filesToUpload = localFiles.filter { localFile in
            !serverFiles.contains { $0.name == localFile.name } ||
            serverFiles.first { $0.name == localFile.name }?.modifiedDate ?? Date.distantPast < localFile.modifiedDate
        }
        
        // 创建需要下载的文件列表
        let filesToDownload = serverFiles.filter { serverFile in
            !localFiles.contains { $0.name == serverFile.name } ||
            localFiles.first { $0.name == serverFile.name }?.modifiedDate ?? Date.distantPast < serverFile.modifiedDate
        }
        
        // 处理上传
        for file in filesToUpload {
            guard let documentsURL = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first else {
                continue
            }
            
            let localFileURL = documentsURL.appendingPathComponent(file.name)
            let remoteFileURL = serverURL.appendingPathComponent(file.name)
            
            var request = URLRequest(url: remoteFileURL)
            request.httpMethod = "PUT"
            request.httpBody = try? Data(contentsOf: localFileURL)
            
            let task = URLSession.shared.dataTask(with: request) { _, _, error in
                if let error = error {
                    print("文件上传失败: \(file.name), 错误: \(error.localizedDescription)")
                } else {
                    print("文件上传成功: \(file.name)")
                }
            }
            task.resume()
        }
        
        // 处理下载
        for file in filesToDownload {
            let remoteFileURL = serverURL.appendingPathComponent(file.name)
            guard let documentsURL = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first else {
                continue
            }
            let localFileURL = documentsURL.appendingPathComponent(file.name)
            
            let task = URLSession.shared.dataTask(with: remoteFileURL) { data, _, error in
                if let error = error {
                    print("文件下载失败: \(file.name), 错误: \(error.localizedDescription)")
                    return
                }
                
                guard let data = data else {
                    print("未收到文件数据: \(file.name)")
                    return
                }
                
                do {
                    try data.write(to: localFileURL)
                    print("文件下载成功: \(file.name)")
                } catch {
                    print("文件保存失败: \(file.name), 错误: \(error.localizedDescription)")
                }
            }
            task.resume()
        }
    }
    
    /// 精确到段落的位置同步
    func syncReadingPosition(bookID: String, paragraphIndex: Int) {
        // 1. 保存本地阅读位置
        UserDefaults.standard.set(paragraphIndex, forKey: "(bookID)_reading_position")
        
        // 2. 准备同步数据
        let syncData: [String: Any] = [
            "book_id": bookID,
            "paragraph_index": paragraphIndex,
            "timestamp": Date().timeIntervalSince1970
        ]
        
        // 3. 同步到服务器
        guard let url = URL(string: "https://api.example.com/sync/position") else {
            print("无效的同步URL")
            return
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        
        do {
            request.httpBody = try JSONSerialization.data(withJSONObject: syncData)
            
            let task = URLSession.shared.dataTask(with: request) { _, response, error in
                if let error = error {
                    print("位置同步失败: \(error.localizedDescription)")
                    return
                }
                
                guard let httpResponse = response as? HTTPURLResponse,
                      (200...299).contains(httpResponse.statusCode) else {
                    print("位置同步服务器错误")
                    return
                }
                
                print("段落位置同步成功")
            }
            task.resume()
        } catch {
            print("位置数据编码失败: \(error.localizedDescription)")
        }
    }
}

/// 高级功能视图
/// 核心功能:
/// 1. 提供高级阅读设置
/// 2. 支持自定义主题和字体
/// 3. 提供高级书签和笔记功能
/// 
/// 主要UI组件:
/// - 设置选项列表
/// - 主题选择器
/// - 字体调整滑块
struct AdvancedFeaturesView: View {
    @State private var selectedTheme: AppTheme = .light
    @State private var fontSize: CGFloat = 14
    @State private var showBookmarks = false
    @State private var showNotes = false

    var body: some View {
        VStack {
            // 主题选择器
            themeSelector

            // 字体调整滑块
            fontSizeSlider

            // 书签和笔记按钮
            bookmarkAndNoteButtons
        }
        .padding()
        .navigationTitle("高级功能")
    }

    // MARK: - 子视图

    private var themeSelector: some View {
        Picker("主题", selection: $selectedTheme) {
            Text("浅色主题").tag(AppTheme.light) // 使用 AppTheme
            Text("深色主题").tag(AppTheme.dark)   // 使用 AppTheme
        }
        .pickerStyle(SegmentedPickerStyle())
    }

    private var fontSizeSlider: some View {
        VStack {
            Text("字体大小")
            Slider(value: $fontSize, in: 10...20)
            // 这里范围固定为10...20，不会出现min==max的情况
        }
    }

    private var bookmarkAndNoteButtons: some View {
        HStack {
            Button(action: { showBookmarks.toggle() }) {
                Label("书签", systemImage: "bookmark")
            }

            Button(action: { showNotes.toggle() }) {
                Label("笔记", systemImage: "note.text")
            }
        }
    }
}

// MARK: - 主题枚举

enum AppTheme: String, CaseIterable, Identifiable { // Renamed to AppTheme
    case light
    case dark

    var id: String { self.rawValue }

    // Add a default theme static property
    static var defaultTheme: AppTheme {
        .light // Or based on system settings, etc.
    }
}