-------------------------------------
Translated Report (Full Report Below)
-------------------------------------

Incident Identifier: 33A02E6B-B91A-4B6F-871C-824540A856DC
CrashReporter Key:   85BC2F4D-3554-8185-EC95-674F30BE244B
Hardware Model:      MacBookPro18,3
Process:             IOSReader [28117]
Path:                /Users/<USER>/Library/Developer/Xcode/UserData/Previews/Simulator Devices/9D65155D-655C-4E8E-91EF-CED7602892C2/data/Containers/Bundle/Application/38E66FBD-19DB-42E9-A7F1-E053D5F0CBC3/IOSReader.app/IOSReader
Identifier:          none.IOSReader
Version:             1.0 (1)
Code Type:           ARM-64 (Native)
Role:                Foreground
Parent Process:      launchd_sim [55529]
Coalition:           com.apple.CoreSimulator.SimDevice.9D65155D-655C-4E8E-91EF-CED7602892C2 [7315]
Responsible Process: SimulatorTrampoline [1151]

Date/Time:           2025-04-23 08:30:19.7109 +0800
Launch Time:         2025-04-23 08:30:19.0473 +0800
OS Version:          macOS 15.4.1 (24E263)
Release Type:        User
Report Version:      104

Exception Type:  EXC_BREAKPOINT (SIGTRAP)
Exception Codes: 0x0000000000000001, 0x0000000195712d2c
Termination Reason: SIGNAL 5 Trace/BPT trap: 5
Terminating Process: exc handler [28117]

Triggered by Thread:  0

Thread 0 Crashed::  Dispatch queue: com.apple.main-thread
0   libswiftCore.dylib            	       0x195712d2c _assertionFailure(_:_:file:line:flags:) + 156
1   SwiftUICore                   	       0x1d4a5c7a4 precondition(_:_:file:line:) + 148
2   SwiftUI                       	       0x1d3452cd0 Normalizing.init(min:max:stride:) + 968
3   SwiftUI                       	       0x1d3452488 Slider.init<A>(value:in:step:onEditingChanged:minimumValueLabel:maximumValueLabel:customMinMaxValueLabels:label:marks:) + 924
4   SwiftUI                       	       0x1d3455040 Slider<>.init<A>(value:in:step:onEditingChanged:) + 416
5   ???                           	       0x34084dfac ???
6   SwiftUICore                   	       0x1d430cfb0 <deduplicated_symbol> + 88
7   SwiftUICore                   	       0x1d4654bd4 _VariadicView.Tree.init(_:content:) + 112
8   SwiftUICore                   	       0x1d45b49dc HStack.init(alignment:spacing:content:) + 76
9   ???                           	       0x34084cf48 ???
10  SwiftUICore                   	       0x1d430cfb0 <deduplicated_symbol> + 88
11  SwiftUICore                   	       0x1d4654bd4 _VariadicView.Tree.init(_:content:) + 112
12  SwiftUICore                   	       0x1d43fee44 VStack.init(alignment:spacing:content:) + 76
13  ???                           	       0x340845710 ???
14  ???                           	       0x340844c90 ???
15  SwiftUICore                   	       0x1d430cfb0 <deduplicated_symbol> + 88
16  SwiftUICore                   	       0x1d4654bd4 _VariadicView.Tree.init(_:content:) + 112
17  SwiftUICore                   	       0x1d43fee44 VStack.init(alignment:spacing:content:) + 76
18  ???                           	       0x3408445c4 ???
19  SwiftUICore                   	       0x1d430cfb0 <deduplicated_symbol> + 88
20  SwiftUICore                   	       0x1d4654bd4 _VariadicView.Tree.init(_:content:) + 112
21  SwiftUICore                   	       0x1d430cf4c ZStack.init(alignment:content:) + 60
22  ???                           	       0x3408441f8 ???
23  SwiftUICore                   	       0x1d46ebc10 partial apply for closure #1 in closure #1 in GeometryReader.Child.updateValue() + 56
24  SwiftUICore                   	       0x1d486cc24 closure #1 in Attribute.syncMainIfReferences<A>(do:) + 104
25  SwiftUICore                   	       0x1d466ec5c specialized static Update.syncMain(_:) + 76
26  SwiftUICore                   	       0x1d486b148 Attribute.syncMainIfReferences<A>(do:) + 280
27  SwiftUICore                   	       0x1d46ebbc0 partial apply for closure #1 in GeometryReader.Child.updateValue() + 92
28  SwiftUICore                   	       0x1d48104d8 closure #1 in _withObservation<A>(do:) + 84
29  SwiftUICore                   	       0x1d44dc3dc partial apply for closure #1 in _withObservation<A>(do:) + 24
30  SwiftUICore                   	       0x1d46691a8 <deduplicated_symbol> + 72
31  SwiftUICore                   	       0x1d46ea11c GeometryReader.Child.updateValue() + 988
32  SwiftUICore                   	       0x1d46e7ec4 partial apply for implicit closure #1 in closure #1 in closure #1 in Attribute.init<A>(_:) + 28
33  AttributeGraph                	       0x1bfeba438 AG::Graph::UpdateStack::update() + 516
34  AttributeGraph                	       0x1bfebac04 AG::Graph::update_attribute(AG::data::ptr<AG::Node>, unsigned int) + 428
35  AttributeGraph                	       0x1bfec24dc AG::Graph::input_value_ref_slow(AG::data::ptr<AG::Node>, AG::AttributeID, unsigned int, unsigned int, AGSwiftMetadata const*, unsigned char&, long) + 708
36  AttributeGraph                	       0x1bfed7fe4 AGGraphGetValue + 232
37  SwiftUICore                   	       0x1d43bcce0 specialized DynamicViewList.updateValue() + 132
38  SwiftUICore                   	       0x1d43cbe80 specialized implicit closure #1 in closure #1 in closure #1 in Attribute.init<A>(_:) + 20
39  AttributeGraph                	       0x1bfeba438 AG::Graph::UpdateStack::update() + 516
40  AttributeGraph                	       0x1bfebac04 AG::Graph::update_attribute(AG::data::ptr<AG::Node>, unsigned int) + 428
41  AttributeGraph                	       0x1bfec24dc AG::Graph::input_value_ref_slow(AG::data::ptr<AG::Node>, AG::AttributeID, unsigned int, unsigned int, AGSwiftMetadata const*, unsigned char&, long) + 708
42  AttributeGraph                	       0x1bfed7fe4 AGGraphGetValue + 232
43  SwiftUICore                   	       0x1d46dce78 specialized implicit closure #1 in closure #1 in closure #1 in Attribute.init<A>(_:) + 96
44  AttributeGraph                	       0x1bfeba438 AG::Graph::UpdateStack::update() + 516
45  AttributeGraph                	       0x1bfebac04 AG::Graph::update_attribute(AG::data::ptr<AG::Node>, unsigned int) + 428
46  AttributeGraph                	       0x1bfec24dc AG::Graph::input_value_ref_slow(AG::data::ptr<AG::Node>, AG::AttributeID, unsigned int, unsigned int, AGSwiftMetadata const*, unsigned char&, long) + 708
47  AttributeGraph                	       0x1bfed7fe4 AGGraphGetValue + 232
48  SwiftUICore                   	       0x1d46dd040 specialized implicit closure #1 in closure #1 in closure #1 in Attribute.init<A>(_:) + 160
49  AttributeGraph                	       0x1bfeba438 AG::Graph::UpdateStack::update() + 516
50  AttributeGraph                	       0x1bfebac04 AG::Graph::update_attribute(AG::data::ptr<AG::Node>, unsigned int) + 428
51  AttributeGraph                	       0x1bfec24dc AG::Graph::input_value_ref_slow(AG::data::ptr<AG::Node>, AG::AttributeID, unsigned int, unsigned int, AGSwiftMetadata const*, unsigned char&, long) + 708
52  AttributeGraph                	       0x1bfed7fe4 AGGraphGetValue + 232
53  SwiftUICore                   	       0x1d46fe000 DynamicLayoutViewAdaptor.updatedItems() + 64
54  SwiftUICore                   	       0x1d43ae510 specialized DynamicContainerInfo.updateItems(disableTransitions:) + 60
55  SwiftUICore                   	       0x1d43ad7ac specialized DynamicContainerInfo.updateValue() + 484
56  SwiftUICore                   	       0x1d43cd698 specialized implicit closure #1 in closure #1 in closure #1 in Attribute.init<A>(_:) + 20
57  AttributeGraph                	       0x1bfeba438 AG::Graph::UpdateStack::update() + 516
58  AttributeGraph                	       0x1bfebac04 AG::Graph::update_attribute(AG::data::ptr<AG::Node>, unsigned int) + 428
59  AttributeGraph                	       0x1bfec80b4 AG::Subgraph::update(unsigned int) + 828
60  SwiftUICore                   	       0x1d4afa6e4 ViewGraph.updateOutputs(async:) + 388
61  SwiftUICore                   	       0x1d4af1528 closure #1 in ViewRendererHost.render(interval:updateDisplayList:targetTimestamp:) + 996
62  SwiftUICore                   	       0x1d4aef064 ViewRendererHost.render(interval:updateDisplayList:targetTimestamp:) + 472
63  SwiftUI                       	       0x1d38e92a8 closure #1 in UIHostingViewBase.didMoveToWindow() + 332
64  SwiftUI                       	       0x1d3443c38 <deduplicated_symbol> + 20
65  SwiftUI                       	       0x1d3443f08 closure #1 in static UIKitUpdateCycle.addPreCommitObserver(_:) + 112
66  SwiftUI                       	       0x1d3443f98 thunk for @escaping @callee_guaranteed (@unowned UnsafeMutableRawPointer?, @unowned Double, @unowned UnsafePointer<_UIUpdateTiming>) -> () + 64
67  UIKitCore                     	       0x185133f04 _UIUpdateSequenceRun + 76
68  UIKitCore                     	       0x185afd9b8 schedulerStepScheduledMainSection + 204
69  UIKitCore                     	       0x185afcdd4 runloopSourceCallback + 80
70  CoreFoundation                	       0x1804284b8 __CFRUNLOOP_IS_CALLING_OUT_TO_A_SOURCE0_PERFORM_FUNCTION__ + 24
71  CoreFoundation                	       0x180428400 __CFRunLoopDoSource0 + 168
72  CoreFoundation                	       0x180427be4 __CFRunLoopDoSources0 + 312
73  CoreFoundation                	       0x180422584 __CFRunLoopRun + 780
74  CoreFoundation                	       0x180421e3c CFRunLoopRunSpecific + 536
75  GraphicsServices              	       0x190f62d00 GSEventRunModal + 164
76  UIKitCore                     	       0x185bcec98 -[UIApplication _run] + 796
77  UIKitCore                     	       0x185bd3064 UIApplicationMain + 124
78  SwiftUI                       	       0x1d3953aa8 closure #1 in KitRendererCommon(_:) + 164
79  SwiftUI                       	       0x1d39537d0 runApp<A>(_:) + 84
80  SwiftUI                       	       0x1d36e09e0 static App.main() + 148
81  ???                           	       0x340049ea8 ???
82  ???                           	       0x340049fd8 ???
83  IOSReader                     	       0x100fd94f0 __debug_blank_executor_run_user_entry_point + 128
84  PreviewsInjection             	       0x23513c498 0x235113000 + 169112
85  PreviewsInjection             	       0x23513cf30 0x235113000 + 171824
86  PreviewsInjection             	       0x23513ce4c __previews_injection_run_user_entrypoint + 12
87  XOJITExecutor                 	       0x246f84058 __xojit_executor_run_program_wrapper + 1708
88  XOJITExecutor                 	       0x246f8051c 0x246f7e000 + 9500
89  PreviewsInjection             	       0x23513cd98 0x235113000 + 171416
90  IOSReader                     	       0x100fd8cd0 __debug_blank_executor_main + 992
91  ???                           	       0x1012713d8 ???
92  dyld                          	       0x101146b4c start + 6000

Thread 1::  Dispatch queue: com.apple.xojit_executor.wrapper_functions
0   libsystem_kernel.dylib        	       0x101040aec semaphore_wait_trap + 8
1   libdispatch.dylib             	       0x18017e238 _dispatch_sema4_wait + 24
2   libdispatch.dylib             	       0x18017e7c0 _dispatch_semaphore_wait_slow + 128
3   XOJITExecutor                 	       0x246f805bc 0x246f7e000 + 9660
4   XOJITExecutor                 	       0x246f838f0 __xojit_executor_run_program_on_main_thread + 224
5   XOJITExecutor                 	       0x246f81be8 0x246f7e000 + 15336
6   XOJITExecutor                 	       0x246f81924 0x246f7e000 + 14628
7   libdispatch.dylib             	       0x18017c788 _dispatch_call_block_and_release + 24
8   libdispatch.dylib             	       0x180197278 _dispatch_client_callout + 12
9   libdispatch.dylib             	       0x1801814f4 _dispatch_continuation_pop + 868
10  libdispatch.dylib             	       0x1801806b0 _dispatch_async_redirect_invoke + 852
11  libdispatch.dylib             	       0x18018f9cc _dispatch_root_queue_drain + 336
12  libdispatch.dylib             	       0x1801903b4 _dispatch_worker_thread2 + 232
13  libsystem_pthread.dylib       	       0x10132ab90 _pthread_wqthread + 228
14  libsystem_pthread.dylib       	       0x10132998c start_wqthread + 8

Thread 2:
0   libsystem_pthread.dylib       	       0x101329984 start_wqthread + 0

Thread 3:
0   libsystem_pthread.dylib       	       0x101329984 start_wqthread + 0

Thread 4:
0   libsystem_pthread.dylib       	       0x101329984 start_wqthread + 0

Thread 5:
0   libsystem_pthread.dylib       	       0x101329984 start_wqthread + 0

Thread 6:
0   libsystem_pthread.dylib       	       0x101329984 start_wqthread + 0

Thread 7:
0   libsystem_pthread.dylib       	       0x101329984 start_wqthread + 0

Thread 8::  Dispatch queue: com.apple.root.utility-qos
0   libswiftCore.dylib            	       0x1959fb82c swift_conformsToProtocolMaybeInstantiateSuperclasses(swift::TargetMetadata<swift::InProcess> const*, swift::TargetProtocolDescriptor<swift::InProcess> const*, bool) + 2704
1   libswiftCore.dylib            	       0x1959f9cc8 swift_conformsToProtocol + 52
2   AttributeGraph                	       0x1bfecd3e8 AG::LayoutDescriptor::make_layout(AG::swift::metadata const*, AGComparisonMode, AG::LayoutDescriptor::HeapMode) + 120
3   AttributeGraph                	       0x1bfecd8d0 AG::(anonymous namespace)::TypeDescriptorCache::fetch(AG::swift::metadata const*, unsigned int, AG::LayoutDescriptor::HeapMode, int) + 384
4   AttributeGraph                	       0x1bfecc6dc AG::LayoutDescriptor::Builder::should_visit_fields(AG::swift::metadata const*, bool) + 56
5   AttributeGraph                	       0x1bfecca68 AG::LayoutDescriptor::Builder::visit_case(AG::swift::metadata const*, AG::swift::field_record const&, unsigned int) + 376
6   AttributeGraph                	       0x1bfeb7024 AG::swift::metadata::visit(AG::swift::metadata_visitor&) const + 588
7   AttributeGraph                	       0x1bfecd538 AG::LayoutDescriptor::make_layout(AG::swift::metadata const*, AGComparisonMode, AG::LayoutDescriptor::HeapMode) + 456
8   AttributeGraph                	       0x1bfecd8d0 AG::(anonymous namespace)::TypeDescriptorCache::fetch(AG::swift::metadata const*, unsigned int, AG::LayoutDescriptor::HeapMode, int) + 384
9   AttributeGraph                	       0x1bfecc6dc AG::LayoutDescriptor::Builder::should_visit_fields(AG::swift::metadata const*, bool) + 56
10  AttributeGraph                	       0x1bfecc588 AG::LayoutDescriptor::Builder::visit_element(AG::swift::metadata const*, AG::swift::metadata::ref_kind, unsigned long, unsigned long) + 116
11  AttributeGraph                	       0x1bfeb793c AG::swift::metadata_visitor::visit_field(AG::swift::metadata const*, AG::swift::field_record const&, unsigned long, unsigned long) + 132
12  AttributeGraph                	       0x1bfeb70e4 AG::swift::metadata::visit(AG::swift::metadata_visitor&) const + 780
13  AttributeGraph                	       0x1bfecd538 AG::LayoutDescriptor::make_layout(AG::swift::metadata const*, AGComparisonMode, AG::LayoutDescriptor::HeapMode) + 456
14  AttributeGraph                	       0x1bfecd8d0 AG::(anonymous namespace)::TypeDescriptorCache::fetch(AG::swift::metadata const*, unsigned int, AG::LayoutDescriptor::HeapMode, int) + 384
15  AttributeGraph                	       0x1bfecc6dc AG::LayoutDescriptor::Builder::should_visit_fields(AG::swift::metadata const*, bool) + 56
16  AttributeGraph                	       0x1bfecc588 AG::LayoutDescriptor::Builder::visit_element(AG::swift::metadata const*, AG::swift::metadata::ref_kind, unsigned long, unsigned long) + 116
17  AttributeGraph                	       0x1bfeb793c AG::swift::metadata_visitor::visit_field(AG::swift::metadata const*, AG::swift::field_record const&, unsigned long, unsigned long) + 132
18  AttributeGraph                	       0x1bfeb70e4 AG::swift::metadata::visit(AG::swift::metadata_visitor&) const + 780
19  AttributeGraph                	       0x1bfecd538 AG::LayoutDescriptor::make_layout(AG::swift::metadata const*, AGComparisonMode, AG::LayoutDescriptor::HeapMode) + 456
20  AttributeGraph                	       0x1bfecd8d0 AG::(anonymous namespace)::TypeDescriptorCache::fetch(AG::swift::metadata const*, unsigned int, AG::LayoutDescriptor::HeapMode, int) + 384
21  AttributeGraph                	       0x1bfecc6dc AG::LayoutDescriptor::Builder::should_visit_fields(AG::swift::metadata const*, bool) + 56
22  AttributeGraph                	       0x1bfecc588 AG::LayoutDescriptor::Builder::visit_element(AG::swift::metadata const*, AG::swift::metadata::ref_kind, unsigned long, unsigned long) + 116
23  AttributeGraph                	       0x1bfeb793c AG::swift::metadata_visitor::visit_field(AG::swift::metadata const*, AG::swift::field_record const&, unsigned long, unsigned long) + 132
24  AttributeGraph                	       0x1bfeb70e4 AG::swift::metadata::visit(AG::swift::metadata_visitor&) const + 780
25  AttributeGraph                	       0x1bfecd538 AG::LayoutDescriptor::make_layout(AG::swift::metadata const*, AGComparisonMode, AG::LayoutDescriptor::HeapMode) + 456
26  AttributeGraph                	       0x1bfececb4 AG::(anonymous namespace)::TypeDescriptorCache::drain_queue(void*) + 360
27  libdispatch.dylib             	       0x180197278 _dispatch_client_callout + 12
28  libdispatch.dylib             	       0x1801b2350 <deduplicated_symbol> + 28
29  libdispatch.dylib             	       0x18018fc10 _dispatch_root_queue_drain + 916
30  libdispatch.dylib             	       0x1801903b4 _dispatch_worker_thread2 + 232
31  libsystem_pthread.dylib       	       0x10132ab90 _pthread_wqthread + 228
32  libsystem_pthread.dylib       	       0x10132998c start_wqthread + 8

Thread 9:
0   libsystem_pthread.dylib       	       0x101329984 start_wqthread + 0

Thread 10:: com.apple.uikit.eventfetch-thread
0   libsystem_kernel.dylib        	       0x101040b70 mach_msg2_trap + 8
1   libsystem_kernel.dylib        	       0x101051f18 mach_msg2_internal + 72
2   libsystem_kernel.dylib        	       0x101048c28 mach_msg_overwrite + 480
3   libsystem_kernel.dylib        	       0x101040ed8 mach_msg + 20
4   CoreFoundation                	       0x180427d14 __CFRunLoopServiceMachPort + 156
5   CoreFoundation                	       0x1804226f4 __CFRunLoopRun + 1148
6   CoreFoundation                	       0x180421e3c CFRunLoopRunSpecific + 536
7   Foundation                    	       0x180f22ddc -[NSRunLoop(NSRunLoop) runMode:beforeDate:] + 208
8   Foundation                    	       0x180f22ffc -[NSRunLoop(NSRunLoop) runUntilDate:] + 60
9   UIKitCore                     	       0x185c7c724 -[UIEventFetcher threadMain] + 408
10  Foundation                    	       0x180f49b98 __NSThread__start__ + 716
11  libsystem_pthread.dylib       	       0x10132e5f0 _pthread_start + 104
12  libsystem_pthread.dylib       	       0x101329998 thread_start + 8

Thread 11:
0   libsystem_pthread.dylib       	       0x101329984 start_wqthread + 0

Thread 12:
0   libsystem_pthread.dylib       	       0x101329984 start_wqthread + 0

Thread 13:
0   libsystem_pthread.dylib       	       0x101329984 start_wqthread + 0

Thread 14:
0   libsystem_pthread.dylib       	       0x101329984 start_wqthread + 0

Thread 15:
0   libsystem_pthread.dylib       	       0x101329984 start_wqthread + 0

Thread 16:
0   libsystem_pthread.dylib       	       0x101329984 start_wqthread + 0

Thread 17:
0   libsystem_pthread.dylib       	       0x101329984 start_wqthread + 0

Thread 18:
0   libsystem_pthread.dylib       	       0x101329984 start_wqthread + 0

Thread 19:
0   libsystem_pthread.dylib       	       0x101329984 start_wqthread + 0

Thread 20:
0   libsystem_pthread.dylib       	       0x101329984 start_wqthread + 0

Thread 21:
0   libsystem_pthread.dylib       	       0x101329984 start_wqthread + 0

Thread 22:: JavaScriptCore libpas scavenger
0   libsystem_kernel.dylib        	       0x101044014 __psynch_cvwait + 8
1   libsystem_pthread.dylib       	       0x10132eab8 _pthread_cond_wait + 976
2   JavaScriptCore                	       0x199737ae8 scavenger_thread_main + 1176
3   libsystem_pthread.dylib       	       0x10132e5f0 _pthread_start + 104
4   libsystem_pthread.dylib       	       0x101329998 thread_start + 8


Thread 0 crashed with ARM Thread State (64-bit):
    x0: 0x80000001d40418d0   x1: 0x0000000000000000   x2: 0x0000000000000000   x3: 0x0000600000d8c630
    x4: 0x0000000000000000   x5: 0x000000016ee19720   x6: 0x0000000000000065   x7: 0x0000000000000000
    x8: 0x0000000000000155   x9: 0x00000000bbea1805  x10: 0x0000000000000154  x11: 0x0000000000000600
   x12: 0x00000000000007fb  x13: 0x00000000000007fd  x14: 0x00000000bc0a2021  x15: 0x00000000bbea1805
   x16: 0x00000000bc000000  x17: 0x0000000000000021  x18: 0x0000000000000000  x19: 0x0000000000000002
   x20: 0x0000000000000000  x21: 0x0000000000000014  x22: 0x00000001d40418d0  x23: 0x000000000000024a
   x24: 0x00000001d3455260  x25: 0x0000000000000000  x26: 0x0000000102822638  x27: 0x00000001f4385918
   x28: 0x0000000102822250   fp: 0x000000016ee198c0   lr: 0x0000000195712d2c
    sp: 0x000000016ee19840   pc: 0x0000000195712d2c cpsr: 0x60001000
   far: 0x0000000000000000  esr: 0xf2000001 (Breakpoint) brk 1

Binary Images:
       0x101140000 -        0x1011dbfff dyld (*) <aca43a8d-6369-3a2c-af92-3d4c458523d6> /usr/lib/dyld
       0x100fd8000 -        0x100fdbfff none.IOSReader (1.0) <de94f987-9d68-3034-8981-871137e28a27> /Users/<USER>/Library/Developer/Xcode/UserData/Previews/Simulator Devices/9D65155D-655C-4E8E-91EF-CED7602892C2/data/Containers/Bundle/Application/38E66FBD-19DB-42E9-A7F1-E053D5F0CBC3/IOSReader.app/IOSReader
       0x100ffc000 -        0x100ffffff __preview.dylib (*) <9f12880f-66c3-36d5-b0db-8a0202df4d07> /Users/<USER>/Library/Developer/Xcode/UserData/Previews/Simulator Devices/9D65155D-655C-4E8E-91EF-CED7602892C2/data/Containers/Bundle/Application/38E66FBD-19DB-42E9-A7F1-E053D5F0CBC3/IOSReader.app/__preview.dylib
       0x10102c000 -        0x10102ffff libLogRedirect.dylib (*) <c8912565-0eb8-3883-a87e-48748e221994> /Volumes/VOLUME/*/libLogRedirect.dylib
       0x101008000 -        0x10100ffff libsystem_platform.dylib (*) <98803d0c-942b-350b-baf1-4879fd637409> /usr/lib/system/libsystem_platform.dylib
       0x101040000 -        0x10107bfff libsystem_kernel.dylib (*) <02dd445e-88c2-3ac8-979a-cf2b39185f71> /usr/lib/system/libsystem_kernel.dylib
       0x101328000 -        0x101337fff libsystem_pthread.dylib (*) <e4d4a6eb-1bf6-3f43-a21d-d042f2771dc4> /usr/lib/system/libsystem_pthread.dylib
       0x101474000 -        0x10147ffff libobjc-trampolines.dylib (*) <22dfc621-7386-3de3-87b2-9ed27c8be84d> /Volumes/VOLUME/*/libobjc-trampolines.dylib
       0x1956e8000 -        0x195b6b2ff libswiftCore.dylib (*) <4f21129f-ede4-3571-b8f0-99188db009fe> /Volumes/VOLUME/*/libswiftCore.dylib
       0x1d42a0000 -        0x1d4c7943f com.apple.SwiftUICore (6.4.41) <0027f0a4-da0d-31e2-a055-a36b8099fe44> /Volumes/VOLUME/*/SwiftUICore.framework/SwiftUICore
       0x1d30fe000 -        0x1d429fabf com.apple.SwiftUI (6.4.41) <05d96221-a9d9-39c5-81cf-c72e8ae73467> /Volumes/VOLUME/*/SwiftUI.framework/SwiftUI
               0x0 - 0xffffffffffffffff ??? (*) <00000000-0000-0000-0000-000000000000> ???
       0x1bfeb1000 -        0x1bfeed6ff com.apple.AttributeGraph (6.4.39.2) <f1845a94-6b4b-325a-b68e-a1c58f629ea2> /Volumes/VOLUME/*/AttributeGraph.framework/AttributeGraph
       0x184d72000 -        0x186adb8df com.apple.UIKitCore (1.0) <d4c23b9a-c567-3e42-86ef-697aec976159> /Volumes/VOLUME/*/UIKitCore.framework/UIKitCore
       0x180395000 -        0x1807acfff com.apple.CoreFoundation (6.9) <3d4aa1d5-03aa-3365-b767-944509b9bbfd> /Volumes/VOLUME/*/CoreFoundation.framework/CoreFoundation
       0x190f60000 -        0x190f681ff com.apple.GraphicsServices (1.0) <eeb999f0-53c2-31ce-b203-3c78fb303dab> /Volumes/VOLUME/*/GraphicsServices.framework/GraphicsServices
       0x235113000 -        0x235183b1f com.apple.dt.PreviewsInjection (16.0) <5e953c9b-7805-3ecf-b4b7-d4ae88f00745> /Volumes/VOLUME/*/PreviewsInjection.framework/PreviewsInjection
       0x246f7e000 -        0x246f8ae2a com.apple.xojit.XOJITExecutor (1.0) <38d76d2b-ab26-3b23-a020-bb1923958b1e> /Volumes/VOLUME/*/XOJITExecutor.framework/XOJITExecutor
       0x18017b000 -        0x1801bfb5f libdispatch.dylib (*) <7bee27fd-f519-330d-aebe-d6ace467df22> /Volumes/VOLUME/*/libdispatch.dylib
       0x1871a8000 -        0x1876aa7bf com.apple.VN (********) <2ec273d8-f591-3e5c-a5bd-604c624c9982> /Volumes/VOLUME/*/Vision.framework/Vision
       0x18082c000 -        0x1813f18df com.apple.Foundation (6.9) <6ec60314-780a-318f-8bdb-5d173b13970e> /Volumes/VOLUME/*/Foundation.framework/Foundation
       0x1980ff000 -        0x19993123f com.apple.JavaScriptCore (8621) <5ebfe6ea-8077-3a92-9761-965c0e9ea6e6> /Volumes/VOLUME/*/JavaScriptCore.framework/JavaScriptCore

EOF

-----------
Full Report
-----------

{"app_name":"IOSReader","timestamp":"2025-04-23 08:30:20.00 +0800","app_version":"1.0","slice_uuid":"de94f987-9d68-3034-8981-871137e28a27","build_version":"1","platform":7,"bundleID":"none.IOSReader","share_with_app_devs":0,"is_first_party":0,"bug_type":"309","os_version":"macOS 15.4.1 (24E263)","roots_installed":0,"name":"IOSReader","incident_id":"33A02E6B-B91A-4B6F-871C-824540A856DC"}
{
  "uptime" : 100000,
  "procRole" : "Foreground",
  "version" : 2,
  "userID" : 501,
  "deployVersion" : 210,
  "modelCode" : "MacBookPro18,3",
  "coalitionID" : 7315,
  "osVersion" : {
    "train" : "macOS 15.4.1",
    "build" : "24E263",
    "releaseType" : "User"
  },
  "captureTime" : "2025-04-23 08:30:19.7109 +0800",
  "codeSigningMonitor" : 1,
  "incident" : "33A02E6B-B91A-4B6F-871C-824540A856DC",
  "pid" : 28117,
  "translated" : false,
  "cpuType" : "ARM-64",
  "roots_installed" : 0,
  "bug_type" : "309",
  "procLaunch" : "2025-04-23 08:30:19.0473 +0800",
  "procStartAbsTime" : 2503734402397,
  "procExitAbsTime" : 2503750240020,
  "procName" : "IOSReader",
  "procPath" : "\/Users\/<USER>\/Library\/Developer\/Xcode\/UserData\/Previews\/Simulator Devices\/9D65155D-655C-4E8E-91EF-CED7602892C2\/data\/Containers\/Bundle\/Application\/38E66FBD-19DB-42E9-A7F1-E053D5F0CBC3\/IOSReader.app\/IOSReader",
  "bundleInfo" : {"CFBundleShortVersionString":"1.0","CFBundleVersion":"1","CFBundleIdentifier":"none.IOSReader"},
  "storeInfo" : {"deviceIdentifierForVendor":"3F9876C3-02AC-5C2A-BBB6-7F177628F833","thirdParty":true},
  "parentProc" : "launchd_sim",
  "parentPid" : 55529,
  "coalitionName" : "com.apple.CoreSimulator.SimDevice.9D65155D-655C-4E8E-91EF-CED7602892C2",
  "crashReporterKey" : "85BC2F4D-3554-8185-EC95-674F30BE244B",
  "appleIntelligenceStatus" : {"state":"unavailable","reasons":["siriAssetIsNotReady","regionIneligible","notOptedIn","countryLocationIneligible","accessNotGranted","countryBillingIneligible"]},
  "responsiblePid" : 1151,
  "responsibleProc" : "SimulatorTrampoline",
  "codeSigningID" : "none.IOSReader",
  "codeSigningTeamID" : "",
  "codeSigningFlags" : 570425857,
  "codeSigningValidationCategory" : 10,
  "codeSigningTrustLevel" : 4294967295,
  "codeSigningAuxiliaryInfo" : 0,
  "instructionByteStream" : {"beforePC":"IAAg1IEF+LfpAwC54gMDquMDBKrkAwWq5QMGquYDB6rnAwiq6AIAlA==","atPC":"IAAg1IgSgFL\/EwC56AcA+UgAgFLoAwA5oCAA8ADQDpGjIADwY8AZkQ=="},
  "bootSessionUUID" : "4E552C7D-17C4-43C9-87FC-249AFC292998",
  "wakeTime" : 1749,
  "sleepWakeUUID" : "7466D945-D53C-44E3-B7EF-FDA1CAC21656",
  "sip" : "enabled",
  "exception" : {"codes":"0x0000000000000001, 0x0000000195712d2c","rawCodes":[1,6802189612],"type":"EXC_BREAKPOINT","signal":"SIGTRAP"},
  "termination" : {"flags":0,"code":5,"namespace":"SIGNAL","indicator":"Trace\/BPT trap: 5","byProc":"exc handler","byPid":28117},
  "os_fault" : {"process":"IOSReader"},
  "extMods" : {"caller":{"thread_create":0,"thread_set_state":0,"task_for_pid":0},"system":{"thread_create":0,"thread_set_state":0,"task_for_pid":0},"targeted":{"thread_create":0,"thread_set_state":0,"task_for_pid":0},"warnings":0},
  "faultingThread" : 0,
  "threads" : [{"triggered":true,"id":2291012,"threadState":{"x":[{"value":9223372044706781392},{"value":0},{"value":0},{"value":105553130473008},{"value":0},{"value":6155245344},{"value":101},{"value":0},{"value":341},{"value":3152680965},{"value":340},{"value":1536},{"value":2043},{"value":2045},{"value":3154780193},{"value":3152680965},{"value":3154116608},{"value":33},{"value":0},{"value":2},{"value":0},{"value":20},{"value":7852005584},{"value":586},{"value":7839502944,"symbolLocation":0,"symbol":"implicit closure #2 in Normalizing.init(min:max:stride:)"},{"value":0},{"value":4337051192},{"value":8392300824,"symbolLocation":0,"symbol":"protocol witness table for Double"},{"value":4337050192}],"flavor":"ARM_THREAD_STATE64","lr":{"value":6802189612},"cpsr":{"value":1610616832},"fp":{"value":6155245760},"sp":{"value":6155245632},"esr":{"value":4060086273,"description":"(Breakpoint) brk 1"},"pc":{"value":6802189612,"matchesCrashFrame":1},"far":{"value":0}},"queue":"com.apple.main-thread","frames":[{"imageOffset":175404,"symbol":"_assertionFailure(_:_:file:line:flags:)","symbolLocation":156,"imageIndex":8},{"imageOffset":8112036,"symbol":"precondition(_:_:file:line:)","symbolLocation":148,"imageIndex":9},{"imageOffset":3493072,"symbol":"Normalizing.init(min:max:stride:)","symbolLocation":968,"imageIndex":10},{"imageOffset":3490952,"symbol":"Slider.init<A>(value:in:step:onEditingChanged:minimumValueLabel:maximumValueLabel:customMinMaxValueLabels:label:marks:)","symbolLocation":924,"imageIndex":10},{"imageOffset":3502144,"symbol":"Slider<>.init<A>(value:in:step:onEditingChanged:)","symbolLocation":416,"imageIndex":10},{"imageOffset":13967351724,"imageIndex":11},{"imageOffset":446384,"symbol":"<deduplicated_symbol>","symbolLocation":88,"imageIndex":9},{"imageOffset":3886036,"symbol":"_VariadicView.Tree.init(_:content:)","symbolLocation":112,"imageIndex":9},{"imageOffset":3230172,"symbol":"HStack.init(alignment:spacing:content:)","symbolLocation":76,"imageIndex":9},{"imageOffset":13967347528,"imageIndex":11},{"imageOffset":446384,"symbol":"<deduplicated_symbol>","symbolLocation":88,"imageIndex":9},{"imageOffset":3886036,"symbol":"_VariadicView.Tree.init(_:content:)","symbolLocation":112,"imageIndex":9},{"imageOffset":1437252,"symbol":"VStack.init(alignment:spacing:content:)","symbolLocation":76,"imageIndex":9},{"imageOffset":13967316752,"imageIndex":11},{"imageOffset":13967314064,"imageIndex":11},{"imageOffset":446384,"symbol":"<deduplicated_symbol>","symbolLocation":88,"imageIndex":9},{"imageOffset":3886036,"symbol":"_VariadicView.Tree.init(_:content:)","symbolLocation":112,"imageIndex":9},{"imageOffset":1437252,"symbol":"VStack.init(alignment:spacing:content:)","symbolLocation":76,"imageIndex":9},{"imageOffset":13967312324,"imageIndex":11},{"imageOffset":446384,"symbol":"<deduplicated_symbol>","symbolLocation":88,"imageIndex":9},{"imageOffset":3886036,"symbol":"_VariadicView.Tree.init(_:content:)","symbolLocation":112,"imageIndex":9},{"imageOffset":446284,"symbol":"ZStack.init(alignment:content:)","symbolLocation":60,"imageIndex":9},{"imageOffset":13967311352,"imageIndex":11},{"imageOffset":4504592,"symbol":"partial apply for closure #1 in closure #1 in GeometryReader.Child.updateValue()","symbolLocation":56,"imageIndex":9},{"imageOffset":6081572,"symbol":"closure #1 in Attribute.syncMainIfReferences<A>(do:)","symbolLocation":104,"imageIndex":9},{"imageOffset":3992668,"symbol":"specialized static Update.syncMain(_:)","symbolLocation":76,"imageIndex":9},{"imageOffset":6074696,"symbol":"Attribute.syncMainIfReferences<A>(do:)","symbolLocation":280,"imageIndex":9},{"imageOffset":4504512,"symbol":"partial apply for closure #1 in GeometryReader.Child.updateValue()","symbolLocation":92,"imageIndex":9},{"imageOffset":5702872,"symbol":"closure #1 in _withObservation<A>(do:)","symbolLocation":84,"imageIndex":9},{"imageOffset":2343900,"symbol":"partial apply for closure #1 in _withObservation<A>(do:)","symbolLocation":24,"imageIndex":9},{"imageOffset":3969448,"symbol":"<deduplicated_symbol>","symbolLocation":72,"imageIndex":9},{"imageOffset":4497692,"symbol":"GeometryReader.Child.updateValue()","symbolLocation":988,"imageIndex":9},{"imageOffset":4488900,"symbol":"partial apply for implicit closure #1 in closure #1 in closure #1 in Attribute.init<A>(_:)","symbolLocation":28,"imageIndex":9},{"imageOffset":37944,"symbol":"AG::Graph::UpdateStack::update()","symbolLocation":516,"imageIndex":12},{"imageOffset":39940,"symbol":"AG::Graph::update_attribute(AG::data::ptr<AG::Node>, unsigned int)","symbolLocation":428,"imageIndex":12},{"imageOffset":70876,"symbol":"AG::Graph::input_value_ref_slow(AG::data::ptr<AG::Node>, AG::AttributeID, unsigned int, unsigned int, AGSwiftMetadata const*, unsigned char&, long)","symbolLocation":708,"imageIndex":12},{"imageOffset":159716,"symbol":"AGGraphGetValue","symbolLocation":232,"imageIndex":12},{"imageOffset":1166560,"symbol":"specialized DynamicViewList.updateValue()","symbolLocation":132,"imageIndex":9},{"imageOffset":1228416,"symbol":"specialized implicit closure #1 in closure #1 in closure #1 in Attribute.init<A>(_:)","symbolLocation":20,"imageIndex":9},{"imageOffset":37944,"symbol":"AG::Graph::UpdateStack::update()","symbolLocation":516,"imageIndex":12},{"imageOffset":39940,"symbol":"AG::Graph::update_attribute(AG::data::ptr<AG::Node>, unsigned int)","symbolLocation":428,"imageIndex":12},{"imageOffset":70876,"symbol":"AG::Graph::input_value_ref_slow(AG::data::ptr<AG::Node>, AG::AttributeID, unsigned int, unsigned int, AGSwiftMetadata const*, unsigned char&, long)","symbolLocation":708,"imageIndex":12},{"imageOffset":159716,"symbol":"AGGraphGetValue","symbolLocation":232,"imageIndex":12},{"imageOffset":4443768,"symbol":"specialized implicit closure #1 in closure #1 in closure #1 in Attribute.init<A>(_:)","symbolLocation":96,"imageIndex":9},{"imageOffset":37944,"symbol":"AG::Graph::UpdateStack::update()","symbolLocation":516,"imageIndex":12},{"imageOffset":39940,"symbol":"AG::Graph::update_attribute(AG::data::ptr<AG::Node>, unsigned int)","symbolLocation":428,"imageIndex":12},{"imageOffset":70876,"symbol":"AG::Graph::input_value_ref_slow(AG::data::ptr<AG::Node>, AG::AttributeID, unsigned int, unsigned int, AGSwiftMetadata const*, unsigned char&, long)","symbolLocation":708,"imageIndex":12},{"imageOffset":159716,"symbol":"AGGraphGetValue","symbolLocation":232,"imageIndex":12},{"imageOffset":4444224,"symbol":"specialized implicit closure #1 in closure #1 in closure #1 in Attribute.init<A>(_:)","symbolLocation":160,"imageIndex":9},{"imageOffset":37944,"symbol":"AG::Graph::UpdateStack::update()","symbolLocation":516,"imageIndex":12},{"imageOffset":39940,"symbol":"AG::Graph::update_attribute(AG::data::ptr<AG::Node>, unsigned int)","symbolLocation":428,"imageIndex":12},{"imageOffset":70876,"symbol":"AG::Graph::input_value_ref_slow(AG::data::ptr<AG::Node>, AG::AttributeID, unsigned int, unsigned int, AGSwiftMetadata const*, unsigned char&, long)","symbolLocation":708,"imageIndex":12},{"imageOffset":159716,"symbol":"AGGraphGetValue","symbolLocation":232,"imageIndex":12},{"imageOffset":4579328,"symbol":"DynamicLayoutViewAdaptor.updatedItems()","symbolLocation":64,"imageIndex":9},{"imageOffset":1107216,"symbol":"specialized DynamicContainerInfo.updateItems(disableTransitions:)","symbolLocation":60,"imageIndex":9},{"imageOffset":1103788,"symbol":"specialized DynamicContainerInfo.updateValue()","symbolLocation":484,"imageIndex":9},{"imageOffset":1234584,"symbol":"specialized implicit closure #1 in closure #1 in closure #1 in Attribute.init<A>(_:)","symbolLocation":20,"imageIndex":9},{"imageOffset":37944,"symbol":"AG::Graph::UpdateStack::update()","symbolLocation":516,"imageIndex":12},{"imageOffset":39940,"symbol":"AG::Graph::update_attribute(AG::data::ptr<AG::Node>, unsigned int)","symbolLocation":428,"imageIndex":12},{"imageOffset":94388,"symbol":"AG::Subgraph::update(unsigned int)","symbolLocation":828,"imageIndex":12},{"imageOffset":8759012,"symbol":"ViewGraph.updateOutputs(async:)","symbolLocation":388,"imageIndex":9},{"imageOffset":8721704,"symbol":"closure #1 in ViewRendererHost.render(interval:updateDisplayList:targetTimestamp:)","symbolLocation":996,"imageIndex":9},{"imageOffset":8712292,"symbol":"ViewRendererHost.render(interval:updateDisplayList:targetTimestamp:)","symbolLocation":472,"imageIndex":9},{"imageOffset":8303272,"symbol":"closure #1 in UIHostingViewBase.didMoveToWindow()","symbolLocation":332,"imageIndex":10},{"imageOffset":3431480,"symbol":"<deduplicated_symbol>","symbolLocation":20,"imageIndex":10},{"imageOffset":3432200,"symbol":"closure #1 in static UIKitUpdateCycle.addPreCommitObserver(_:)","symbolLocation":112,"imageIndex":10},{"imageOffset":3432344,"symbol":"thunk for @escaping @callee_guaranteed (@unowned UnsafeMutableRawPointer?, @unowned Double, @unowned UnsafePointer<_UIUpdateTiming>) -> ()","symbolLocation":64,"imageIndex":10},{"imageOffset":3940100,"symbol":"_UIUpdateSequenceRun","symbolLocation":76,"imageIndex":13},{"imageOffset":14203320,"symbol":"schedulerStepScheduledMainSection","symbolLocation":204,"imageIndex":13},{"imageOffset":14200276,"symbol":"runloopSourceCallback","symbolLocation":80,"imageIndex":13},{"imageOffset":603320,"symbol":"__CFRUNLOOP_IS_CALLING_OUT_TO_A_SOURCE0_PERFORM_FUNCTION__","symbolLocation":24,"imageIndex":14},{"imageOffset":603136,"symbol":"__CFRunLoopDoSource0","symbolLocation":168,"imageIndex":14},{"imageOffset":601060,"symbol":"__CFRunLoopDoSources0","symbolLocation":312,"imageIndex":14},{"imageOffset":578948,"symbol":"__CFRunLoopRun","symbolLocation":780,"imageIndex":14},{"imageOffset":577084,"symbol":"CFRunLoopRunSpecific","symbolLocation":536,"imageIndex":14},{"imageOffset":11520,"symbol":"GSEventRunModal","symbolLocation":164,"imageIndex":15},{"imageOffset":15060120,"symbol":"-[UIApplication _run]","symbolLocation":796,"imageIndex":13},{"imageOffset":15077476,"symbol":"UIApplicationMain","symbolLocation":124,"imageIndex":13},{"imageOffset":8739496,"symbol":"closure #1 in KitRendererCommon(_:)","symbolLocation":164,"imageIndex":10},{"imageOffset":8738768,"symbol":"runApp<A>(_:)","symbolLocation":84,"imageIndex":10},{"imageOffset":6171104,"symbol":"static App.main()","symbolLocation":148,"imageIndex":10},{"imageOffset":13958946472,"imageIndex":11},{"imageOffset":13958946776,"imageIndex":11},{"imageOffset":5360,"symbol":"__debug_blank_executor_run_user_entry_point","symbolLocation":128,"imageIndex":1},{"imageOffset":169112,"imageIndex":16},{"imageOffset":171824,"imageIndex":16},{"imageOffset":171596,"symbol":"__previews_injection_run_user_entrypoint","symbolLocation":12,"imageIndex":16},{"imageOffset":24664,"symbol":"__xojit_executor_run_program_wrapper","symbolLocation":1708,"imageIndex":17},{"imageOffset":9500,"imageIndex":17},{"imageOffset":171416,"imageIndex":16},{"imageOffset":3280,"symbol":"__debug_blank_executor_main","symbolLocation":992,"imageIndex":1},{"imageOffset":4314305496,"imageIndex":11},{"imageOffset":27468,"symbol":"start","symbolLocation":6000,"imageIndex":0}]},{"id":2291066,"threadState":{"x":[{"value":14},{"value":15383},{"value":171798697235},{"value":38530151612931},{"value":14680198217728},{"value":38530151612416},{"value":48},{"value":0},{"value":0},{"value":1},{"value":105553140417088},{"value":3},{"value":2},{"value":105553140417104},{"value":8384695144,"symbolLocation":0,"symbol":"OBJC_CLASS_$_OS_dispatch_semaphore"},{"value":8384695144,"symbolLocation":0,"symbol":"OBJC_CLASS_$_OS_dispatch_semaphore"},{"value":18446744073709551580},{"value":6444012696,"symbolLocation":0,"symbol":"-[OS_object retain]"},{"value":0},{"value":105553150876768},{"value":105553150876704},{"value":18446744073709551615},{"value":105553162408576},{"value":9960583168,"symbolLocation":512,"symbol":"_OBJC_PROTOCOL_$_NSObject"},{"value":9780594168},{"value":4351},{"value":0},{"value":0},{"value":284}],"flavor":"ARM_THREAD_STATE64","lr":{"value":6444016184},"cpsr":{"value":1610616832},"fp":{"value":6155856944},"sp":{"value":6155856928},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":4312009452},"far":{"value":0}},"queue":"com.apple.xojit_executor.wrapper_functions","frames":[{"imageOffset":2796,"symbol":"semaphore_wait_trap","symbolLocation":8,"imageIndex":5},{"imageOffset":12856,"symbol":"_dispatch_sema4_wait","symbolLocation":24,"imageIndex":18},{"imageOffset":14272,"symbol":"_dispatch_semaphore_wait_slow","symbolLocation":128,"imageIndex":18},{"imageOffset":9660,"imageIndex":17},{"imageOffset":22768,"symbol":"__xojit_executor_run_program_on_main_thread","symbolLocation":224,"imageIndex":17},{"imageOffset":15336,"imageIndex":17},{"imageOffset":14628,"imageIndex":17},{"imageOffset":6024,"symbol":"_dispatch_call_block_and_release","symbolLocation":24,"imageIndex":18},{"imageOffset":115320,"symbol":"_dispatch_client_callout","symbolLocation":12,"imageIndex":18},{"imageOffset":25844,"symbol":"_dispatch_continuation_pop","symbolLocation":868,"imageIndex":18},{"imageOffset":22192,"symbol":"_dispatch_async_redirect_invoke","symbolLocation":852,"imageIndex":18},{"imageOffset":84428,"symbol":"_dispatch_root_queue_drain","symbolLocation":336,"imageIndex":18},{"imageOffset":86964,"symbol":"_dispatch_worker_thread2","symbolLocation":232,"imageIndex":18},{"imageOffset":11152,"symbol":"_pthread_wqthread","symbolLocation":228,"imageIndex":6},{"imageOffset":6540,"symbol":"start_wqthread","symbolLocation":8,"imageIndex":6}]},{"id":2291067,"frames":[{"imageOffset":6532,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":6}],"threadState":{"x":[{"value":6156431360},{"value":5635},{"value":6155894784},{"value":0},{"value":409603},{"value":18446744073709551615},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":0},"cpsr":{"value":4096},"fp":{"value":0},"sp":{"value":6156431360},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":4315060612},"far":{"value":0}}},{"id":2291068,"frames":[{"imageOffset":6532,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":6}],"threadState":{"x":[{"value":6157004800},{"value":8451},{"value":6156468224},{"value":0},{"value":409604},{"value":18446744073709551615},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":0},"cpsr":{"value":4096},"fp":{"value":0},"sp":{"value":6157004800},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":4315060612},"far":{"value":0}}},{"id":2291069,"frames":[{"imageOffset":6532,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":6}],"threadState":{"x":[{"value":6157578240},{"value":11283},{"value":6157041664},{"value":0},{"value":409604},{"value":18446744073709551615},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":0},"cpsr":{"value":4096},"fp":{"value":0},"sp":{"value":6157578240},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":4315060612},"far":{"value":0}}},{"id":2291082,"frames":[{"imageOffset":6532,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":6}],"threadState":{"x":[{"value":6158151680},{"value":14923},{"value":6157615104},{"value":0},{"value":409604},{"value":18446744073709551615},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":0},"cpsr":{"value":4096},"fp":{"value":0},"sp":{"value":6158151680},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":4315060612},"far":{"value":0}}},{"id":2291083,"frames":[{"imageOffset":6532,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":6}],"threadState":{"x":[{"value":6158725120},{"value":15231},{"value":6158188544},{"value":0},{"value":409604},{"value":18446744073709551615},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":0},"cpsr":{"value":4096},"fp":{"value":0},"sp":{"value":6158725120},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":4315060612},"far":{"value":0}}},{"id":2291084,"frames":[{"imageOffset":6532,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":6}],"threadState":{"x":[{"value":6159298560},{"value":14663},{"value":6158761984},{"value":0},{"value":409604},{"value":18446744073709551615},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":0},"cpsr":{"value":4096},"fp":{"value":0},"sp":{"value":6159298560},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":4315060612},"far":{"value":0}}},{"id":2291085,"threadState":{"x":[{"value":6159868848},{"value":6565596868,"symbolLocation":204,"symbol":"type_layout_string 6Vision26DetectFaceLandmarksRequestV"},{"value":0},{"value":0},{"value":6806610044,"symbolLocation":0,"symbol":"protocol descriptor for Equatable"},{"value":4314436404},{"value":0},{"value":6159863392},{"value":18446744073709043172},{"value":6806610044,"symbolLocation":0,"symbol":"protocol descriptor for Equatable"},{"value":6159868742},{"value":0},{"value":1},{"value":8384706248,"symbolLocation":0,"symbol":"OBJC_CLASS_$_NSObject"},{"value":8384706248,"symbolLocation":0,"symbol":"OBJC_CLASS_$_NSObject"},{"value":0},{"value":4311779140,"symbolLocation":0,"symbol":"os_unfair_lock_unlock"},{"value":178},{"value":0},{"value":4320194008},{"value":6566105352},{"value":6566110568},{"value":4320194840},{"value":6566105356},{"value":6159868752},{"value":6159868743},{"value":8392492728,"symbolLocation":0,"symbol":"Conformances"},{"value":6159868728},{"value":6159868768}],"flavor":"ARM_THREAD_STATE64","lr":{"value":6805239864},"cpsr":{"value":2147487744},"fp":{"value":6159868992},"sp":{"value":6159868624},"esr":{"value":2449473543,"description":"(Data Abort) byte read Translation fault"},"pc":{"value":6805239852},"far":{"value":0}},"queue":"com.apple.root.utility-qos","frames":[{"imageOffset":3225644,"symbol":"swift_conformsToProtocolMaybeInstantiateSuperclasses(swift::TargetMetadata<swift::InProcess> const*, swift::TargetProtocolDescriptor<swift::InProcess> const*, bool)","symbolLocation":2704,"imageIndex":8},{"imageOffset":3218632,"symbol":"swift_conformsToProtocol","symbolLocation":52,"imageIndex":8},{"imageOffset":115688,"symbol":"AG::LayoutDescriptor::make_layout(AG::swift::metadata const*, AGComparisonMode, AG::LayoutDescriptor::HeapMode)","symbolLocation":120,"imageIndex":12},{"imageOffset":116944,"symbol":"AG::(anonymous namespace)::TypeDescriptorCache::fetch(AG::swift::metadata const*, unsigned int, AG::LayoutDescriptor::HeapMode, int)","symbolLocation":384,"imageIndex":12},{"imageOffset":112348,"symbol":"AG::LayoutDescriptor::Builder::should_visit_fields(AG::swift::metadata const*, bool)","symbolLocation":56,"imageIndex":12},{"imageOffset":113256,"symbol":"AG::LayoutDescriptor::Builder::visit_case(AG::swift::metadata const*, AG::swift::field_record const&, unsigned int)","symbolLocation":376,"imageIndex":12},{"imageOffset":24612,"symbol":"AG::swift::metadata::visit(AG::swift::metadata_visitor&) const","symbolLocation":588,"imageIndex":12},{"imageOffset":116024,"symbol":"AG::LayoutDescriptor::make_layout(AG::swift::metadata const*, AGComparisonMode, AG::LayoutDescriptor::HeapMode)","symbolLocation":456,"imageIndex":12},{"imageOffset":116944,"symbol":"AG::(anonymous namespace)::TypeDescriptorCache::fetch(AG::swift::metadata const*, unsigned int, AG::LayoutDescriptor::HeapMode, int)","symbolLocation":384,"imageIndex":12},{"imageOffset":112348,"symbol":"AG::LayoutDescriptor::Builder::should_visit_fields(AG::swift::metadata const*, bool)","symbolLocation":56,"imageIndex":12},{"imageOffset":112008,"symbol":"AG::LayoutDescriptor::Builder::visit_element(AG::swift::metadata const*, AG::swift::metadata::ref_kind, unsigned long, unsigned long)","symbolLocation":116,"imageIndex":12},{"imageOffset":26940,"symbol":"AG::swift::metadata_visitor::visit_field(AG::swift::metadata const*, AG::swift::field_record const&, unsigned long, unsigned long)","symbolLocation":132,"imageIndex":12},{"imageOffset":24804,"symbol":"AG::swift::metadata::visit(AG::swift::metadata_visitor&) const","symbolLocation":780,"imageIndex":12},{"imageOffset":116024,"symbol":"AG::LayoutDescriptor::make_layout(AG::swift::metadata const*, AGComparisonMode, AG::LayoutDescriptor::HeapMode)","symbolLocation":456,"imageIndex":12},{"imageOffset":116944,"symbol":"AG::(anonymous namespace)::TypeDescriptorCache::fetch(AG::swift::metadata const*, unsigned int, AG::LayoutDescriptor::HeapMode, int)","symbolLocation":384,"imageIndex":12},{"imageOffset":112348,"symbol":"AG::LayoutDescriptor::Builder::should_visit_fields(AG::swift::metadata const*, bool)","symbolLocation":56,"imageIndex":12},{"imageOffset":112008,"symbol":"AG::LayoutDescriptor::Builder::visit_element(AG::swift::metadata const*, AG::swift::metadata::ref_kind, unsigned long, unsigned long)","symbolLocation":116,"imageIndex":12},{"imageOffset":26940,"symbol":"AG::swift::metadata_visitor::visit_field(AG::swift::metadata const*, AG::swift::field_record const&, unsigned long, unsigned long)","symbolLocation":132,"imageIndex":12},{"imageOffset":24804,"symbol":"AG::swift::metadata::visit(AG::swift::metadata_visitor&) const","symbolLocation":780,"imageIndex":12},{"imageOffset":116024,"symbol":"AG::LayoutDescriptor::make_layout(AG::swift::metadata const*, AGComparisonMode, AG::LayoutDescriptor::HeapMode)","symbolLocation":456,"imageIndex":12},{"imageOffset":116944,"symbol":"AG::(anonymous namespace)::TypeDescriptorCache::fetch(AG::swift::metadata const*, unsigned int, AG::LayoutDescriptor::HeapMode, int)","symbolLocation":384,"imageIndex":12},{"imageOffset":112348,"symbol":"AG::LayoutDescriptor::Builder::should_visit_fields(AG::swift::metadata const*, bool)","symbolLocation":56,"imageIndex":12},{"imageOffset":112008,"symbol":"AG::LayoutDescriptor::Builder::visit_element(AG::swift::metadata const*, AG::swift::metadata::ref_kind, unsigned long, unsigned long)","symbolLocation":116,"imageIndex":12},{"imageOffset":26940,"symbol":"AG::swift::metadata_visitor::visit_field(AG::swift::metadata const*, AG::swift::field_record const&, unsigned long, unsigned long)","symbolLocation":132,"imageIndex":12},{"imageOffset":24804,"symbol":"AG::swift::metadata::visit(AG::swift::metadata_visitor&) const","symbolLocation":780,"imageIndex":12},{"imageOffset":116024,"symbol":"AG::LayoutDescriptor::make_layout(AG::swift::metadata const*, AGComparisonMode, AG::LayoutDescriptor::HeapMode)","symbolLocation":456,"imageIndex":12},{"imageOffset":122036,"symbol":"AG::(anonymous namespace)::TypeDescriptorCache::drain_queue(void*)","symbolLocation":360,"imageIndex":12},{"imageOffset":115320,"symbol":"_dispatch_client_callout","symbolLocation":12,"imageIndex":18},{"imageOffset":226128,"symbol":"<deduplicated_symbol>","symbolLocation":28,"imageIndex":18},{"imageOffset":85008,"symbol":"_dispatch_root_queue_drain","symbolLocation":916,"imageIndex":18},{"imageOffset":86964,"symbol":"_dispatch_worker_thread2","symbolLocation":232,"imageIndex":18},{"imageOffset":11152,"symbol":"_pthread_wqthread","symbolLocation":228,"imageIndex":6},{"imageOffset":6540,"symbol":"start_wqthread","symbolLocation":8,"imageIndex":6}]},{"id":2291086,"frames":[{"imageOffset":6532,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":6}],"threadState":{"x":[{"value":6160445440},{"value":12551},{"value":6159908864},{"value":0},{"value":409604},{"value":18446744073709551615},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":0},"cpsr":{"value":4096},"fp":{"value":0},"sp":{"value":6160445440},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":4315060612},"far":{"value":0}}},{"id":2291087,"name":"com.apple.uikit.eventfetch-thread","threadState":{"x":[{"value":268451845},{"value":21592279046},{"value":8589934592},{"value":84675280240640},{"value":4294967295},{"value":84675280240640},{"value":2},{"value":4294967295},{"value":0},{"value":17179869184},{"value":0},{"value":2},{"value":0},{"value":0},{"value":19715},{"value":3072},{"value":18446744073709551569},{"value":43},{"value":0},{"value":4294967295},{"value":2},{"value":84675280240640},{"value":4294967295},{"value":84675280240640},{"value":6161014152},{"value":8589934592},{"value":21592279046},{"value":18446744073709550527},{"value":4412409862}],"flavor":"ARM_THREAD_STATE64","lr":{"value":4312080152},"cpsr":{"value":4096},"fp":{"value":6161014000},"sp":{"value":6161013920},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":4312009584},"far":{"value":0}},"frames":[{"imageOffset":2928,"symbol":"mach_msg2_trap","symbolLocation":8,"imageIndex":5},{"imageOffset":73496,"symbol":"mach_msg2_internal","symbolLocation":72,"imageIndex":5},{"imageOffset":35880,"symbol":"mach_msg_overwrite","symbolLocation":480,"imageIndex":5},{"imageOffset":3800,"symbol":"mach_msg","symbolLocation":20,"imageIndex":5},{"imageOffset":601364,"symbol":"__CFRunLoopServiceMachPort","symbolLocation":156,"imageIndex":14},{"imageOffset":579316,"symbol":"__CFRunLoopRun","symbolLocation":1148,"imageIndex":14},{"imageOffset":577084,"symbol":"CFRunLoopRunSpecific","symbolLocation":536,"imageIndex":14},{"imageOffset":7302620,"symbol":"-[NSRunLoop(NSRunLoop) runMode:beforeDate:]","symbolLocation":208,"imageIndex":20},{"imageOffset":7303164,"symbol":"-[NSRunLoop(NSRunLoop) runUntilDate:]","symbolLocation":60,"imageIndex":20},{"imageOffset":15771428,"symbol":"-[UIEventFetcher threadMain]","symbolLocation":408,"imageIndex":13},{"imageOffset":7461784,"symbol":"__NSThread__start__","symbolLocation":716,"imageIndex":20},{"imageOffset":26096,"symbol":"_pthread_start","symbolLocation":104,"imageIndex":6},{"imageOffset":6552,"symbol":"thread_start","symbolLocation":8,"imageIndex":6}]},{"id":2291089,"frames":[{"imageOffset":6532,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":6}],"threadState":{"x":[{"value":6161592320},{"value":23555},{"value":6161055744},{"value":0},{"value":409604},{"value":18446744073709551615},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":0},"cpsr":{"value":4096},"fp":{"value":0},"sp":{"value":6161592320},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":4315060612},"far":{"value":0}}},{"id":2291090,"frames":[{"imageOffset":6532,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":6}],"threadState":{"x":[{"value":6162165760},{"value":23811},{"value":6161629184},{"value":0},{"value":409604},{"value":18446744073709551615},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":0},"cpsr":{"value":4096},"fp":{"value":0},"sp":{"value":6162165760},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":4315060612},"far":{"value":0}}},{"id":2291091,"frames":[{"imageOffset":6532,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":6}],"threadState":{"x":[{"value":6162739200},{"value":24067},{"value":6162202624},{"value":0},{"value":409604},{"value":18446744073709551615},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":0},"cpsr":{"value":4096},"fp":{"value":0},"sp":{"value":6162739200},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":4315060612},"far":{"value":0}}},{"id":2291092,"frames":[{"imageOffset":6532,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":6}],"threadState":{"x":[{"value":6163312640},{"value":24323},{"value":6162776064},{"value":0},{"value":409604},{"value":18446744073709551615},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":0},"cpsr":{"value":4096},"fp":{"value":0},"sp":{"value":6163312640},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":4315060612},"far":{"value":0}}},{"id":2291093,"frames":[{"imageOffset":6532,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":6}],"threadState":{"x":[{"value":6163886080},{"value":24579},{"value":6163349504},{"value":0},{"value":409604},{"value":18446744073709551615},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":0},"cpsr":{"value":4096},"fp":{"value":0},"sp":{"value":6163886080},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":4315060612},"far":{"value":0}}},{"id":2291094,"frames":[{"imageOffset":6532,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":6}],"threadState":{"x":[{"value":6164459520},{"value":24835},{"value":6163922944},{"value":0},{"value":409604},{"value":18446744073709551615},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":0},"cpsr":{"value":4096},"fp":{"value":0},"sp":{"value":6164459520},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":4315060612},"far":{"value":0}}},{"id":2291095,"frames":[{"imageOffset":6532,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":6}],"threadState":{"x":[{"value":6165032960},{"value":25091},{"value":6164496384},{"value":0},{"value":409604},{"value":18446744073709551615},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":0},"cpsr":{"value":4096},"fp":{"value":0},"sp":{"value":6165032960},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":4315060612},"far":{"value":0}}},{"id":2291096,"frames":[{"imageOffset":6532,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":6}],"threadState":{"x":[{"value":6165606400},{"value":25347},{"value":6165069824},{"value":0},{"value":409604},{"value":18446744073709551615},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":0},"cpsr":{"value":4096},"fp":{"value":0},"sp":{"value":6165606400},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":4315060612},"far":{"value":0}}},{"id":2291097,"frames":[{"imageOffset":6532,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":6}],"threadState":{"x":[{"value":6166179840},{"value":25603},{"value":6165643264},{"value":0},{"value":409604},{"value":18446744073709551615},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":0},"cpsr":{"value":4096},"fp":{"value":0},"sp":{"value":6166179840},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":4315060612},"far":{"value":0}}},{"id":2291098,"frames":[{"imageOffset":6532,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":6}],"threadState":{"x":[{"value":6166753280},{"value":25859},{"value":6166216704},{"value":0},{"value":409604},{"value":18446744073709551615},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":0},"cpsr":{"value":4096},"fp":{"value":0},"sp":{"value":6166753280},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":4315060612},"far":{"value":0}}},{"id":2291099,"frames":[{"imageOffset":6532,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":6}],"threadState":{"x":[{"value":6167326720},{"value":26115},{"value":6166790144},{"value":0},{"value":409604},{"value":18446744073709551615},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0},{"value":0}],"flavor":"ARM_THREAD_STATE64","lr":{"value":0},"cpsr":{"value":4096},"fp":{"value":0},"sp":{"value":6167326720},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":4315060612},"far":{"value":0}}},{"id":2291102,"name":"JavaScriptCore libpas scavenger","threadState":{"x":[{"value":260},{"value":0},{"value":0},{"value":0},{"value":0},{"value":160},{"value":0},{"value":99999008},{"value":6167899816},{"value":0},{"value":0},{"value":2},{"value":2},{"value":0},{"value":0},{"value":0},{"value":305},{"value":7},{"value":0},{"value":5528578240},{"value":5528578304},{"value":6167900384},{"value":99999008},{"value":0},{"value":0},{"value":257},{"value":512},{"value":8393973760,"symbolLocation":7888,"symbol":"pas_compact_expendable_memory_header"},{"value":8393965568,"symbolLocation":4900,"symbol":"jit_common_primitive_heap_support"}],"flavor":"ARM_THREAD_STATE64","lr":{"value":4315081400},"cpsr":{"value":1610616832},"fp":{"value":6167899936},"sp":{"value":6167899792},"esr":{"value":1442840704,"description":" Address size fault"},"pc":{"value":4312023060},"far":{"value":0}},"frames":[{"imageOffset":16404,"symbol":"__psynch_cvwait","symbolLocation":8,"imageIndex":5},{"imageOffset":27320,"symbol":"_pthread_cond_wait","symbolLocation":976,"imageIndex":6},{"imageOffset":23300840,"symbol":"scavenger_thread_main","symbolLocation":1176,"imageIndex":21},{"imageOffset":26096,"symbol":"_pthread_start","symbolLocation":104,"imageIndex":6},{"imageOffset":6552,"symbol":"thread_start","symbolLocation":8,"imageIndex":6}]}],
  "usedImages" : [
  {
    "source" : "P",
    "arch" : "arm64e",
    "base" : 4313055232,
    "size" : 638976,
    "uuid" : "aca43a8d-6369-3a2c-af92-3d4c458523d6",
    "path" : "\/usr\/lib\/dyld",
    "name" : "dyld"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4311580672,
    "CFBundleShortVersionString" : "1.0",
    "CFBundleIdentifier" : "none.IOSReader",
    "size" : 16384,
    "uuid" : "de94f987-9d68-3034-8981-871137e28a27",
    "path" : "\/Users\/<USER>\/Library\/Developer\/Xcode\/UserData\/Previews\/Simulator Devices\/9D65155D-655C-4E8E-91EF-CED7602892C2\/data\/Containers\/Bundle\/Application\/38E66FBD-19DB-42E9-A7F1-E053D5F0CBC3\/IOSReader.app\/IOSReader",
    "name" : "IOSReader",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4311728128,
    "size" : 16384,
    "uuid" : "9f12880f-66c3-36d5-b0db-8a0202df4d07",
    "path" : "\/Users\/<USER>\/Library\/Developer\/Xcode\/UserData\/Previews\/Simulator Devices\/9D65155D-655C-4E8E-91EF-CED7602892C2\/data\/Containers\/Bundle\/Application\/38E66FBD-19DB-42E9-A7F1-E053D5F0CBC3\/IOSReader.app\/__preview.dylib",
    "name" : "__preview.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4311924736,
    "size" : 16384,
    "uuid" : "c8912565-0eb8-3883-a87e-48748e221994",
    "path" : "\/Volumes\/VOLUME\/*\/libLogRedirect.dylib",
    "name" : "libLogRedirect.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4311777280,
    "size" : 32768,
    "uuid" : "98803d0c-942b-350b-baf1-4879fd637409",
    "path" : "\/usr\/lib\/system\/libsystem_platform.dylib",
    "name" : "libsystem_platform.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4312006656,
    "size" : 245760,
    "uuid" : "02dd445e-88c2-3ac8-979a-cf2b39185f71",
    "path" : "\/usr\/lib\/system\/libsystem_kernel.dylib",
    "name" : "libsystem_kernel.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4315054080,
    "size" : 65536,
    "uuid" : "e4d4a6eb-1bf6-3f43-a21d-d042f2771dc4",
    "path" : "\/usr\/lib\/system\/libsystem_pthread.dylib",
    "name" : "libsystem_pthread.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 4316413952,
    "size" : 49152,
    "uuid" : "22dfc621-7386-3de3-87b2-9ed27c8be84d",
    "path" : "\/Volumes\/VOLUME\/*\/libobjc-trampolines.dylib",
    "name" : "libobjc-trampolines.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 6802014208,
    "size" : 4731648,
    "uuid" : "4f21129f-ede4-3571-b8f0-99188db009fe",
    "path" : "\/Volumes\/VOLUME\/*\/libswiftCore.dylib",
    "name" : "libswiftCore.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 7854489600,
    "CFBundleShortVersionString" : "6.4.41",
    "CFBundleIdentifier" : "com.apple.SwiftUICore",
    "size" : 10327104,
    "uuid" : "0027f0a4-da0d-31e2-a055-a36b8099fe44",
    "path" : "\/Volumes\/VOLUME\/*\/SwiftUICore.framework\/SwiftUICore",
    "name" : "SwiftUICore",
    "CFBundleVersion" : "6.4.41"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 7836000256,
    "CFBundleShortVersionString" : "6.4.41",
    "CFBundleIdentifier" : "com.apple.SwiftUI",
    "size" : 18488000,
    "uuid" : "05d96221-a9d9-39c5-81cf-c72e8ae73467",
    "path" : "\/Volumes\/VOLUME\/*\/SwiftUI.framework\/SwiftUI",
    "name" : "SwiftUI",
    "CFBundleVersion" : "6.4.41"
  },
  {
    "size" : 0,
    "source" : "A",
    "base" : 0,
    "uuid" : "00000000-0000-0000-0000-000000000000"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 7514820608,
    "CFBundleShortVersionString" : "6.4.39.2",
    "CFBundleIdentifier" : "com.apple.AttributeGraph",
    "size" : 247552,
    "uuid" : "f1845a94-6b4b-325a-b68e-a1c58f629ea2",
    "path" : "\/Volumes\/VOLUME\/*\/AttributeGraph.framework\/AttributeGraph",
    "name" : "AttributeGraph",
    "CFBundleVersion" : "6.4.39.2"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 6523658240,
    "CFBundleShortVersionString" : "1.0",
    "CFBundleIdentifier" : "com.apple.UIKitCore",
    "size" : 30841056,
    "uuid" : "d4c23b9a-c567-3e42-86ef-697aec976159",
    "path" : "\/Volumes\/VOLUME\/*\/UIKitCore.framework\/UIKitCore",
    "name" : "UIKitCore",
    "CFBundleVersion" : "8444.1.105"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 6446206976,
    "CFBundleShortVersionString" : "6.9",
    "CFBundleIdentifier" : "com.apple.CoreFoundation",
    "size" : 4292608,
    "uuid" : "3d4aa1d5-03aa-3365-b767-944509b9bbfd",
    "path" : "\/Volumes\/VOLUME\/*\/CoreFoundation.framework\/CoreFoundation",
    "name" : "CoreFoundation",
    "CFBundleVersion" : "3423"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 6727008256,
    "CFBundleShortVersionString" : "1.0",
    "CFBundleIdentifier" : "com.apple.GraphicsServices",
    "size" : 33280,
    "uuid" : "eeb999f0-53c2-31ce-b203-3c78fb303dab",
    "path" : "\/Volumes\/VOLUME\/*\/GraphicsServices.framework\/GraphicsServices",
    "name" : "GraphicsServices",
    "CFBundleVersion" : "1.0"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 9480253440,
    "CFBundleShortVersionString" : "16.0",
    "CFBundleIdentifier" : "com.apple.dt.PreviewsInjection",
    "size" : 461600,
    "uuid" : "5e953c9b-7805-3ecf-b4b7-d4ae88f00745",
    "path" : "\/Volumes\/VOLUME\/*\/PreviewsInjection.framework\/PreviewsInjection",
    "name" : "PreviewsInjection",
    "CFBundleVersion" : "22.30.25.1"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 9780584448,
    "CFBundleShortVersionString" : "1.0",
    "CFBundleIdentifier" : "com.apple.xojit.XOJITExecutor",
    "size" : 52779,
    "uuid" : "38d76d2b-ab26-3b23-a020-bb1923958b1e",
    "path" : "\/Volumes\/VOLUME\/*\/XOJITExecutor.framework\/XOJITExecutor",
    "name" : "XOJITExecutor",
    "CFBundleVersion" : "59"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 6444003328,
    "size" : 281440,
    "uuid" : "7bee27fd-f519-330d-aebe-d6ace467df22",
    "path" : "\/Volumes\/VOLUME\/*\/libdispatch.dylib",
    "name" : "libdispatch.dylib"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 6561628160,
    "CFBundleShortVersionString" : "********",
    "CFBundleIdentifier" : "com.apple.VN",
    "size" : 5253056,
    "uuid" : "2ec273d8-f591-3e5c-a5bd-604c624c9982",
    "path" : "\/Volumes\/VOLUME\/*\/Vision.framework\/Vision",
    "name" : "Vision",
    "CFBundleVersion" : "********"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 6451019776,
    "CFBundleShortVersionString" : "6.9",
    "CFBundleIdentifier" : "com.apple.Foundation",
    "size" : 12343520,
    "uuid" : "6ec60314-780a-318f-8bdb-5d173b13970e",
    "path" : "\/Volumes\/VOLUME\/*\/Foundation.framework\/Foundation",
    "name" : "Foundation",
    "CFBundleVersion" : "3423"
  },
  {
    "source" : "P",
    "arch" : "arm64",
    "base" : 6846148608,
    "CFBundleShortVersionString" : "8621",
    "CFBundleIdentifier" : "com.apple.JavaScriptCore",
    "size" : 25371200,
    "uuid" : "5ebfe6ea-8077-3a92-9761-965c0e9ea6e6",
    "path" : "\/Volumes\/VOLUME\/*\/JavaScriptCore.framework\/JavaScriptCore",
    "name" : "JavaScriptCore",
    "CFBundleVersion" : "8621.*********"
  }
],
  "sharedCache" : {
  "base" : 6442450944,
  "size" : 3904126976,
  "uuid" : "1b5110a7-df94-3eaa-a80c-ac7472b2977b"
},
  "vmSummary" : "ReadOnly portion of Libraries: Total=904.9M resident=0K(0%) swapped_out_or_unallocated=904.9M(100%)\nWritable regions: Total=1.8G written=1815K(0%) resident=1815K(0%) swapped_out=0K(0%) unallocated=1.8G(100%)\n\n                                VIRTUAL   REGION \nREGION TYPE                        SIZE    COUNT (non-coalesced) \n===========                     =======  ======= \nActivity Tracing                   256K        1 \nAttributeGraph Data               1024K        1 \nColorSync                           48K        3 \nFoundation                          16K        1 \nJS JIT generated code            512.0M        3 \nKernel Alloc Once                   32K        1 \nMALLOC                           610.2M       51 \nMALLOC guard page                  192K       12 \nSQLite page cache                  128K        1 \nSTACK GUARD                       56.4M       23 \nStack                             19.7M       23 \nVM_ALLOCATE                        3.1G        6 \nVM_ALLOCATE (reserved)           384.0M        1         reserved VM address space (unallocated)\nWebKit Malloc                    192.8M        7 \n__DATA                            17.9M      509 \n__DATA_CONST                      61.9M      527 \n__DATA_DIRTY                        91K       11 \n__FONT_DATA                        2352        1 \n__LINKEDIT                       331.1M        9 \n__OBJC_RO                         61.2M        1 \n__OBJC_RW                         2723K        1 \n__TEXT                           573.8M      541 \n__TPRO_CONST                       148K        2 \ndyld private memory                2.5G       12 \nmapped file                       41.0M      146 \npage table in kernel              1815K        1 \nshared memory                     5296K       82 \n===========                     =======  ======= \nTOTAL                              8.4G     1977 \nTOTAL, minus reserved VM space     8.0G     1977 \n",
  "legacyInfo" : {
  "threadTriggered" : {
    "queue" : "com.apple.main-thread"
  }
},
  "logWritingSignature" : "ca236a735b8d13ccc3a657245a0845d5f028a46d",
  "trialInfo" : {
  "rollouts" : [
    {
      "rolloutId" : "60186475825c62000ccf5450",
      "factorPackIds" : {

      },
      "deploymentId" : 240000083
    },
    {
      "rolloutId" : "6297d96be2c9387df974efa4",
      "factorPackIds" : {

      },
      "deploymentId" : 240000032
    }
  ],
  "experiments" : [
    {
      "treatmentId" : "28060e10-d4e5-4163-aa2b-d8bd088d5cbf",
      "experimentId" : "6685b283afc7c17197d69eec",
      "deploymentId" : 400000008
    },
    {
      "treatmentId" : "4cfb9672-7b7b-42d8-a7dc-b675ffa2f5dc",
      "experimentId" : "67f46877b1ea9f1f114b8d0b",
      "deploymentId" : 400000001
    }
  ]
}
}

Model: MacBookPro18,3, BootROM 11881.101.1, proc 10:8:2 processors, 16 GB, SMC 
Graphics: Apple M1 Pro, Apple M1 Pro, Built-In
Display: Color LCD, 3024 x 1964 Retina, Main, MirrorOff, Online
Memory Module: LPDDR5, Samsung
AirPort: spairport_wireless_card_type_wifi (0x14E4, 0x4387), wl0: Feb 22 2025 01:26:18 version 20.130.16.0.8.7.195 FWID 01-81c38859
IO80211_driverkit-1475.34 "IO80211_driverkit-1475.34" Mar  9 2025 20:59:13
AirPort: 
Bluetooth: Version (null), 0 services, 0 devices, 0 incoming serial ports
Network Service: Wi-Fi, AirPort, en0
USB Device: USB31Bus
USB Device: USB31Bus
USB Device: USB31Bus
Thunderbolt Bus: MacBook Pro, Apple Inc.
Thunderbolt Bus: MacBook Pro, Apple Inc.
Thunderbolt Bus: MacBook Pro, Apple Inc.
