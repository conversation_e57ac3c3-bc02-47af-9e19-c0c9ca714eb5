//
// ReadingRecord.swift
// IOSReader
//
// Created by Trae AI on 2024/07/26.
//

import Foundation
import SwiftData

@Model
final class ReadingRecord {
    var id: UUID
    var bookID: UUID // Link to the Book model
    var startTime: Date
    var endTime: Date?
    var startProgress: Double // e.g., percentage or page number
    var endProgress: Double?
    var notes: String?

    init(id: UUID = UUID(), bookID: UUID, startTime: Date = Date(), endTime: Date? = nil, startProgress: Double, endProgress: Double? = nil, notes: String? = nil) {
        self.id = id
        self.bookID = bookID
        self.startTime = startTime
        self.endTime = endTime
        self.startProgress = startProgress
        self.endProgress = endProgress
        self.notes = notes
    }
}