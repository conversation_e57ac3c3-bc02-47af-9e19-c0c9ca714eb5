// MARK: - 主类定义

/// 阅读器核心协调器类
/// 主要职责:
/// - 作为阅读器模块的核心控制器，协调各个子模块的工作
/// - 管理阅读器的全局状态和设置
/// - 处理用户交互和业务逻辑
/// - 提供统一的API供UI层调用
///
/// 设计特点:
/// - 使用Combine框架实现响应式状态管理
/// - 支持并行渲染提高性能
/// - 提供统一的API供UI层调用

import Foundation
import SwiftUI
import PDFKit
// 移除了 ZIPFoundation 导入,因为该模块不存在
import Combine
import UniformTypeIdentifiers
import SwiftData
// import Observation // 添加 Observation 导入

// @Observable // 暂时注释掉，使用 ObservableObject
//import SwiftUI // 确保导入 SwiftUI 以使用 ObservableObject



// MARK: - 类型定义

// 定义可缓存的图片协议
protocol CacheableImage: AnyObject {
    var image: Image { get }
}

// 图片包装器类，实现CacheableImage协议
@MainActor
final class ImageWrapper: CacheableImage {
    let image: Image
    init(_ image: Image) {
        self.image = image
    }
}


// 渲染进度追踪器
actor RenderProgress {
    var renderedPages: [Int: [Image]] = [:]
    var failedPages: Set<Int> = []
    
    func markPageRendered(_ index: Int, pages: [Image]) {
        renderedPages[index] = pages
    }
            
    func markPageFailed(_ index: Int) {
        failedPages.insert(index)
    }
            
    func getRenderedPages() -> [Int: [Image]] {
        return renderedPages
    }
            
    func getFailedPages() -> Set<Int> {
        return failedPages
    }
    
    func getAllRenderedPages() -> [Image] {
        return renderedPages
            .sorted { $0.key < $1.key }
            .flatMap { $0.value }
    }
}

// MARK: - 错误定义
enum ReaderError: LocalizedError {
    case fileNotFound
    case unsupportedFileType
    case chapterLoadingFailed
    case loadingFailed(String)
    
    var errorDescription: String? {
        switch self {
        case .fileNotFound:
            return "文件不存在"
        case .unsupportedFileType:
            return "不支持的文件类型"
        case .loadingFailed(let message):
            return "加载失败：\(message)"
        case .chapterLoadingFailed:
            return "章节加载失败"
        }
    }
}

@MainActor // 确保所有操作在主线程执行，解决并发问题
public class ReaderCoordinator: ObservableObject, @unchecked Sendable {
    // MARK: - 属性
    
    /// 预览用的协调器实例
    static var preview: ReaderCoordinator {
        do {
            let config = ModelConfiguration(isStoredInMemoryOnly: true)
            let container = try ModelContainer(for: Book.self, BookSource.self, ReadingProgress.self, Bookmark.self, configurations: config)
            let sourceManager = BookSourceManager(modelContainer: container)
            let readerViewModel = ReaderViewModel(modelContainer: container)
            let coordinator = ReaderCoordinator(modelContainer: container)
            coordinator.readerViewModel = readerViewModel
            coordinator.sourceManager = sourceManager
            return coordinator
        } catch {
            fatalError("无法创建预览 ModelContainer: \(error.localizedDescription)")
        }
    }
    @Published var document: PDFDocument? = nil
    @Published var pages: [Image] = []
    @Published var currentPage: Int = 0
    @Published var totalPages: Int = 0
    @Published var alertTitle: String = ""
    @Published var alertMessage: String = ""
    @Published var showAlert: Bool = false
    // MARK: - 错误处理方法
    
    /// 显示错误提示
    /// - Parameters:
    ///   - title: 错误标题
    ///   - message: 错误信息
    private func showError(title: String, message: String) {
        self.alertTitle = title
        self.alertMessage = message
        self.showAlert = true
    }
    @Published var themeColor: Color = .white
    @Published var readingMode: ReadingMode = .day // 默认设置为日间模式
    @Published var currentBookSource: BookSource? = nil
    @Published var bookSources: [BookSource] = []
    @Published var books: [Book] = []
    @Published var currentBook: Book? = nil
    @Published var bookmarks: [String: [Bookmark]] = [:]
    @Published var readingProgress: [String: (chapterIndex: Int, pageNumber: Int)] = [:] // 添加 readingProgress 声明
    @Published var readerViewModel: ReaderViewModel
    lazy var bookShelfViewModel: BookListViewModel = BookListViewModel(sourceManager: self.sourceManager)
    // Note: Changed to lazy var and removed @Published to resolve initialization order issue.
    let modelContainer: ModelContainer // 模型容器，改为internal访问级别
    
    // Properties for ReaderToolbars
    @Published var isListening: Bool = false
    @Published var showChapters: Bool = false
    @Published var showSettings: Bool = false
    @Published var showSourceManager: Bool = false // 控制书源管理界面显示
    
    /// 切换到上一章节
    func previousChapter() async {
        await readerViewModel.previousChapter()
    }

    // MARK: - Reading Mode
    
    /// 切换阅读模式（日间/夜间）
    func toggleReadingMode() {
        // TODO: 实现阅读模式切换逻辑
        // 例如：readingMode = (readingMode == .day) ? .night : .day
        print("toggleReadingMode called")
    }
    
    // SwiftData ModelContext
    @Published var currentChapterIndex: Int = 0
    @Published public var fontSize: CGFloat = 16.0 // 默认字体大小
    @Published var isLoading: Bool = false // 加载状态
    @Published var errorMessage: String? = nil // 错误信息
    @Published var showingURLInputSheet: Bool = false // 用于控制书源管理中的URL输入弹窗
    private var cancellables = Set<AnyCancellable>() // 用于存储 Combine 订阅
    
    // 核心组件
    private let readerCore: ReaderCore
    private let contentFetcher: ContentFetcher
    private let backupManager: BackupManager
    
    // MARK: - Backup and Restore Methods
    
    /// 启动备份过程
    /// - Parameter location: 备份位置 (本地或云端)
    /// - Throws: 备份过程中的错误
    func startBackup(to location: BackupLocation) async throws {
        // TODO: 实现备份逻辑
        // 示例：调用 backupManager 的方法
        print("ReaderCoordinator: startBackup to \(location)")
        // 模拟异步操作
        try await Task.sleep(nanoseconds: 1_000_000_000) // 1秒延迟
        // 假设备份成功
        // 如果需要处理错误：throw BackupError.backupFailed("Simulated backup error")
    }
    
    /// 启动恢复过程
    /// - Parameters:
    ///   - location: 恢复位置 (本地或云端)
    ///   - fileURL: (可选) 从本地恢复时指定的文件URL
    /// - Throws: 恢复过程中的错误
    func startRestore(from location: BackupLocation, fileURL: URL? = nil) async throws {
        // TODO: 实现恢复逻辑
        // 示例：调用 backupManager 的方法
        print("ReaderCoordinator: startRestore from \(location) with file: \(fileURL?.lastPathComponent ?? "N/A")")
        // 模拟异步操作
        try await Task.sleep(nanoseconds: 1_000_000_000) // 1秒延迟
        // 假设恢复成功
        // 如果需要处理错误：throw RestoreError.restoreFailed("Simulated restore error")
    }
    
    // 内容相关
    @Published var chapters: [(title: String, index: Int)] = [] // 章节列表
    @Published var bookTitle: String = "" // 书籍标题
    
    // 书源管理器
    @Published var sourceManager: BookSourceManager
    // 选中的书籍，用于通知ContentView打开阅读视图
    let selectedBook = PassthroughSubject<Book?, Never>()
    
    // MARK: - 书签管理 UI 交互
    
    /// 在当前页面添加书签
    @MainActor
    public func addBookmarkAtCurrentPage(description: String? = nil) async {
        guard let book = currentBook else {
            alertTitle = "错误"
            alertMessage = "无法添加书签，请先打开一本书籍。"
            showAlert = true
            return
        }
        
        let bookmark = Bookmark(
            bookId: book.id,
            pageIndex: currentPage, // 调整顺序
            chapterIndex: currentChapterIndex,
            content: description ?? "第\(currentChapterIndex + 1)章 - 第\(currentPage + 1)页",
            createdAt: Date()
        )
        
        // 更新书签列表
        var currentBookmarks = bookmarks[book.id.uuidString] ?? []
        currentBookmarks.append(bookmark)
        bookmarks[book.id.uuidString] = currentBookmarks
        modelContainer.mainContext.insert(bookmark)
    }
    
    /// 删除书签
    public func deleteBookmark(_ bookmarkToDelete: Bookmark) {
        guard let book = currentBook else {
            print("ReaderCoordinator: 无法删除书签，当前没有打开的书籍。")
            return
        }
        let bookId = book.id.uuidString
        
        // 从 SwiftData 删除
        modelContainer.mainContext.delete(bookmarkToDelete)
        
        // 从内存中的书签列表删除
        if var existingBookmarks = bookmarks[bookId] {
            existingBookmarks.removeAll { $0.id == bookmarkToDelete.id }
            bookmarks[bookId] = existingBookmarks
        }
        print("ReaderCoordinator: 已删除书签。")
    }
    
    /// 跳转到书签
    public func jumpToBookmark(_ bookmark: Bookmark) {
        guard let book = currentBook, book.id == bookmark.bookId else {
            print("ReaderCoordinator: 无法跳转到书签，书籍不匹配或未打开。")
            return
        }
        // 更新当前章节和页码
        self.currentChapterIndex = bookmark.chapterIndex ?? 0 // 如果没有章节信息，默认为0
        self.currentPage = bookmark.pageIndex // 使用 pageIndex 替代 pageNumber，与 Bookmark 模型保持一致
        // TODO: 可能需要重新加载章节内容或页面内容
        // 例如，如果章节内容是按需加载的，这里需要触发加载
        // Task {
        //     await readerViewModel.loadChapterContent(chapters[currentChapterIndex])
        //     readerViewModel.currentPage = bookmark.pageNumber
        // }
        print("ReaderCoordinator: 已跳转到书签位置：章节 \(currentChapterIndex + 1)，页码 \(currentPage + 1)。")
    }
    
    /// 显示用于输入书源URL的弹窗
    /// 这个方法由 ImportSourceSheet 调用，通过将 showingURLInputSheet 设置为 true，
    /// 间接触发 SourceManagerView 中绑定的 alert 显示。
    public func showURLInput() {
        // 确保在主线程上更新 @Published 属性
        DispatchQueue.main.async {
            self.showingURLInputSheet = true
        }
    }
    
    /// 显示书源管理界面
    public func presentSourceManager() {
        // 确保在主线程上更新 @Published 属性
        DispatchQueue.main.async {
            self.showSourceManager = true
        }
    }
    
    // MARK: - Toolbar Actions
    
    /// Toggles the listening state for text-to-speech.
    func toggleListening() {
        isListening.toggle()
        // TODO: Add actual text-to-speech logic here
        print("Listening state toggled: \(isListening)")
    }
    
    // MARK: - 初始化器
    public init(modelContainer: ModelContainer) { // 修改为 public
        self.modelContainer = modelContainer
        self.readerCore = ReaderCore()
        self.contentFetcher = ContentFetcher() // ContentFetcher 初始化不需要参数
        self.backupManager = BackupManager()
        self.sourceManager = BookSourceManager(modelContainer: modelContainer)
        // 初始化 readerViewModel 和 bookShelfViewModel，确保它们在 coordinator 初始化时被创建
        self.readerViewModel = ReaderViewModel(modelContainer: modelContainer)
        // Initialization of bookShelfViewModel moved to lazy var declaration
        
        // 设置selectedBook的订阅，当收到选中的书籍时设置为currentBook
        selectedBook
            .receive(on: DispatchQueue.main)
            .sink { [weak self] book in
                self?.currentBook = book
            }
            .store(in: &cancellables)
        
        // 加载初始数据
        Task {
            loadInitialData()
        }
    }
    
    // 添加 addBook 方法的实现
    @MainActor
    func addBook(url: URL, title: String) async throws {
        // 实现添加本地书籍的逻辑
        // 这里可以调用 bookShelfViewModel 或直接操作 modelContainer
        // 确保 Book 初始化时 url 参数传递的是 URL 类型, coverUrl 也应该是 URL 类型
        let newBook = Book(title: title, author: "未知作者", coverUrl: url, introduction: nil, latestChapter: nil, url: url, sourceId: UUID(), bookType: url.pathExtension.lowercased(), filePath: url.path)
        modelContainer.mainContext.insert(newBook)
        try modelContainer.mainContext.save()
        self.loadInitialData() // Refresh the coordinator's book list
    }
    
    // 添加 addNetworkBook 方法的实现
    @MainActor
    func addNetworkBook(url: URL, title: String, author: String) async {
        // 实现添加网络书籍的逻辑
        // 这里可以调用 bookShelfViewModel 或直接操作 modelContainer
        // 确保 Book 初始化时 url 参数传递的是 URL 类型，coverUrl 也应该是 URL 类型
        let newBook = Book(title: title, author: author, coverUrl: url, introduction: nil, latestChapter: nil, url: url, sourceId: UUID(), bookType: "network") // 假设网络书籍类型为 network
        modelContainer.mainContext.insert(newBook)
        try? modelContainer.mainContext.save()
        self.loadInitialData() // Refresh the coordinator's book list
    }
    
    // 添加 isBookDuplicate 方法的实现
    @MainActor
    func isBookDuplicate(url: URL?, title: String?, bookType: String?) -> Bool {
        let context = modelContainer.mainContext

        // 优先基于 URL (文件路径或网络链接) 进行检查
        if let bookUrl = url {
            var predicateBasedOnUrl: Predicate<Book>?
            if bookUrl.isFileURL {
                let path = bookUrl.path // path 是 Sendable 的 String
                predicateBasedOnUrl = #Predicate<Book> { book in
                    book.filePath == path
                }
            } else {
                let absoluteUrlString = bookUrl.absoluteString // absoluteUrlString 是 Sendable 的 String
                predicateBasedOnUrl = #Predicate<Book> { book in
                    book.url.absoluteString == absoluteUrlString
                }
            }

            if let predicate = predicateBasedOnUrl {
                let descriptor = FetchDescriptor(predicate: predicate)
                do {
                    if try context.fetchCount(descriptor) > 0 {
                        return true // 如果URL匹配，则认为是重复
                    }
                } catch {
                    print("Error fetching book count for URL duplicate check: \(error)")
                    // 发生错误时，继续后续检查
                }
            }
        }

        // 如果URL不匹配或未提供，则基于书名和书籍类型进行检查
        if let bookTitle = title, !bookTitle.isEmpty, let type = bookType, !type.isEmpty {
            let normalizedTitle = bookTitle.trimmingCharacters(in: .whitespacesAndNewlines)
            let normalizedBookType = type.lowercased()
            
            let trimmedNormalizedTitle = normalizedTitle.trimmingCharacters(in: .whitespacesAndNewlines) // Sendable String
            let finalNormalizedBookType = normalizedBookType // Sendable String

            let titleAndTypePredicate = #Predicate<Book> { book in
                book.title == trimmedNormalizedTitle &&
                book.bookType == finalNormalizedBookType
            }
            
            let descriptor = FetchDescriptor(predicate: titleAndTypePredicate)
            do {
                let count = try context.fetchCount(descriptor)
                return count > 0
            } catch {
                print("Error fetching book count for title/type duplicate check: \(error)")
                return false
            }
        }
        
        // 如果以上条件都不满足，则不认为是重复
        return false
    }
    
    
    // MARK: - 初始化辅助方法
    
    /// 加载初始数据
    func loadInitialData() { // 改为 internal (默认访问级别)
        // 从数据库加载书源
        Task {
            // 使用 sourceManager 加载书源
            await sourceManager.fetchBookSources()
            self.bookSources = sourceManager.bookSources // 直接访问属性
        }
        
        // 从数据库加载书籍列表
        Task {
            await MainActor.run {
                let fetchDescriptor = FetchDescriptor<Book>(sortBy: [SortDescriptor(\Book.title, order: .reverse)])
                do {
                    self.books = try modelContainer.mainContext.fetch(fetchDescriptor)
                    print("成功从 SwiftData 加载了 \(self.books.count) 本书籍到 Coordinator")
                } catch {
                    print("从 SwiftData 加载书籍列表失败: \(error.localizedDescription)")
                    // 可以在这里向用户显示错误，或者记录日志
                }
            }
        }
    }
    
    /// 设置订阅
    private func setupSubscriptions() {
        // 监听 readerViewModel 的状态变化
        readerViewModel.$isLoading
            .sink { [weak self] isLoading in
                self?.isLoading = isLoading
            }
            .store(in: &cancellables)
        
        readerViewModel.$errorMessage
            .compactMap { $0 }
            .sink { [weak self] message in
                self?.errorMessage = message
            }
            .store(in: &cancellables)
        
        // 监听字体大小变化
        $fontSize
            .debounce(for: .milliseconds(300), scheduler: RunLoop.main)
            .sink { [weak self] newSize in // 使用 newSize 来避免 self 的警告
                // Trigger re-pagination or view update when font size changes
                // self?.readerViewModel.updateSettings()
                print("Font size changed to \(newSize), potentially needs re-pagination.")
            }
            .store(in: &cancellables)
        
        // 监听选中书籍变化
        selectedBook
            .receive(on: RunLoop.main)
            .sink { [weak self] bookToOpen in
                guard let self = self, let book = bookToOpen else { return }
                print("Received request to open book: \(book.title)")
                self.openBook(book)
            }
            .store(in: &cancellables)
    }
    
    /// 保存阅读进度
    /// - Parameters:
    ///   - bookID: 书籍ID
    ///   - chapterIndex: 章节索引
    ///   - pageNumber: 页码
    func saveReadingProgress(bookID: String, chapterIndex: Int, pageNumber: Int) {
        readingProgress[bookID] = (chapterIndex: chapterIndex, pageNumber: pageNumber)
        print("保存阅读进度：书籍 \(bookID)，章节 \(chapterIndex)，页码 \(pageNumber)")
        // 将进度保存到持久化存储，可使用SwiftData
        Task {
            await MainActor.run {
                let context = modelContainer.mainContext
                // 尝试查找现有的阅读进度记录
                let fetchDescriptor = FetchDescriptor<ReadingProgress>(predicate: #Predicate { $0.bookID == bookID })
                do {
                    if let existingProgress = try context.fetch(fetchDescriptor).first {
                        // 更新现有记录
                        existingProgress.chapterIndex = chapterIndex
                        existingProgress.pageNumber = pageNumber
                        existingProgress.lastReadDate = Date()
                    } else {
                        // 创建新记录
                        let newProgress = ReadingProgress(bookID: bookID, chapterIndex: chapterIndex, pageNumber: pageNumber)
                        context.insert(newProgress)
                    }
                    try context.save()
                    print("阅读进度已保存到 SwiftData")
                } catch {
                    print("保存阅读进度到 SwiftData 失败: \(error.localizedDescription)")
                }
            }
        }
    }
    
    /// 加载指定书籍的阅读进度
    /// - Parameter bookID: 书籍ID
    func loadReadingProgress(bookID: String) {
        Task {
            await MainActor.run {
                let context = modelContainer.mainContext
                let fetchDescriptor = FetchDescriptor<ReadingProgress>(predicate: #Predicate { $0.bookID == bookID })
                do {
                    if let savedProgress = try context.fetch(fetchDescriptor).first {
                        self.readingProgress[bookID] = (chapterIndex: savedProgress.chapterIndex, pageNumber: savedProgress.pageNumber)
                        // 更新当前阅读位置，如果这本书是当前打开的书
                        // 确保 currentBook 的 ID 类型与 bookID 一致，通常是 String
                        if self.currentBook?.id.uuidString == bookID {
                            self.currentChapterIndex = savedProgress.chapterIndex
                            self.currentPage = savedProgress.pageNumber
                            print("已加载阅读进度：书籍 \(bookID)，章节 \(savedProgress.chapterIndex)，页码 \(savedProgress.pageNumber)")
                        }
                    } else {
                        print("未找到书籍 \(bookID) 的阅读进度，将使用默认值 (章节0, 页码0)")
                        // 如果没有找到进度，可以设置默认值或不操作
                        self.readingProgress[bookID] = (chapterIndex: 0, pageNumber: 0) // 重置或设置默认
                        if self.currentBook?.id.uuidString == bookID {
                            self.currentChapterIndex = 0
                            self.currentPage = 0
                        }
                    }
                } catch {
                    print("加载阅读进度失败: \(error.localizedDescription)")
                }
            }
        }
    }
    
    // MARK: - 书签管理（旧版或重复，待清理）
    // 这些方法可能与上面新添加的 public 方法重复，需要检查并清理
    // 确保只保留一套功能正确且访问权限合适的书签管理方法
    
    // /// 添加书签
    // /// - Parameters:
    // ///   - bookID: 书籍的唯一标识符
    // ///   - pageNumber: 书签所在的页码 (0-indexed)
    // ///   - textSnippet: 书签相关的文本片段或描述
    // ///   - chapterIndex: (可选) 书签所在的章节索引 (0-indexed)
    // @MainActor
    // func addBookmark(bookID: String, pageNumber: Int, textSnippet: String, chapterIndex: Int? = nil) {
    //     guard let bookUUID = UUID(uuidString: bookID) else {
    //         print("Error: Invalid bookID string for bookmark: \(bookID)")
    //         return
    //     }
    //
    //     let newBookmark = Bookmark(
    //         bookId: bookUUID, // 提供 bookId
    //         pageIndex: pageNumber,
    //         chapterIndex: chapterIndex ?? self.currentChapterIndex,
    //         content: textSnippet,
    //         userDescription: nil, // 明确为可选参数提供 nil
    //         createdAt: Date()
    //     )
    //
    //     let context = modelContainer.mainContext
    //     context.insert(newBookmark)
    //
    //     do {
    //         try context.save()
    //         // 更新内存中的书签列表
    //         if self.bookmarks[bookID] != nil {
    //             self.bookmarks[bookID]?.append(newBookmark)
    //         } else {
    //             self.bookmarks[bookID] = [newBookmark]
    //         }
    //         print("添加书签并已保存到 SwiftData: '\(textSnippet)' for book \(bookID) at page \(pageNumber)")
    //     } catch {
    //         print("保存新书签到 SwiftData 失败: \(error.localizedDescription)")
    //     }
    // }
    
    // /// 删除书签
    // /// - Parameter bookmark: 要删除的书签对象
    // @MainActor
    // func deleteBookmark(_ bookmark: Bookmark) {
    //     // bookmark.bookId 是 UUID 类型，我们用它的字符串表示作为字典的键
    //     let bookIdString = bookmark.bookId.uuidString
    //
    //     let context = modelContainer.mainContext
    //     // bookmark.id 已经是 UUID 类型，可以直接用于查询
    //     let fetchDescriptor = FetchDescriptor<Bookmark>(predicate: #Predicate { $0.id == bookmark.id })
    //
    //     do {
    //         if let bookmarkToDelete = try context.fetch(fetchDescriptor).first {
    //             context.delete(bookmarkToDelete)
    //             try context.save()
    //             // 更新内存中的书签列表，使用 bookIdString 作为键
    //             if self.bookmarks[bookIdString]?.removeAll(where: { $0.id == bookmark.id }) != nil {
    //                 print("删除书签并已从 SwiftData 删除: '\(bookmark.content)' from book \(bookIdString)")
    //             } else {
    //                 // 这可能意味着 bookIdString 在 bookmarks 字典中没有对应的条目，或者条目是空数组
    //                 print("书签已从 SwiftData 删除，但在内存字典中未找到键 '\(bookIdString)' 或其书签列表为空。书签内容: '\(bookmark.content)'")
    //             }
    //         } else {
    //             print("无法从 SwiftData 删除书签：未找到书签。可能已被删除或ID不匹配。书签内容: '\(bookmark.content)'")
    //             // 即使在数据库中未找到，也尝试从内存中删除，以保持一致性
    //             if self.bookmarks[bookIdString]?.removeAll(where: { $0.id == bookmark.id }) != nil {
    //                 print("从内存中删除了书签（因为它不在 SwiftData 中）。书签内容: '\(bookmark.content)'")
    //             }
    //         }
    //     } catch {
    //         print("从 SwiftData 删除书签失败: \(error.localizedDescription)")
    //     }
    // }
    
    /// 加载指定书籍的书签 (保留此方法)
    /// - Parameter bookID: 书籍ID
    @MainActor
    func loadBookmarks(forBookID bookID: String) {
        guard let bookUUID = UUID(uuidString: bookID) else {
            print("错误：加载书签时 bookID 字符串无效，无法转换为 UUID: \(bookID)")
            self.bookmarks[bookID] = [] // 清空对应 bookID 的书签列表或标记错误
            return
        }
        let context = modelContainer.mainContext
        // 使用转换后的 UUID 进行查询
        let fetchDescriptor = FetchDescriptor<Bookmark>(predicate: #Predicate { $0.bookId == bookUUID })
        
        do {
            let fetchedBookmarks = try context.fetch(fetchDescriptor)
            self.bookmarks[bookID] = fetchedBookmarks
            print("为书籍 \(bookID) 加载了 \(fetchedBookmarks.count) 个书签")
        } catch {
            print("为书籍 \(bookID) 加载书签失败: \(error.localizedDescription)")
            self.bookmarks[bookID] = [] // 加载失败则清空
        }
    }
    
    // /// 跳转到书签指定的阅读位置
    // /// - Parameter bookmark: 要跳转到的书签对象
    // @MainActor
    // func jumpToBookmark(_ bookmark: Bookmark) {
    //     // bookmark.bookId 是 UUID 类型
    //     // self.currentBook?.id 也是 UUID 类型
    //     // 直接比较 UUIDs
    //     guard let currentBookID = self.currentBook?.id, currentBookID == bookmark.bookId else {
    //         let bookIdString = bookmark.bookId.uuidString // 用于打印
    //         print("无法跳转到书签：当前书籍 (\(self.currentBook?.title ?? "未知")) 与书签的书籍ID (\(bookIdString)) 不匹配。")
    //         return
    //     }
    //
    //     print("跳转到书签: '\(bookmark.content)' at page \(bookmark.pageIndex)")
    //
    //     self.currentPage = bookmark.pageIndex
    //     if let chapterIdx = bookmark.chapterIndex {
    //         self.currentChapterIndex = chapterIdx
    //     }
    //
    //     // 通知 ReaderViewModel 更新视图。这可以通过多种方式实现：
    //     // 1. ReaderViewModel 观察 Coordinator 的 currentPage 和 currentChapterIndex。
    //     // 2. Coordinator 直接调用 ReaderViewModel 的方法。
    //     // 例如: await self.readerViewModel.loadBookContentForPage(pageIndex: bookmark.pageIndex, chapterIndex: bookmark.chapterIndex)
    //     // 当前实现依赖于视图/ViewModel响应Coordinator的状态变化。
    // }
    
    // MARK: - 页面导航与进度
    
    /// 进度描述字符串，例如 "10/100" 或 "章节 1/10"
    var progressDescription: String {
        if totalPages > 0 {
            // 确保 currentPage 在有效范围内，用户看到的页码通常从1开始
            let validCurrentPageForDisplay = max(1, min(currentPage + 1, totalPages))
            return "\(validCurrentPageForDisplay)/\(totalPages)"
        } else if !chapters.isEmpty { // 使用 self.chapters (目录)
            let validCurrentChapterForDisplay = max(1, min(currentChapterIndex + 1, chapters.count))
            return "章节 \(validCurrentChapterForDisplay)/\(chapters.count)"
        }
        return "N/A"
    }
    
    /// 当前阅读进度，范围 0.0 到 1.0，用于 Slider
    var currentProgress: Double {
        get {
            guard totalPages > 1 else { return 0.0 } // 如果只有一页或没有页，进度为0
            // currentPage 是0索引的, totalPages - 1 是最后一页的索引
            return Double(currentPage) / Double(totalPages - 1)
        }
        set {
            guard totalPages > 1 else { return }
            let newPageProvisional = Int(newValue * Double(totalPages - 1))
            // 更新 currentPage 以便 Slider 实时反馈，实际内容加载由 seekToProgress 处理
            self.currentPage = min(max(0, newPageProvisional), totalPages - 1)
            // print("Slider drag: currentProgress set, currentPage (0-indexed) now \(self.currentPage)")
        }
    }
    
    /// 跳转到指定进度
    /// - Parameter progress: 目标进度，范围 0.0 到 1.0
    @MainActor
    func seekToProgress(_ progress: Double) {
        guard totalPages > 1 else { return }
        let targetPage = Int(progress * Double(totalPages - 1))
        let newPage = min(max(0, targetPage), totalPages - 1)
        
        if currentPage != newPage {
            currentPage = newPage
            // TODO: 实际加载新页面的内容到 readerViewModel
            // self.readerViewModel.loadContentForPage(newPage)
            // self.objectWillChange.send() // 如果 readerViewModel 更新不自动触发 coordinator 更新
            print("ReaderCoordinator: seekToProgress to page \(currentPage) (0-indexed)")
        }
    }
    
    /// 跳转到下一页
    @MainActor
    func nextPage() {
        guard totalPages > 0 else {
            print("ReaderCoordinator: No pages to navigate.")
            return
        }
        if currentPage < totalPages - 1 {
            currentPage += 1
            // TODO: 实际加载新页面的内容到 readerViewModel
            // self.readerViewModel.loadContentForPage(currentPage)
            print("ReaderCoordinator: nextPage to page \(currentPage) (0-indexed)")
        } else {
            print("ReaderCoordinator: Already on the last page.")
            // TODO: 考虑是否加载下一章节 (如果适用)
        }
    }
    
    /// 跳转到上一页
    @MainActor
    func previousPage() {
        guard totalPages > 0 else {
            print("ReaderCoordinator: No pages to navigate.")
            return
        }
        if currentPage > 0 {
            currentPage -= 1
            // TODO: 实际加载新页面的内容到 readerViewModel
            // self.readerViewModel.loadContentForPage(currentPage)
            print("ReaderCoordinator: previousPage to page \(currentPage) (0-indexed)")
        } else {
            print("ReaderCoordinator: Already on the first page.")
            // TODO: 考虑是否加载上一章节 (如果适用)
        }
    }
    
    
    
    // MARK: - 文件处理方法
    
    @MainActor
    func openBook(_ book: Book) {
        currentBook = book
        bookTitle = book.title
        document = nil // 清除旧的PDF文档
        pages = [] // 清除旧的图片页面
        chapters = [] // 清除旧的章节列表
        //currentPage = 0 // 重置当前页码
        //totalPages = 0 // 重置总页数
        //currentChapterIndex = 0 // 重置当前章节索引
        
        // 加载阅读进度
        // 确保 book.id 是 UUID 类型，然后转换为 String
        loadReadingProgress(bookID: book.id.uuidString)
        // 加载书签
        loadBookmarks(forBookID: book.id.uuidString)
        
        // 从Book的filePath创建URL
        let fileURL = book.filePath.map { URL(fileURLWithPath: $0) } ?? book.url
        if fileURL == nil {
            // 使用 self.showError 来确保调用的是实例方法
            self.showError(title: "错误", message: "书籍文件路径无效。无法从 Book 对象获取 URL。")
            return
        }
        
        // 在后台任务中调用异步的 openBook(path:)
        Task {
            do {
                try await self.openBook(fileURL.path)
            } catch {
                // 确保在主线程上更新UI相关的错误状态
                await MainActor.run {
                    self.showError(title: "打开书籍失败", message: "处理书籍文件时发生错误: \(error.localizedDescription)")
                }
            }
        }
    }
    
    /// 读取文件内容
    /// - Parameter path: 文件路径
    /// - Returns: 文件内容字符串，如果读取失败则返回nil
    func readFileContent(at path: String) -> String? {
        do {
            let fileURL = URL(fileURLWithPath: path)
            // 尝试使用UTF-8编码读取，如果失败，尝试GBK等其他常见中文编码
            if let content = try? String(contentsOf: fileURL, encoding: .utf8) {
                return content
            } else if let content = try? String(contentsOf: fileURL, encoding: .gb_18030_2000) {
                // GB18030 是一个常用的中文编码标准，兼容GBK
                return content
            } else {
                // 如果以上编码都失败，可以尝试更多编码或返回nil
                print("无法使用UTF-8或GB18030编码读取文件内容: \(fileURL.lastPathComponent)")
                return nil
            }
        } catch {
            print("读取文件内容时发生错误: \(error.localizedDescription)")
            return nil
        }
    }
    
    /// 打开并加载书籍
    /// - Parameter path: 书籍文件路径
    /// - Throws: ReaderError
    func openBook(_ path: String) async throws {
        print("接收到打开书籍请求: \(URL(fileURLWithPath: path).lastPathComponent)")
        
        // 检查文件是否存在
        guard FileManager.default.fileExists(atPath: path) else {
            throw ReaderError.fileNotFound
        }
        
        // 检测文件类型
        let fileExtension = (path as NSString).pathExtension.lowercased()
        let fileType = FileType.detectType(from: path)
        print("打开书籍类型: \(fileType) 扩展名: \(fileExtension)")
        
        // 根据文件类型处理
        do {
            switch fileType {
            case .txt:
                try await loadTxtBook(path)
            case .pdf:
                try await loadPdfBook(path)
            case .epub:
                try await loadEpubBook(path)
            case .html:
                try await loadHtmlBook(path)
            case .unknown:
                // 对于未知类型，尝试根据文件扩展名再次判断
                if fileExtension == "txt" {
                    try await loadTxtBook(path)
                } else {
                    throw ReaderError.unsupportedFileType
                }
            }
            
            // 加载成功后，更新当前书籍信息
            await updateBookInfoFromViewModel()
            
        } catch {
            print("打开书籍失败 '\(URL(fileURLWithPath: path).lastPathComponent)': \(error)")
            throw error
        }
    }
    
    // MARK: - 文件类型检测
    
    /// 文件类型枚举
    enum FileType: String {
        case txt = "txt"
        case pdf = "pdf"
        case epub = "epub"
        case html = "html"
        case unknown = "unknown"
        
        /// 根据文件路径检测文件类型
        /// - Parameter path: 文件路径
        /// - Returns: 文件类型
        static func detectType(from path: String) -> FileType {
            let fileExtension = (path as NSString).pathExtension.lowercased()
            
            switch fileExtension {
            case "txt":
                return .txt
            case "pdf":
                return .pdf
            case "epub":
                return .epub
            case "html", "htm":
                return .html
            default:
                return .unknown
            }
        }
    }
    
    /// 加载TXT格式书籍
    /// - Parameter path: 文件路径
    private func loadTxtBook(_ path: String) async throws {
        print("Initializing reader for type: txt with path: \(path)")
        // 从路径创建 Book 对象
        let url = URL(fileURLWithPath: path)
        let title = url.deletingPathExtension().lastPathComponent
        
        // 尝试使用多种编码读取文件内容
        let contentString: String
        do {
            if let utf8Content = try? String(contentsOf: url, encoding: .utf8) {
                contentString = utf8Content
            } else if let gbkContent = try? String(contentsOf: url, encoding: .gb_18030_2000) { // GB18030 兼容 GBK
                contentString = gbkContent
                print("成功使用 GB18030 编码读取TXT文件: \(url.lastPathComponent)")
            } else if let gb2312Content = try? String(contentsOf: url, encoding: .gb_18030_2000) { // 使用 .gb_18030_2000 替代 .gb_2312_80，它兼容GB2312
                contentString = gb2312Content
                print("成功使用 GB18030 (兼容GB2312) 编码读取TXT文件: \(url.lastPathComponent)")
            } else {
                // 如果所有尝试的编码都失败，则抛出错误
                throw ReaderError.loadingFailed("无法使用任何已知编码读取TXT文件内容。文件可能已损坏或编码不受支持。")
            }
        } catch let error as NSError where error.domain == NSCocoaErrorDomain && error.code == NSFileReadUnknownStringEncodingError {
            // 特定于编码问题的错误处理
            throw ReaderError.loadingFailed("无法确定TXT文件的正确编码: \(error.localizedDescription)")
        } catch {
            // 其他读取错误
            throw ReaderError.loadingFailed("读取TXT文件内容时发生错误: \(error.localizedDescription)")
        }
        
        // 创建一个新的 Book 实例
        // 根据 Book.swift 中的 init 方法调整参数
        let newBook = Book(
            title: title,
            author: "未知作者",
            coverUrl: nil,
            introduction: nil,
            latestChapter: nil,
            url: url, // 使用文件自身的URL作为书籍URL
            sourceId: UUID(), // 为本地文件创建一个新的UUID作为sourceId，或根据逻辑调整
            bookType: FileType.txt.rawValue, // 使用 FileType 的 rawValue
            filePath: path, // 存储文件路径
            lastReadTimestamp: nil,
            coverImageData: nil
        )
        
        // 使用 ReaderViewModel 加载 Book 对象
        // 传递读取到的 contentString 给 ReaderViewModel，如果它需要的话
        // 假设 loadBookContent 内部会处理从 Book 对象获取内容或路径，或者直接使用传递的内容
        // TODO: 确认 readerViewModel.loadBookContent 是否需要 contentString，如果需要，则传递
        // 暂时假设 readerViewModel.loadBookContent(book: newBook, content: contentString) 这样的接口
        // 如果 ReaderViewModel 内部自己处理文件读取，则不需要传递 contentString
        await readerViewModel.loadBookContent(book: newBook) // 保持原有调用，如果ViewModel内部处理文件读取
        // 如果ViewModel需要预读的内容，则应修改ViewModel接口并传递 contentString
        // 例如: await readerViewModel.loadBookContent(book: newBook, content: contentString)
    }
    
    /// 加载PDF格式书籍
    /// - Parameter path: 文件路径
    private func loadPdfBook(_ path: String) async throws {
        guard let pdfDoc = PDFDocument(url: URL(fileURLWithPath: path)) else {
            throw ReaderError.loadingFailed("无法加载PDF文件")
        }
        self.document = pdfDoc
        self.totalPages = pdfDoc.pageCount
    }
    
    /// 加载EPUB格式书籍
    /// - Parameter path: 文件路径
    private func loadEpubBook(_ path: String) async throws {
        print("Initializing reader for type: epub with path: \(path)")
        let fileURL = URL(fileURLWithPath: path)
        var bookTitle = fileURL.deletingPathExtension().lastPathComponent
        
        let fileManager = FileManager.default
        let temporaryDirectoryURL = fileManager.temporaryDirectory.appendingPathComponent(UUID().uuidString)
        let epubParser = EPUBParser()
        
        do {
            try fileManager.createDirectory(at: temporaryDirectoryURL, withIntermediateDirectories: true, attributes: nil)
            try fileManager.unzipItem(at: fileURL, to: temporaryDirectoryURL)
            print("EPUB 文件已成功解压到: \(temporaryDirectoryURL.path)")
            
            let containerXMLURL = temporaryDirectoryURL.appendingPathComponent("META-INF/container.xml")
            guard fileManager.fileExists(atPath: containerXMLURL.path) else {
                throw EPUBParseError.containerXMLNotFound
            }
            
            let opfRelativePath = try epubParser.getOPFPath(from: containerXMLURL)
            let opfFileURL = temporaryDirectoryURL.appendingPathComponent(opfRelativePath)
            let opfFileBaseURL = opfFileURL.deletingLastPathComponent() // OPF文件所在的目录，用于解析相对路径
            
            guard fileManager.fileExists(atPath: opfFileURL.path) else {
                throw EPUBParseError.opfFileNotFound(opfFileURL.path)
            }
            
            let (metadata, manifest, spine) = try epubParser.parseOPF(at: opfFileURL, opfFileBaseURL: opfFileBaseURL)
            
            // 更新书籍标题（如果从元数据中获取到）
            if let titleFromMetadata = metadata.title, !titleFromMetadata.isEmpty {
                bookTitle = titleFromMetadata
            }
            
            // TODO: 从 manifest 中找到封面图片并加载数据
            var coverImageData: Data? = nil
            if let coverId = metadata.coverImageId, let coverManifestItem = manifest[coverId] {
                let coverImageURL = opfFileBaseURL.appendingPathComponent(coverManifestItem.href)
                if fileManager.fileExists(atPath: coverImageURL.path) {
                    coverImageData = try? Data(contentsOf: coverImageURL)
                }
            }
            
            let newBook = Book(
                title: bookTitle,
                author: metadata.creator ?? "未知作者",
                coverUrl: nil, // 可以考虑存储封面图片的相对路径或文件名
                introduction: nil, // TODO: 从元数据中获取更详细的描述
                latestChapter: nil, // EPUB通常没有单一的“最新章节”概念，除非从阅读进度来
                url: fileURL,
                sourceId: UUID(), // 本地文件可以有固定或生成的sourceId
                bookType: FileType.epub.rawValue,
                filePath: path,
                lastReadTimestamp: nil as Double?,
                coverImageData: coverImageData
            )
            
            // TODO: 将解析到的章节和内容传递给 ReaderViewModel
            // 示例：构建章节列表 (这里只是一个非常简化的示例，实际需要从spine和manifest构建)
            var parsedChapters: [(title: String, contentPath: String)] = []
            for itemRef in spine {
                if let manifestItem = manifest[itemRef.idref], manifestItem.mediaType == "application/xhtml+xml" {
                    // 尝试从 manifestItem.href 或 manifestItem.id 获取章节标题 (可能需要进一步解析HTML的<title>)
                    let chapterTitle = manifestItem.id // 简化处理，用id作为标题
                    let chapterContentPath = opfFileBaseURL.appendingPathComponent(manifestItem.href).path
                    parsedChapters.append((title: chapterTitle, contentPath: chapterContentPath))
                }
            }
            
            // 更新 chapters 属性供UI显示
            self.chapters = parsedChapters.enumerated().map { (index, chapter) in (title: chapter.title, index: index) }
            self.bookTitle = newBook.title
            self.currentBook = newBook // 设置当前书籍
            
            // TODO: 实现 ReaderViewModel 对 EPUB 内容的加载逻辑
            // await readerViewModel.loadEpubBookContent(book: newBook, chapters: parsedChapters, temporaryDirectoryURL: temporaryDirectoryURL)
            print("EPUB 书籍 '\(newBook.title)' 元数据已解析。章节数: \(parsedChapters.count)。内容加载待进一步实现。")
            
            // 暂时不抛出错误，允许基本信息加载
            // throw ReaderError.loadingFailed("EPUB 内容加载功能正在开发中。")
            
        } catch let error as EPUBParseError {
            try? fileManager.removeItem(at: temporaryDirectoryURL)
            print("EPUB解析失败: \(error.localizedDescription)")
            throw ReaderError.loadingFailed("EPUB文件解析失败: \(error.localizedDescription)")
        } catch {
            try? fileManager.removeItem(at: temporaryDirectoryURL)
            print("EPUB加载时发生未知错误: \(error.localizedDescription)")
            throw ReaderError.loadingFailed("EPUB文件处理时发生未知错误: \(error.localizedDescription)")
        }
        // 成功解析元数据后，临时目录的清理可以推迟到书籍关闭或应用退出时，以便访问内容文件
        // 或者在 ReaderViewModel 加载完所有内容后清理
        // For now, we are not cleaning it immediately to allow potential content access later.
        // Consider adding a mechanism to clean up temporary EPUB directories when they are no longer needed.
    }
    
    /// 加载HTML格式书籍
    /// - Parameter path: 文件路径
    private func loadHtmlBook(_ path: String) async throws {
        // HTML加载逻辑待实现
        throw ReaderError.loadingFailed("HTML格式支持正在开发中")
    }
    
    /// 设置错误处理
    private func setupErrorHandling() {
        // 监听错误状态变化
        $errorMessage
            .compactMap { $0 }
            .sink { [weak self] message in
                self?.alertTitle = "错误"
                self?.alertMessage = message
                self?.showAlert = true
            }
            .store(in: &cancellables)
    }
    
    // 便利初始化器，用于预览和测试
    convenience init(modelContainer: ModelContainer, bookSources: [BookSource]) {
        self.init(modelContainer: modelContainer)
        self.bookSources = bookSources
        print("Initialized ReaderCoordinator with bookSources (count: \(bookSources.count))")
    }
    
    // MARK: - 页面导航和进度更新方法 (恢复)
    
    // MARK: - 预览实例
    // static var preview: ReaderCoordinator { // 重复声明，将被删除
    //     // 创建一个用于预览的 Coordinator 实例
    //     do {
    //         // 创建内存中的模型容器
    //         let config = ModelConfiguration(isStoredInMemoryOnly: true)
    //         let container = try ModelContainer(for: Book.self, BookSource.self, configurations: config)
    //
    //         // 创建预览用的协调器
    //         let coordinator = ReaderCoordinator(modelContainer: container)
    //
    //         // 设置一些预览数据
    //         coordinator.bookTitle = "示例书籍"
    //         coordinator.totalPages = 100
    //         coordinator.currentPage = 10
    //         coordinator.updateProgress()
    //
    //         return coordinator
    //     } catch {
    //         fatalError("Failed to create ModelContainer for preview: \(error)")
    //     }
    // }
    
    
    
    /// 更新阅读进度百分比
    func updateProgress() {
        if totalPages > 0 {
            // 确保当前页在有效范围内 [0, totalPages - 1]
            let validCurrentPage = max(0, min(currentPage, totalPages - 1))
            // 基于有效的0索引页码计算进度
            // 避免在totalPages为1时除以零
            currentProgress = totalPages > 1 ? Double(validCurrentPage) / Double(totalPages - 1) : (validCurrentPage == 0 ? 0.0 : 1.0)
            
            // 如果有当前书籍，保存阅读进度
            if let bookId = currentBook?.id.uuidString {
                saveReadingProgress(bookID: bookId, chapterIndex: currentChapterIndex, pageNumber: currentPage)
            }
        } else {
            currentProgress = 0.0
        }
        
        print("阅读进度更新: \(currentProgress * 100)%")
    }
    
    
    /// 跳转到下一页 (内部版本)
    func nextPageInternal() {
        guard totalPages > 0 else { return }
        let nextPage = currentPage + 1
        if nextPage < totalPages {
            currentPage = nextPage
            updateProgress() // 更新进度会自动保存阅读进度
        }
    }
    
    
    /// 根据进度百分比跳转页面 (内部版本)
    func seekToProgressInternal(_ progress: Double) {
        guard totalPages > 0 else { return }
        let clampedProgress = max(0.0, min(progress, 1.0))
        let targetPage = Int(clampedProgress * Double(totalPages > 0 ? totalPages - 1 : 0))
        currentPage = max(0, min(targetPage, totalPages - 1))
        updateProgress() // 更新进度会自动保存阅读进度
    }
    
    
    // MARK: - 阅读相关方法
    
    /// 开始阅读指定路径的书籍
    /// - Parameter path: 书籍文件路径
    /// - Returns: 是否成功开始阅读
    func startReading(from path: String) async throws -> Bool {
        isLoading = true
        defer { isLoading = false }
        
        do {
            // 使用 openBook 方法处理不同类型的书籍
            try await openBook(path)
            return true
        } catch {
            errorMessage = error.localizedDescription
            throw error
        }
    }
    
    /// 使用 ReaderViewModel 加载书籍
    /// - Parameter path: 书籍文件路径
    /// - Throws: 加载错误
    func loadBook(from path: String) async throws {
        // 设置加载状态
        isLoading = true
        defer { isLoading = false }
        
        do {
            // Coordinator的openBook方法会处理文件加载和ViewModel的更新
            try await openBook(path) // 调用本类的 openBook 方法处理路径
            
            // 更新当前书籍信息 (openBook会间接调用updateBookInfoFromViewModel)
            // await updateBookInfoFromViewModel() // 这行可以移除，因为openBook会处理
            
            // 通知UI更新
            selectedBook.send(currentBook)
        } catch {
            errorMessage = "加载书籍失败: \(error.localizedDescription)"
            throw error
        }
    }
    
    /// 从ViewModel更新书籍信息到Coordinator
    private func updateBookInfoFromViewModel() async {
        // 获取当前章节信息
        self.chapters = readerViewModel.chapters.enumerated().map { (index, chapter) in (title: chapter.title, index: index) } // 处理可选标题, 确保元组标签与属性定义一致
        
        // 更新页面信息
        self.totalPages = readerViewModel.totalPages
        self.currentPage = 0
        
        // 更新书籍标题
        if let title = readerViewModel.currentBook?.title { // 直接使用 currentBook.title
            self.bookTitle = title
        }
        
        // 更新当前书籍引用
        if let book = readerViewModel.currentBook {
            self.currentBook = book
        }
        
        // 更新进度
        self.updateProgress()
    }
    
    /// 获取指定书籍的阅读进度
    /// - Parameter bookID: 书籍ID
    /// - Returns: 阅读进度信息（章节索引和页码）
    func getReadingProgress(bookID: String) -> (chapterIndex: Int, pageNumber: Int)? {
        return readingProgress[bookID]
    }
    
    /// 获取指定书籍的总页数
    /// - Parameter bookID: 书籍ID
    /// - Returns: 总页数
    func getTotalPages(bookID: String) -> Int {
        // TODO：这里可以根据实际需求实现获取总页数的逻辑
        return totalPages
    }
    
    // MARK: - ViewModel Interaction and View Setup (Placeholders)
    
    @MainActor
    func updateChaptersFromViewModel() {
        // TODO: Implement chapter update logic based on ReaderViewModel
        // 例如，如果 ReaderViewModel 有一个 chapters 属性，可以这样同步:
        // if let newChapters = readerViewModel.chapters?.map({ ($0.title, $0.id) }) { // 假设 viewModel.chapters 是 [(title: String, id: Int)]
        //     self.chapters = newChapters
        // }
        print("ReaderCoordinator: updateChaptersFromViewModel called (placeholder)")
    }
    
    @MainActor
    func setupPdfView() {
        // TODO: Implement PDF view setup logic if/when PDF support is finalized
        // 这可能涉及到配置 PDFKitPageViewer 或其他 PDF 相关的视图组件
        print("ReaderCoordinator: setupPdfView called (placeholder)")
    }
    
    // MARK: - 亮度调整
    
    @MainActor
    func adjustBrightness() {
        // TODO: 实现亮度调整逻辑，例如显示一个亮度调节滑块或直接修改屏幕亮度
        print("adjustBrightness called")
    }
    
    // MARK: - 章节管理方法
    
    /// 更新章节列表
    func updateChapters() {
        guard let book = currentBook else {
            self.chapters = []
            return
        }
        
        let bookTypeLowercased = book.bookType?.lowercased() ?? FileType.unknown.rawValue
        print("Updating chapters for book type: \(bookTypeLowercased)")
        
        switch bookTypeLowercased {
        case FileType.txt.rawValue:
            // 示例：从TXT文件内容提取章节（如果适用）
            // let extractedChapters = extractChaptersFromTXT(book.contentString) // Assuming content is available
            // self.chapters = extractedChapters
            self.chapters = (0..<5).map { (title: "第 \($0 + 1) 章 (示例)", index: $0) } // Placeholder, 修复元组标签以匹配 'index'
            
        case FileType.epub.rawValue:
            // TODO: 实现EPUB章节提取
            print("Updating chapters for EPUB (Placeholder)")
            // Example: Use totalChapters if available, otherwise parse structure
            // self.chapters = (0..<(book.totalChapters ?? 1)).map { (title: "EPUB Chapter \(\($0 + 1)", index: $0) }
            // More robust parsing needed here
            Task { // Use Task for async parsing if needed
                // TODO: 实现 EPUB 目录解析 (parseEPUBToc)
                // do {
                //     if let url = book.filePath.map({ URL(fileURLWithPath: $0) }) {
                //         let tocItems = try await parseEPUBToc(from: url)
                //         await MainActor.run {
                //             self.chapters = tocItems.enumerated().map { (index, item) in
                //                 (title: item.label, index: index) // Use enumeration index for now, needs mapping to page/content
                //             }
                //             print("EPUB chapters updated: \(self.chapters.count)")
                //         }
                //     } else {
                //         await MainActor.run { self.chapters = [] }
                //     }
                // } catch {
                //     print("Error parsing EPUB TOC: \(error)")
                //     await MainActor.run { self.chapters = [] }
                // }
                await MainActor.run { self.chapters = [] } // 暂时设置为空
            }
            
            
        case FileType.pdf.rawValue:
            print("Updating chapters for PDF")
            if let pdfDoc = self.document, let outline = pdfDoc.outlineRoot {
                self.chapters = parsePDFOutline(outline)
                print("PDF chapters updated from outline: \(self.chapters.count)")
            } else {
                self.chapters = []
                print("No PDF outline found or document not loaded.")
            }
            
        default:
            self.chapters = []
            print("Unsupported book type for chapter extraction: \(bookTypeLowercased)")
        }
        
        // Ensure UI updates on the main thread if chapters is @Published
        // objectWillChange.send() // If needed
    }
    
    
    /// 递归解析PDF目录
    private func parsePDFOutline(_ outline: PDFOutline, level: Int = 0) -> [(title: String, index: Int)] {
        var chapters: [(title: String, index: Int)] = []
        for i in 0..<outline.numberOfChildren {
            if let child = outline.child(at: i) {
                if let title = child.label, let destination = child.destination, let page = destination.page {
                    // 直接使用page对象获取页码，不需要检查pageRef
                    if let doc = self.document {
                        let pageIdx = doc.index(for: page) // 页码是0-based
                        chapters.append((title: title, index: pageIdx))
                        chapters.append(contentsOf: parsePDFOutline(child, level: level + 1))
                    }
                }
            }
        }
        return chapters
    }
}
