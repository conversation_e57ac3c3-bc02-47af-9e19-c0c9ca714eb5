import SwiftUI

/// 共享视图样式
/// 用于统一书架、书店和设置视图的UI风格
struct SharedViewStyles {
    // MARK: - 导航栏样式
    struct NavigationBarStyle {
        static let height: CGFloat = 44
        static let titleFont: Font = .system(size: 18, weight: .semibold)
        static let backgroundColor = Color(UIColor.systemBackground).opacity(0.98)
        static let shadowColor = Color.black.opacity(0.1)
        
        static func titleStyle(_ title: String) -> some View {
            Text(title)
                .font(titleFont)
                .foregroundColor(.primary)
        }
        
        static func container<Content: View>(_ content: Content) -> some View {
            content
                .frame(height: height)
                .background(backgroundColor)
        }
    }
    
    // MARK: - 列表样式
    struct ListStyle {
        static let itemSpacing: CGFloat = 12
        static let itemPadding = EdgeInsets(top: 12, leading: 16, bottom: 12, trailing: 16)
        static let cornerRadius: CGFloat = 12
        
        static func itemBackground(colorScheme: ColorScheme) -> some View {
            RoundedRectangle(cornerRadius: cornerRadius)
                .fill(colorScheme == .dark ? Color(uiColor: UIColor(red: 0.11, green: 0.11, blue: 0.12, alpha: 1.0)) : .white)
                .shadow(color: colorScheme == .dark ? .black.opacity(0.3) : .gray.opacity(0.1),
                        radius: 8, x: 0, y: 4)
        }
    }
    
    // MARK: - 按钮样式
    struct ButtonStyle {
        static let primaryColor = Color.accentColor
        static let secondaryColor = Color.gray.opacity(0.1)
        static let cornerRadius: CGFloat = 10
        static let height: CGFloat = 44
        
        static func primary<Content: View>(_ content: Content) -> some View {
            content
                .frame(height: height)
                .frame(maxWidth: .infinity)
                .background(primaryColor)
                .foregroundColor(.white)
                .clipShape(RoundedRectangle(cornerRadius: cornerRadius))
        }
        
        static func secondary<Content: View>(_ content: Content) -> some View {
            content
                .frame(height: height)
                .frame(maxWidth: .infinity)
                .background(secondaryColor)
                .foregroundColor(.primary)
                .clipShape(RoundedRectangle(cornerRadius: cornerRadius))
        }
    }
    
    // MARK: - 图标样式
    struct IconStyle {
        static let size: CGFloat = 24
        static let padding: CGFloat = 8
        
        static func icon(_ name: String) -> some View {
            Image(systemName: name)
                .font(.system(size: size))
                .frame(width: size + padding * 2, height: size + padding * 2)
        }
    }
    
    // MARK: - 文本样式
    struct TextStyle {
        static let titleFont: Font = .system(size: 18, weight: .semibold)
        static let subtitleFont: Font = .system(size: 16)
        static let bodyFont: Font = .system(size: 14)
        static let captionFont: Font = .system(size: 12)
        
        static func title(_ text: String) -> some View {
            Text(text).font(titleFont)
        }
        
        static func subtitle(_ text: String) -> some View {
            Text(text).font(subtitleFont)
        }
        
        static func body(_ text: String) -> some View {
            Text(text).font(bodyFont)
        }
        
        static func caption(_ text: String) -> some View {
            Text(text).font(captionFont)
        }
    }
    
    // MARK: - 搜索栏样式
    struct SearchBarStyle {
        static let height: CGFloat = 36
        static let cornerRadius: CGFloat = 10
        static let backgroundColor = Color.gray.opacity(0.1)
        
        static func searchBar(_ text: Binding<String>) -> some View {
            HStack(spacing: 8) {
                Image(systemName: "magnifyingglass")
                    .foregroundColor(.gray)
                TextField("搜索", text: text)
                    .textFieldStyle(PlainTextFieldStyle())
                if !text.wrappedValue.isEmpty {
                    Button(action: { text.wrappedValue = "" }) {
                        Image(systemName: "xmark.circle.fill")
                            .foregroundColor(.gray)
                    }
                }
            }
            .padding(.horizontal, 12)
            .frame(height: height)
            .background(backgroundColor)
            .clipShape(RoundedRectangle(cornerRadius: cornerRadius))
        }
    }
}