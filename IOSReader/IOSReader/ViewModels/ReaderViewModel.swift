//
//  ReaderViewModel.swift
//  IOSReader
//
//  ViewModel for the reader view, managing state like current page, font, theme.
//

import SwiftUI
import Combine
import SwiftData
import CoreText // 导入 CoreText 以使用 CTFramesetter 等
#if canImport(UIKit)
import UIKit // 导入 UIKit 以使用 UIColor 等
#elseif canImport(AppKit)
import AppKit // 导入 AppKit 以使用 NSColor 等
#endif
// ReaderSettings 定义已移至 ReaderTypes.swift 以解决歧义

// 尝试导入 Book 模型，如果 Book.swift 在 IOSReader 模块的 Models 文件夹下
// import IOSReader.Models // 这种方式可能不适用于 Swift Package 或者项目结构
// 更通用的方式是确保 Book 类型在当前模块内可见，或者通过正确的模块导入
// 假设 Book 定义在与 ReaderViewModel 相同的 Target (IOSReader) 下的 Models 目录中
// 通常不需要显式导入，除非有命名空间冲突或特殊设置。
// 如果 Book.swift 确实在 Models/Book.swift 并且是同一个 target 的一部分，
// 编译器应该能自动找到它。错误 'cannot find 'Book' in scope' 可能暗示
// 1. Book.swift 没有被正确添加到 Target。
// 2. Book.swift 中的 Book 类型定义有误或访问控制级别阻止了访问。
// 3. 编译顺序或缓存问题。
// 鉴于之前的日志，Book 模型应该存在。我们先确保文件顶部有必要的 import。
// 如果 Book 是 SwiftData 模型，它应该能被自动找到。
// 让我们先尝试不加特定导入，因为 Swift 通常会自动处理同一模块内的类型。
// 如果问题依然存在，说明问题可能更深层，比如 Target Membership 或者 Book 自身的定义。

@MainActor
public class ReaderViewModel: ObservableObject, @unchecked Sendable { // 修改：将类声明为 public
    // MARK: - Published Properties
    @Published var currentPage: Int = 0 // 重命名 currentPageIndex 为 currentPage
    @Published var totalPages: Int = 0 {
        didSet {
            print("ReaderViewModel.totalPages didSet: \(totalPages)")
        }
    }
    @Published var settings: ReaderSettings // 阅读设置
    @Published var isLoading: Bool = false
    @Published var loadingProgress: Double = 0.0 // 新增：书籍加载进度
    @Published var errorMessage: String? = nil
    @Published var visibleContent: NSAttributedString? = nil { // 当前页的富文本内容
        didSet {
            let contentLength = visibleContent?.length ?? 0
            print("ReaderViewModel.visibleContent didSet: length = \(contentLength)")
        }
    }
    @Published var currentChapterIndex: Int = 0 // 当前章节索引
    @Published var chapters: [Chapter] = [] // 章节列表
    @Published var readingProgress: Double = 0.0 // 阅读进度
    @Published var bookmarks: [Bookmark] = [] // 书签列表
    @Published var currentChapterTitle: String = "" // 新增：当前章节标题
    @Published var pageContent: [NSAttributedString] = [] { // 新增：分页后的内容，以匹配 UnifiedReaderView 的期望
        didSet {
            print("ReaderViewModel.pageContent didSet: count = \(pageContent.count)")
        }
    }
    @Published var pages: [String] = [] // 新增：解决 'no member pages' 编译错误，具体类型和用途可能需要根据实际使用调整
    
    private var modelContainer: ModelContainer
    private var cancellables = Set<AnyCancellable>()
    
    var currentBook: Book? // 新增：存储当前书籍信息
    private var fullAttributedContent: NSAttributedString? // 新增：存储完整的书籍内容的富文本
    @Published var currentViewSize: CGSize = ReaderViewModel.defaultViewSize // 修改：设置默认尺寸而不是零值
    @Published var needsPagination: Bool = false // 修改：标记是否需要在获取尺寸后进行分页，并设为Published以供订阅
    
    private static let defaultViewSize = CGSize(width: 375, height: 667) // 新增：默认视图尺寸常量

    private var paginationRetryCount: Int = 0 // 新增：分页重试计数器
    private var viewSizeIsValidCancellable: AnyCancellable? // 新增：用于等待 viewSize 有效的订阅
    private let maxPaginationRetries: Int = 5 // 新增：最大分页重试次数
    private let paginationRetryDelay: TimeInterval = 0.2 // 新增：分页重试延迟时间（秒）

    // MARK: - Initialization
    init(modelContainer: ModelContainer, settings: ReaderSettings = ReaderSettings()) {
        self.modelContainer = modelContainer
        self.settings = settings
        // TODO: Initialize other properties or load initial data
        
        // 监听 settings 的变化以重新分页
        $settings
            .removeDuplicates() // 仅当设置实际更改时才触发
            .debounce(for: .milliseconds(300), scheduler: RunLoop.main)
            .sink { [weak self] newSettings in
                Task { @MainActor [weak self] in // 确保在主actor上执行，如果 applySettingsAndRepaginate 更新UI
                    await self?.applySettingsAndRepaginate(newSettings)
                }
            }
            .store(in: &cancellables)

        // 订阅 needsPagination 和 currentViewSize 的组合变化
        Publishers.CombineLatest($needsPagination, $currentViewSize)
            .debounce(for: .milliseconds(100), scheduler: RunLoop.main) // 轻微防抖
            .sink { [weak self] (triggeredNeedsPaginate, triggeredViewSize) in
                guard let self = self else { return }

                // 主要依赖 self.needsPagination 的实时状态
                if self.needsPagination {
                    print("ReaderViewModel.CombineLatest.sink: needsPagination is true. Current self.currentViewSize: \(self.currentViewSize).")
                    // 检查当前的 self.currentViewSize 是否有效
                    if self.currentViewSize.width > 0 && self.currentViewSize.height > 0 {
                        print("ReaderViewModel.CombineLatest.sink: currentViewSize is valid. Calling attemptPagination directly.")
                        Task { @MainActor in
                            await self.attemptPagination()
                        }
                    } else {
                        // 如果 currentViewSize 无效，等待一小段时间再尝试
                        print("ReaderViewModel.CombineLatest.sink: currentViewSize is NOT valid. Waiting briefly before retrying attemptPagination.")
                        Task { @MainActor in
                            try? await Task.sleep(nanoseconds: 200_000_000) // 等待 200 毫秒
                            print("ReaderViewModel.CombineLatest.sink: After delay, currentViewSize: \(self.currentViewSize). Retrying attemptPagination.")
                            if self.needsPagination && self.currentViewSize.width > 0 && self.currentViewSize.height > 0 {
                                await self.attemptPagination()
                            } else {
                                print("ReaderViewModel.CombineLatest.sink: After delay, conditions still not met or needsPagination became false. Not paginating.")
                            }
                        }
                    }
                } else {
                    print("ReaderViewModel.CombineLatest.sink: self.needsPagination is false. No action taken.")
                }
            }
            .store(in: &cancellables)
    }
    
    // MARK: - Public Methods
    
    /// 更新阅读视图尺寸并触发分页。
    public func updateReaderViewSize(_ newSize: CGSize) {
        print("ReaderViewModel.updateReaderViewSize: 收到新尺寸 \(newSize)")
        
        let effectiveSize: CGSize // 将声明移到if语句之前
        // 检查尺寸是否有效
        if newSize.width > 0 && newSize.height > 0 {
            effectiveSize = newSize
        } else {
            // 如果新尺寸无效，并且当前尺寸已经是默认尺寸，则不进行操作
            if self.currentViewSize == ReaderViewModel.defaultViewSize {
                print("ReaderViewModel.updateReaderViewSize: 新尺寸无效，当前已是默认尺寸，不执行操作")
                return
            }
            effectiveSize = ReaderViewModel.defaultViewSize
        }
        
        if self.currentViewSize != effectiveSize {
            self.currentViewSize = effectiveSize
            print("ReaderViewModel.updateReaderViewSize: currentViewSize 更新为 \(self.currentViewSize)")
            // 如果内容已加载，视图尺寸变化意味着需要重新分页
            if self.fullAttributedContent != nil && self.fullAttributedContent!.length > 0 {
                self.needsPagination = true
                print("ReaderViewModel.updateReaderViewSize: 尺寸已更改或初始化为 \(self.currentViewSize)，设置 needsPagination = true")
            } else {
                print("ReaderViewModel.updateReaderViewSize: 尺寸已更改或初始化为 \(self.currentViewSize)，但无内容，不立即设置 needsPagination")
            }
        } else {
            // 即使尺寸相同，如果之前因为尺寸无效而未分页，且现在有内容，也应尝试分页
            if self.needsPagination == false && (newSize.width <= 0 || newSize.height <= 0) && self.fullAttributedContent != nil && self.fullAttributedContent!.length > 0 {
                 print("ReaderViewModel.updateReaderViewSize: 尺寸仍无效但之前未分页且有内容，尝试强制分页")
                 self.needsPagination = true // 确保在内容加载后，即使尺寸仍是默认/无效，也会尝试一次分页
            } else if self.currentViewSize == newSize {
                 print("ReaderViewModel.updateReaderViewSize: 新尺寸与当前尺寸相同 \(newSize)，不执行操作")
            }
        }
    }
    
    func loadBookContent(book: Book) async {
        await MainActor.run {
            isLoading = true
            loadingProgress = 0.0 // 重置加载进度
            errorMessage = nil
            self.currentBook = book
        }
        
        print("ReaderViewModel.loadBookContent: Starting for book '\(book.title)'")
        print("ReaderViewModel.loadBookContent: Type='\(book.bookType ?? "N/A")', Path='\(book.filePath ?? "N/A")'")
        print("ReaderViewModel.loadBookContent: Initial Introduction length=\(book.introduction?.count ?? 0)")
        if let intro = book.introduction, intro.count < 200 && !intro.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            print("ReaderViewModel.loadBookContent: Initial Introduction content (first 200 chars)='\(String(intro.prefix(200)))'")
        } else if book.introduction?.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty == true {
            print("ReaderViewModel.loadBookContent: Initial Introduction is empty or whitespace.")
        }
        
        var actualContent: String? // This will hold the content we intend to use for display
        
        if book.bookType?.lowercased() == "txt" {
            print("ReaderViewModel.loadBookContent: [TXT Path] Processing TXT book.")
            if let filePath = book.filePath, !filePath.isEmpty {
                print("ReaderViewModel.loadBookContent: [TXT Path] Attempting to read from filePath: \(filePath)")
                do {
                    let fileURL = URL(fileURLWithPath: filePath)
                    let gb18030Encoding = String.Encoding(rawValue: CFStringConvertEncodingToNSStringEncoding(CFStringEncoding(CFStringEncodings.GB_18030_2000.rawValue)))
                    
                    // 尝试1: 使用 String(contentsOf:encoding:) 直接读取 GB18030
                    if let decodedContent = try? String(contentsOf: fileURL, encoding: gb18030Encoding) {
                        actualContent = decodedContent
                        print("✅ ReaderViewModel.loadBookContent: [TXT Path] Method 1: Successfully read using String(contentsOf:encoding: GB18030). Content length=\(actualContent?.count ?? 0)")
                        // 尝试2: 使用 String(contentsOf:encoding:) 直接读取 UTF-8
                    } else if let decodedContent = try? String(contentsOf: fileURL, encoding: .utf8) {
                        actualContent = decodedContent
                        print("✅ ReaderViewModel.loadBookContent: [TXT Path] Method 1: Successfully read using String(contentsOf:encoding: UTF-8). Content length=\(actualContent?.count ?? 0)")
                    } else {
                        // 尝试3: 回退到 Data(contentsOf:) 然后解码 (如果 String(contentsOf:encoding:) 失败)
                        print("⚠️ ReaderViewModel.loadBookContent: [TXT Path] Method 1 (String(contentsOf:encoding:)) failed for GB18030 and UTF-8. Attempting Method 2 (Data(contentsOf:))...")
                        let rawData = try Data(contentsOf: fileURL) // 如果这里失败，会被外层 catch 捕获
                        if let decodedContentGB = String(data: rawData, encoding: gb18030Encoding) {
                            actualContent = decodedContentGB
                            print("✅ ReaderViewModel.loadBookContent: [TXT Path] Method 2: Successfully read via Data(contentsOf:) + GB18030. Content length=\(actualContent?.count ?? 0)")
                        } else if let decodedContentUTF = String(data: rawData, encoding: .utf8) {
                            actualContent = decodedContentUTF
                            print("✅ ReaderViewModel.loadBookContent: [TXT Path] Method 2: Successfully read via Data(contentsOf:) + UTF-8. Content length=\(actualContent?.count ?? 0)")
                        } else {
                            print("⚠️ ReaderViewModel.loadBookContent: [TXT Path] Method 2: All decoding attempts failed for data read by Data(contentsOf:). Falling back to book.introduction.")
                            actualContent = book.introduction // Fallback
                        }
                    }
                } catch {
                    print("Error reading TXT file from path \(filePath) (either String(contentsOf:) or Data(contentsOf:) failed): \(error.localizedDescription). Falling back to book.introduction.")
                    actualContent = book.introduction // Fallback on any read error
                }
            } else {
                print("ReaderViewModel.loadBookContent: [TXT Path] filePath is nil or empty. Using book.introduction.")
                actualContent = book.introduction // No filePath, use introduction
            }
            // Ensure actualContent is not nil, default to empty string if all sources fail
            if actualContent == nil {
                print("ReaderViewModel.loadBookContent: [TXT Path] All content sources (filePath, introduction) are nil. Setting content to empty string.")
                actualContent = ""
            }
        } else {
            // For non-TXT books, use introduction
            print("ReaderViewModel.loadBookContent: [Non-TXT Path] Using book.introduction.")
            actualContent = book.introduction
        }
        
        // Ensure actualContent is not nil for chapter creation and loadContent calls
        let contentForProcessing = actualContent ?? ""
        print("ReaderViewModel.loadBookContent: Final contentForProcessing length=\(contentForProcessing.count)")
        if contentForProcessing.count < 200 && !contentForProcessing.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            print("ReaderViewModel.loadBookContent: Final contentForProcessing (first 200 chars)='\(String(contentForProcessing.prefix(200)))'")
        } else if contentForProcessing.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            print("ReaderViewModel.loadBookContent: Final contentForProcessing is empty or whitespace.")
        }
        
        // 模拟加载进度更新
        for i in 1...5 {
            try? await Task.sleep(nanoseconds: 100_000_000) // 模拟耗时操作
            let progressValue = Double(i) * 0.1 // 更新进度 10%, 20%, ..., 50%
            DispatchQueue.main.async {
                self.loadingProgress = progressValue
            }
        }
        
        // 调用 book.fetchChapters 获取章节列表
        // 注意：Book.swift 中的 fetchChapters 方法需要能处理异步回调中的UI更新（如果直接更新进度条的话）
        // 或者如此处所示，在 fetchChapters 完成后，在这里更新UI相关的 chapters 属性
        do {
            let fetchedChapters = try await book.fetchChapters { progress in
                // 使用异步版本的fetchChapters，可以在这里更新loadingProgress
                await MainActor.run {
                    self.loadingProgress = 0.5 + (progress * 0.3) // 假设章节获取占总进度的30%，起始于50%
                    print("ReaderViewModel.loadBookContent: fetchChapters progress - \(progress)")
                }
            }
            await MainActor.run {
                self.chapters = fetchedChapters
            }
        } catch {
            print("获取章节失败: \(error.localizedDescription)")
            self.chapters = []
        }

        await MainActor.run {
            print("ReaderViewModel.loadBookContent: book.fetchChapters returned \(self.chapters.count) chapters.")
            // 模拟章节获取后的加载进度更新 (例如，到 80%)
            self.loadingProgress = 0.8
        }

        if self.chapters.isEmpty {
            print("ReaderViewModel.loadBookContent: fetchChapters returned empty or failed. Creating a single fallback chapter.")
            let fallbackChapterContent = contentForProcessing // contentForProcessing 是之前获取的书籍完整内容字符串
            // 使用与 Chapter.swift 中定义的初始化方法匹配的方式创建章节
            // 假设 Chapter 有一个接受 index, title, bookId, content 的初始化器
            let fallbackChapter = Chapter(index: 0, title: book.title, startPosition: 0, endPosition: fallbackChapterContent.count, content: fallbackChapterContent, bookId: book.id)
            DispatchQueue.main.async {
                self.chapters = [fallbackChapter]
                print("ReaderViewModel.loadBookContent: Created fallback chapter with title '\(book.title)' and content length \(fallbackChapterContent.count).")
            }
        }

        if let firstChapter = self.chapters.first {
            self.currentChapterIndex = 0 // 确保从第一章开始
            self.currentChapterTitle = firstChapter.title ?? book.title // 设置当前章节标题
            print("ReaderViewModel.loadBookContent: Loading first chapter: '\(self.currentChapterTitle)'. Content length: \(firstChapter.content?.count ?? 0)")
            await loadChapterContent(firstChapter) // 加载第一章的内容
        } else {
            // 理论上在有回退逻辑后，chapters 不应为空。但为保险起见，处理此情况。
            print("ReaderViewModel.loadBookContent: Chapters list is unexpectedly empty even after fallback. Loading full content directly.")
            self.currentChapterTitle = book.title // 使用书名作为标题
            await loadContent(contentForProcessing) // 直接加载书籍的全部内容
        }
        // 模拟加载进度更新
        loadingProgress = 0.9 // 90%
        try? await Task.sleep(nanoseconds: 100_000_000) // 模拟耗时操作
        
        // 更新阅读进度
        updateReadingProgress()
        
        // 加载书签
        await loadBookmarks()
        
        loadingProgress = 1.0 // 完成加载
        isLoading = false
    } // 正确结束 loadBookContent 方法

    // } // 结束 ReaderViewModel 类 -> 这个应该在文件末尾

    public func cancelLoading() async {
        // 取消所有正在进行的任务
        for cancellable in cancellables {
            cancellable.cancel()
        }
        cancellables.removeAll()
        
        // 重置加载状态
        await MainActor.run {
            self.isLoading = false
            self.loadingProgress = 0.0
            self.errorMessage = nil
            self.fullAttributedContent = nil
            self.visibleContent = nil
            self.pageContent.removeAll()
            self.needsPagination = false
            print("ReaderViewModel: 已取消所有加载任务并重置状态")
        }
    }
    
    // 修改方法签名并实现逻辑
    func loadBook(from filePath: String) async throws { // 添加 throws
        self.isLoading = true
        self.loadingProgress = 0.0
        self.errorMessage = nil
        // self.viewSize = viewSize // viewSize 将通过 updateViewSize 单独设置
        
        // 1. 从 filePath 解析 Book 对象 (可能需要访问 modelContainer)
        let predicate = #Predicate<Book> { book in
            book.filePath == filePath
        }
        let descriptor = FetchDescriptor<Book>(predicate: predicate)
        
        do {
            let books = try modelContainer.mainContext.fetch(descriptor)
            guard let book = books.first else {
                self.errorMessage = "无法找到书籍: \(filePath)"
                self.isLoading = false
                self.loadingProgress = 1.0 // 即使失败也标记完成
                
                // 调试信息：打印数据库中所有书籍的 filePath
                print("调试信息：尝试加载路径 \(filePath) 失败。当前数据库中的书籍路径：")
                let allBooksDescriptor = FetchDescriptor<Book>()
                do {
                    let allBooks = try modelContainer.mainContext.fetch(allBooksDescriptor)
                    if allBooks.isEmpty {
                        print("数据库中没有书籍记录。")
                    } else {
                        allBooks.forEach { bookEntry in
                            print("- \(bookEntry.filePath ?? "未知路径") (标题: \(bookEntry.title))")
                        }
                    }
                } catch {
                    print("获取所有书籍路径失败: \(error.localizedDescription)")
                }
                
                // throw URLError(.fileDoesNotExist) // 可以抛出更具体的错误
                return // 或者直接返回，让 errorMessage 显示
            }
            // 2. 调用 self.loadBookContent(book: loadedBook)
            await self.loadBookContent(book: book)
        } catch {
            self.errorMessage = "加载书籍失败: \(error.localizedDescription)"
            self.isLoading = false
            self.loadingProgress = 1.0 // 即使失败也标记完成
            // throw error // 将错误继续抛出，如果调用者需要处理
        }
    }
    

    
    private func loadChapterContent(_ chapter: Chapter) async {
        guard let content = chapter.content else {
            errorMessage = "章节内容为空"
            return
        }
        
        await loadContent(content)
    }
    
    private func loadContent(_ content: String) async {
        await MainActor.run {
            print("ReaderViewModel.loadContent: Received content length=\(content.count)")
            if content.count < 200 && !content.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
                print("ReaderViewModel.loadContent: Received content (first 200 chars)='\(String(content.prefix(200)))'")
            } else if content.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
                print("ReaderViewModel.loadContent: Received content is empty or whitespace.")
            }
        }
        
        // 创建段落样式
        let paragraphStyle = NSMutableParagraphStyle()
        paragraphStyle.lineSpacing = settings.lineSpacing
        paragraphStyle.paragraphSpacing = settings.paragraphSpacing
        
        // 创建基础富文本属性
        let attributes: [NSAttributedString.Key: Any] = [
            .font: UIFont.systemFont(ofSize: settings.fontSize),
            .foregroundColor: settings.textColor.toUIColor(),
            .paragraphStyle: paragraphStyle
        ]
        
        self.fullAttributedContent = NSAttributedString(string: content, attributes: attributes)
        print("ReaderViewModel.loadContent: fullAttributedContent prepared. Setting needsPagination to true.")
        self.needsPagination = true // 标记需要分页，这将触发 CombineLatest 订阅
        
        // 显式调用 attemptPagination，不仅依赖 CombineLatest 处理
        // 如果当前视图尺寸有效，立即尝试分页
        if self.currentViewSize.width > 0 && self.currentViewSize.height > 0 {
            print("ReaderViewModel.loadContent: currentViewSize is valid (\(self.currentViewSize)). Calling attemptPagination directly.")
            await self.attemptPagination()
        } else {
            // 如果视图尺寸无效，使用默认尺寸进行初始分页
            print("ReaderViewModel.loadContent: currentViewSize is invalid (\(self.currentViewSize)). Using default size for initial pagination.")
            // 临时设置默认尺寸进行分页，这有助于在视图尚未布局时提供初步内容
            _ = self.currentViewSize
            self.currentViewSize = ReaderViewModel.defaultViewSize
            print("ReaderViewModel.loadContent: Temporarily setting currentViewSize to default: \(ReaderViewModel.defaultViewSize)")
            
            // 使用默认尺寸进行分页
            await self.attemptPagination()
            
            // 不恢复 currentViewSize 到可能无效的 originalSize
            // 保持 needsPagination = true，等待 CombineLatest 或 updateViewSize 触发正确的分页
            self.needsPagination = true // 确保在真实尺寸可用时重新分页
            print("ReaderViewModel.loadContent: Used default size for initial pagination. needsPagination is true. Waiting for valid view size update.")
        }
    }

    /// 尝试进行分页的核心逻辑，由 viewSize.didSet 和 $needsPagination 触发
    private func attemptPagination() async {
        await MainActor.run {
            // 日志记录：打印当时的 self.currentViewSize 和 self.needsPagination 状态
            print("ReaderViewModel.attemptPagination: Entry. Current currentViewSize: \(self.currentViewSize), needsPagination: \(self.needsPagination)")
        }

        // 检查视图尺寸是否有效
        let viewSizeIsValid = self.currentViewSize.width > 0 && self.currentViewSize.height > 0
        
        // 如果视图尺寸无效但我们使用了默认尺寸，则允许继续
        let usingDefaultSize = !viewSizeIsValid && self.currentViewSize == ReaderViewModel.defaultViewSize
        
        guard viewSizeIsValid || usingDefaultSize else {
            await MainActor.run {
                print("ReaderViewModel.attemptPagination: currentViewSize is not valid (\(self.currentViewSize)). Cannot paginate yet.")
                self.errorMessage = "视图尺寸无效，无法进行分页"
            }
            return // 视图尺寸无效，无法分页
        }

        guard self.needsPagination else {
            await MainActor.run {
                print("ReaderViewModel.attemptPagination: needsPagination is false. No pagination required or already done.")
            }
            return // 不需要分页
        }

        guard let content = self.fullAttributedContent, content.length > 0 else {
            await MainActor.run {
                print("ReaderViewModel.attemptPagination: fullAttributedContent is nil or empty. Cannot paginate.")
                self.errorMessage = "没有可分页的内容"
            }
            return // 没有内容可分页
        }

        print("ReaderViewModel.attemptPagination: All conditions met (currentViewSize: \(self.currentViewSize), needsPagination: true, content exists). Calling paginateContent().")
        await self.paginateContent() // 执行分页
    }
    
    /// 更新阅读进度
    /// 根据当前章节和页码计算阅读进度
    private func updateReadingProgress() {
        guard totalPages > 0 else {
            readingProgress = 0.0
            return
        }
        
        let totalPagesInAllChapters = chapters.count
        let currentPageInChapter = currentPage
        let pagesBeforeCurrentChapter = currentChapterIndex
        
        readingProgress = Double(pagesBeforeCurrentChapter + currentPageInChapter) / Double(totalPagesInAllChapters)
    }
    
    /// 创建错误提示的富文本字符串
    /// - Parameter message: 错误信息
    /// - Returns: 格式化的富文本字符串
    private func createErrorAttributedString(_ message: String) -> NSAttributedString {
        #if canImport(UIKit)
        return NSAttributedString(string: message, attributes: [.foregroundColor: UIColor.black])
        #elseif canImport(AppKit)
        return NSAttributedString(string: message, attributes: [.foregroundColor: NSColor.black])
        #endif
    }
    
    /// 重置分页状态
    /// - Parameter error: 可选的错误信息
    @MainActor
    private func resetPaginationState(withError error: String? = nil) {
        self.pageContent = []
        self.totalPages = 0
        self.currentPage = 0
        self.needsPagination = error != nil
        if let error = error {
            self.errorMessage = error
        }
        updateVisibleContent()
        updateReadingProgress()
    }
    
    // MARK: - 导航方法
    
    /// 跳转到下一页
    /// 如果当前页是章节的最后一页，会尝试加载下一章节
    public func nextPage() async {
        await MainActor.run {
            if currentPage < totalPages - 1 {
                currentPage += 1
                updateVisibleContent()
                updateReadingProgress()
                return
            }
            
            guard currentChapterIndex < chapters.count - 1 else { return }
            
            currentChapterIndex += 1
            Task {
                await loadNextChapter()
            }
        }
    }
    
    /// 加载下一章节的内容
    @MainActor
    private func loadNextChapter() async {
        let chapterToLoad = chapters[currentChapterIndex]
        currentChapterTitle = chapterToLoad.title
        
        do {
            if let chapterContent = chapterToLoad.content, !chapterContent.isEmpty {
                await loadContent(chapterContent)
            } else if let extractedContent = try await extractChapterContent(from: chapterToLoad) {
                await loadContent(extractedContent)
            } else {
                throw ReaderError.chapterLoadingFailed
            }
            
            currentPage = 0
            updateReadingProgress()
        } catch {
            await resetPaginationState(withError: "无法加载下一章节")
            currentChapterIndex -= 1
            currentChapterTitle = chapters[currentChapterIndex].title ?? "默认标题"
            updateReadingProgress()
        }
    }
    
    /// 从完整内容中提取章节内容
    /// - Parameter chapter: 需要提取内容的章节
    /// - Returns: 提取的章节内容
    private func extractChapterContent(from chapter: Chapter) async throws -> String? {
        guard let fullContent = fullAttributedContent,
              chapter.startPosition >= 0,
              let endPosition = chapter.endPosition,
              endPosition > chapter.startPosition,
              endPosition <= fullContent.length else {
            return nil
        }
        
        let range = NSRange(location: chapter.startPosition, length: endPosition - chapter.startPosition)
        return (fullContent.string as NSString).substring(with: range)
    }
    
    /// 跳转到上一页
    /// 如果当前页是章节的第一页，会尝试加载上一章节的最后一页
    public func previousPage() async {
        await MainActor.run {
            if currentPage > 0 {
                currentPage -= 1
                updateVisibleContent()
                updateReadingProgress()
                return
            }
            
            guard currentChapterIndex > 0 else { return }
            
            currentChapterIndex -= 1
            Task {
                await loadPreviousChapter()
            }
        }
    }
    
    /// 切换到上一章节（公共接口）
    @MainActor
    public func previousChapter() async {
        await loadPreviousChapter()
    }
    
    /// 切换到下一章节（公共接口）
    @MainActor
    public func nextChapter() async {
        guard currentChapterIndex < chapters.count - 1 else { 
            errorMessage = "已经是最后一章"
            return 
        }
        
        currentChapterIndex += 1
        await loadNextChapter()
    }
    
    /// 加载上一章节的内容
    @MainActor
    private func loadPreviousChapter() async {
        let chapterToLoad = chapters[currentChapterIndex]
        currentChapterTitle = chapterToLoad.title
        
        do {
            if let chapterContent = chapterToLoad.content, !chapterContent.isEmpty {
                await loadContent(chapterContent)
            } else if let extractedContent = try await extractChapterContent(from: chapterToLoad) {
                await loadContent(extractedContent)
            } else {
                throw ReaderError.chapterLoadingFailed
            }
            
            currentPage = totalPages > 0 ? totalPages - 1 : 0
            updateReadingProgress()
        } catch {
            await resetPaginationState(withError: "无法加载上一章节")
            currentChapterIndex += 1
            currentChapterTitle = chapters[currentChapterIndex].title
            updateReadingProgress()
        }
    }
    


    
    /// 跳转到指定章节
    /// - Parameter index: 章节索引
    public func jumpToChapter(_ index: Int) async {
        await MainActor.run {
            guard index >= 0 && index < chapters.count else {
                self.errorMessage = "无效的章节索引：\(index)"
                return
            }
            
            let previousIndex = currentChapterIndex
            currentChapterIndex = index
            currentChapterTitle = chapters[index].title ?? "默认标题"
            
            Task {
                do {
                    try await loadSpecificChapter(index)
                } catch {
                    // 恢复到之前的章节
                    currentChapterIndex = previousIndex
                    currentChapterTitle = chapters[previousIndex].title
                    self.errorMessage = "无法加载指定章节"
                }
            }
        }
    }
    
    /// 加载指定章节的内容
    /// - Parameter index: 章节索引
    @MainActor
    private func loadSpecificChapter(_ index: Int) async throws {
        let chapterToLoad = chapters[index]
        
        if let chapterContent = chapterToLoad.content, !chapterContent.isEmpty {
            await loadContent(chapterContent)
        } else if let extractedContent = try await extractChapterContent(from: chapterToLoad) {
            await loadContent(extractedContent)
        } else {
            throw ReaderError.chapterLoadingFailed
        }
        
        currentPage = 0
        updateReadingProgress()
    }
    
    // 跳转到指定书签位置
    // 跳转到指定书签位置
    @MainActor
    func jumpToBookmark(_ bookmark: Bookmark) async {
        do {
            // 验证书签有效性
            guard let chapterIndex = bookmark.chapterIndex,
                  chapterIndex >= 0 && chapterIndex < chapters.count else {
                self.errorMessage = "无效的书签：章节信息错误"
                return
            }
            
            // 如果章节不同，先加载章节
            if chapterIndex != currentChapterIndex {
                currentChapterIndex = chapterIndex
                if let chapter = chapters[safe: chapterIndex] {
                    await loadChapterContent(chapter)
                } else {
                    self.errorMessage = "无法加载章节：章节索引无效"
                    return
                }
            }
            
            // 验证页码有效性并跳转
            if bookmark.pageIndex >= 0 && bookmark.pageIndex < totalPages {
                currentPage = bookmark.pageIndex
                updateVisibleContent()
                updateReadingProgress()
                print("成功跳转到书签：\(bookmark.content)")
            } else {
                self.errorMessage = "无效的书签：页码超出范围"
                currentPage = 0
                updateVisibleContent()
                updateReadingProgress()
            }
        } catch {
            self.errorMessage = "跳转到书签失败：\(error.localizedDescription)"
            print("跳转到书签失败：\(error)")
        }
    }
    
    // MARK: - 书签管理
    
    func deleteBookmark(_ bookmark: Bookmark) async throws {
        // 从本地数组中移除以立即更新UI
        bookmarks.removeAll { $0.id == bookmark.id }
        
        // 从 SwiftData 中删除
        // 确保在主actor上执行SwiftData操作
        guard let bookId = currentBook?.id else {
            print("错误：未设置当前书籍，无法从上下文中删除书签。")
            throw ReaderError.loadingFailed("未设置当前书籍")
        }
        
        let idToDelete = bookmark.id
        let currentBookCheckedId = bookId // bookId is from guard let bookId = currentBook?.id
        let predicate = #Predicate<Bookmark> { b in
            b.id == idToDelete && b.bookId == currentBookCheckedId
        }
        let descriptor = FetchDescriptor(predicate: predicate)
        
        do {
            if let bookmarkToDelete = try modelContainer.mainContext.fetch(descriptor).first {
                modelContainer.mainContext.delete(bookmarkToDelete)
                try modelContainer.mainContext.save()
                print("成功从SwiftData删除书签。")
            }
        } catch {
            print("从SwiftData删除书签失败: \(error)")
            throw error
        }
    }
    
    @MainActor
    func loadBookmarks() async {
        guard let bookId = currentBook?.id else {
            print("错误：无法加载书签，当前书籍ID未设置。")
            self.bookmarks = []
            return
        }
        
        let context = modelContainer.mainContext
        let fetchDescriptor = FetchDescriptor<Bookmark>(predicate: #Predicate { $0.bookId == bookId })
        
        do {
            let fetchedBookmarks = try context.fetch(fetchDescriptor)
            self.bookmarks = fetchedBookmarks
        } catch {
            print("为书籍 \(bookId) 加载书签失败: \(error.localizedDescription)")
            self.bookmarks = []
        }
    }
    
    @MainActor
    func addBookmark(description: String? = nil) async {
        guard let book = currentBook else {
            self.errorMessage = "无法添加书签：当前没有打开的书籍"
            return
        }
        
        guard currentPage < pageContent.count else {
            self.errorMessage = "无法添加书签：当前页面不存在"
            return
        }
        
        // 获取当前页面的内容预览
        let contentPreview = pageContent[currentPage].string.prefix(50).trimmingCharacters(in: .whitespacesAndNewlines)
        
        let newBookmark = Bookmark(
            bookId: book.id,
            pageIndex: currentPage,
            chapterIndex: currentChapterIndex,
            content: contentPreview,
            userDescription: description
        )
        
        bookmarks.append(newBookmark)
        await saveBookmarks()
        
        print("添加书签成功：\(newBookmark.content)")
    }
    
    @MainActor
    func saveBookmarks() async {
        guard let book = currentBook else { return }
        
        do {
            let currentBookID = book.id
            let descriptor = FetchDescriptor<Bookmark>(predicate: #Predicate<Bookmark> { bookmark in
                bookmark.bookId == currentBookID
            })
            
            // 先删除旧书签
            let existingBookmarks = try modelContainer.mainContext.fetch(descriptor)
            for bookmark in existingBookmarks {
                modelContainer.mainContext.delete(bookmark)
            }
            
            // 添加新书签
            for bookmark in bookmarks {
                modelContainer.mainContext.insert(bookmark)
            }
            
            try modelContainer.mainContext.save()
            print("成功保存 \(bookmarks.count) 个书签到 SwiftData")
        } catch {
            print("保存书签到 SwiftData 失败: \(error.localizedDescription)")
            self.errorMessage = "保存书签失败：\(error.localizedDescription)"
        }
    }
    
    // func updateFontSize(to size: CGFloat) { // 将通过 settings 更新
    //     settings.fontSize = size
    //     // paginateContent() // 由 settings 的观察者处理
    // }
    
    // func changeTheme(to newTheme: ReaderTheme) { // 将通过 settings 更新
    //     settings.theme = newTheme
    //     // paginateContent() // 由 settings 的观察者处理
    // }
    
    // 新增：更新视图尺寸并触发重新分页
    
    
    // MARK: - 私有方法
    
    /// 应用新的阅读设置并重新分页
    /// - Parameter newSettings: 新的阅读设置
    private func applySettingsAndRepaginate(_ newSettings: ReaderSettings) async {
        // 更新富文本属性并重新分页
        guard let rawContent = currentBook?.introduction else {
            // 如果没有原始内容，可能不需要重新生成 fullAttributedContent
            // 但如果字体等影响现有 pages，仍需重新分页
            if fullAttributedContent != nil {
                // 尝试基于现有 fullAttributedContent 更新属性，或重新创建它
                let currentText = fullAttributedContent!.string
                let paragraphStyle = NSMutableParagraphStyle()
                paragraphStyle.lineSpacing = newSettings.lineSpacing
                paragraphStyle.paragraphSpacing = newSettings.paragraphSpacing
                
                let attributes: [NSAttributedString.Key: Any] = [
                    .font: UIFont.systemFont(ofSize: newSettings.fontSize),
                    .foregroundColor: newSettings.textColor.toUIColor(), // 使用 newSettings.textColor
                    .paragraphStyle: paragraphStyle
                ]
                self.fullAttributedContent = NSAttributedString(string: currentText, attributes: attributes)
                await paginateContent()
            }
            return
        }
        
        let paragraphStyle = NSMutableParagraphStyle()
        paragraphStyle.lineSpacing = newSettings.lineSpacing
        paragraphStyle.paragraphSpacing = newSettings.paragraphSpacing
        
        let attributes: [NSAttributedString.Key: Any] = [
            .font: UIFont.systemFont(ofSize: newSettings.fontSize),
            .foregroundColor: newSettings.textColor.toUIColor(), // 使用 newSettings.textColor
            .paragraphStyle: paragraphStyle
        ]
        self.fullAttributedContent = NSAttributedString(string: rawContent, attributes: attributes)
        await paginateContent()
    }
    
    /// 使用 CoreText 进行精确分页
    /// 此方法会根据当前视图尺寸和内容进行分页处理
    @MainActor
    private func paginateContent() async {
        // 检查视图尺寸是否有效
        let viewSizeIsValid = currentViewSize.width > 0 && currentViewSize.height > 0
        let usingDefaultSize = !viewSizeIsValid && currentViewSize == ReaderViewModel.defaultViewSize
        
        guard viewSizeIsValid || usingDefaultSize else {
            await resetPaginationState(withError: "等待有效的视图尺寸")
            return
        }

        guard let contentToPaginate = fullAttributedContent, contentToPaginate.length > 0 else {
            await resetPaginationState()
            return
        }
        
        // 如果我们到达这里，viewSize有效且内容存在，因此继续分页。
        self.needsPagination = false // 重置标志，因为我们现在正在分页。
        // self.errorMessage = nil // 清除之前的等待提示（如果设置了）
        // print("ReaderViewModel.paginateContent: Proceeding with pagination. currentViewSize=\(currentViewSize)") // 优化：此日志信息与起始日志重复
        
        // 创建新的富文本内容，应用默认样式
        let mutableAttributedContent = NSMutableAttributedString(string: contentToPaginate.string)
        let fullRange = NSRange(location: 0, length: mutableAttributedContent.length)

        // 诊断日志：打印 settings.fontSize
        print("ReaderViewModel.paginateContent: DIAGNOSTIC - settings.fontSize = \(settings.fontSize)")
        
        // 设置默认文本属性
        var defaultAttributes: [NSAttributedString.Key: Any] = [:]
        
        #if canImport(UIKit)
        defaultAttributes[NSAttributedString.Key.foregroundColor] = settings.textColor.toUIColor() ?? UIColor.black
        defaultAttributes[NSAttributedString.Key.font] = UIFont.systemFont(ofSize: CGFloat(settings.fontSize))
        #elseif canImport(AppKit)
        defaultAttributes[NSAttributedString.Key.foregroundColor] = settings.textColor.toNSColor() ?? NSColor.black
        defaultAttributes[NSAttributedString.Key.font] = NSFont.systemFont(ofSize: CGFloat(settings.fontSize))
        #endif
        
        let paragraphStyle = NSMutableParagraphStyle()
        paragraphStyle.lineSpacing = settings.lineSpacing
        paragraphStyle.paragraphSpacing = settings.paragraphSpacing
        paragraphStyle.alignment = .justified
        paragraphStyle.lineBreakMode = .byWordWrapping
        defaultAttributes[NSAttributedString.Key.paragraphStyle] = paragraphStyle
        
        // 应用默认样式
        // print("ReaderViewModel.paginateContent: 应用默认文本样式，字体大小：\(settings.fontSize), 行间距：\(settings.lineSpacing)") // 优化：细节日志，可移除
        mutableAttributedContent.addAttributes(defaultAttributes, range: fullRange)

        // 诊断日志：打印 mutableAttributedContent 的长度和部分属性
        print("ReaderViewModel.paginateContent: DIAGNOSTIC - mutableAttributedContent.length = \(mutableAttributedContent.length)")
        if mutableAttributedContent.length > 0 {
            let attributesAtZero = mutableAttributedContent.attributes(at: 0, effectiveRange: nil)
            print("ReaderViewModel.paginateContent: DIAGNOSTIC - Attributes at index 0: \(attributesAtZero)")
            if let font = attributesAtZero[.font] as? UIFont {
                print("ReaderViewModel.paginateContent: DIAGNOSTIC - Font at index 0: \(font.fontName), size: \(font.pointSize)")
            } else {
                print("ReaderViewModel.paginateContent: DIAGNOSTIC - Font at index 0 is nil or not UIFont.")
            }
        }

        let tempTextStorage = NSTextStorage(attributedString: mutableAttributedContent) // 使用修改后的富文本
        let tempLayoutManager = NSLayoutManager()
        tempTextStorage.addLayoutManager(tempLayoutManager)
        
        var newPages: [NSAttributedString] = []
        var currentGlyphIndex = 0
        let totalGlyphs = tempLayoutManager.numberOfGlyphs
        print("ReaderViewModel.paginateContent: Calculated totalGlyphs = \(totalGlyphs)") // 新增日志

        if totalGlyphs == 0 {
            print("ReaderViewModel.paginateContent: totalGlyphs is 0. No content to paginate into pages.")
        } else {
            while currentGlyphIndex < totalGlyphs {

            let pageTextContainer = NSTextContainer(size: currentViewSize)
            pageTextContainer.lineFragmentPadding = 20 // 设置行片段内边距，增加页边距
            tempLayoutManager.addTextContainer(pageTextContainer)
            // print("ReaderViewModel.paginateContent: Inside loop. pageTextContainer.size=\(pageTextContainer.size), lineFragmentPadding=\(pageTextContainer.lineFragmentPadding), layoutManager.textContainers.count=\(tempLayoutManager.textContainers.count)") // 优化：循环内部的详细日志，可移除
            
            let glyphRangeInContainer = tempLayoutManager.glyphRange(for: pageTextContainer)
            
            if glyphRangeInContainer.length == 0 {
                print("ReaderViewModel.paginateContent: Warning - glyphRangeInContainer.length is 0. Breaking pagination loop. currentGlyphIndex=\(currentGlyphIndex)/\(totalGlyphs)")
                tempLayoutManager.removeTextContainer(at: tempLayoutManager.textContainers.count - 1) // Clean up the last added container
                break
            }
            
            let characterRange = tempLayoutManager.characterRange(forGlyphRange: glyphRangeInContainer, actualGlyphRange: nil)
            let pageText = contentToPaginate.attributedSubstring(from: characterRange)
            // print("ReaderViewModel.paginateContent: Appending page. characterRange=\(characterRange), pageText.length=\(pageText.length)") // 优化：循环内部的详细日志，可移除
            newPages.append(pageText)
            
            currentGlyphIndex = NSMaxRange(glyphRangeInContainer)
            // 不要在循环内移除文本容器，这会导致每次都从头分页
            // 我们将在循环结束后统一清理所有添加的文本容器
            }
        } // 对应 if totalGlyphs == 0 的 else 结束

        // 清理所有添加的文本容器
        while tempLayoutManager.textContainers.count > 0 {
            tempLayoutManager.removeTextContainer(at: 0)
        }
        
        print("ReaderViewModel.paginateContent: Finished. Paginated into \(newPages.count) pages.")
        self.pageContent = newPages
        self.totalPages = newPages.count
        self.currentPage = min(max(0, self.currentPage), self.totalPages > 0 ? self.totalPages - 1 : 0)
        
        // print("ReaderViewModel.paginateContent: Just before final UI update. self.fullAttributedContent?.length = \(self.fullAttributedContent?.length ?? -999)") // 优化：细节日志，可移除
        // 确保更新 UI
        updateVisibleContent()
        self.updateReadingProgress()
        
        // 如果使用了默认尺寸，标记需要在真实尺寸可用时重新分页
        if usingDefaultSize {
            print("ReaderViewModel.paginateContent: Used default size for pagination. Setting needsPagination=true for later repagination with actual size.")
            self.needsPagination = true
        }
    }
    
    /// 更新当前可见内容
    /// 根据当前页码和分页内容更新显示的文本
    @MainActor
    private func updateVisibleContent() {
        guard !pageContent.isEmpty && currentPage >= 0 && currentPage < totalPages else {
            visibleContent = createErrorAttributedString("暂无内容")
            return
        }
        
        let currentPageContent = pageContent[currentPage]
        guard currentPageContent.length > 0 else {
            visibleContent = createErrorAttributedString("当前页面为空")
            return
        }
        
        let mutableContent = NSMutableAttributedString(attributedString: currentPageContent)
        let fullRange = NSRange(location: 0, length: mutableContent.length)
        
        // 设置默认文本属性
        var defaultAttributes: [NSAttributedString.Key: Any] = [:]
        
        #if canImport(UIKit)
        defaultAttributes[NSAttributedString.Key.foregroundColor] = settings.textColor.toUIColor()
        defaultAttributes[NSAttributedString.Key.font] = UIFont.systemFont(ofSize: CGFloat(settings.fontSize))
        #elseif canImport(AppKit)
        defaultAttributes[NSAttributedString.Key.foregroundColor] = settings.textColor.toNSColor() ?? NSColor.black
        defaultAttributes[NSAttributedString.Key.font] = NSFont.systemFont(ofSize: CGFloat(settings.fontSize))
        #endif
        
        let paragraphStyle = NSMutableParagraphStyle()
        paragraphStyle.lineSpacing = settings.lineSpacing
        paragraphStyle.paragraphSpacing = settings.paragraphSpacing
        paragraphStyle.alignment = .justified
        paragraphStyle.lineBreakMode = .byWordWrapping
        defaultAttributes[NSAttributedString.Key.paragraphStyle] = paragraphStyle
        defaultAttributes[NSAttributedString.Key.kern] = 0.5 // 添加字间距
        
        // 应用默认样式
        print("ReaderViewModel.updateVisibleContent: 应用默认文本样式")
        mutableContent.addAttributes(defaultAttributes, range: fullRange)
                visibleContent = mutableContent
                
                // 打印内容预览
                let previewRange = NSRange(location: 0, length: min(30, mutableContent.length))
                let contentPreview = mutableContent.attributedSubstring(from: previewRange).string
                print("ReaderViewModel.updateVisibleContent: 内容预览: \(contentPreview)...")
                print("ReaderViewModel.updateVisibleContent: 显示第 \(currentPage + 1) 页，共 \(totalPages) 页，内容长度: \(visibleContent?.length ?? 0)")
                visibleContent = mutableContent
                
        // 最终检查确保visibleContent不为nil
        if visibleContent == nil {
            #if canImport(UIKit)
            visibleContent = NSAttributedString(string: "无法显示内容，请重试。", attributes: [.foregroundColor: UIColor.black])
            #elseif canImport(AppKit)
            visibleContent = NSAttributedString(string: "无法显示内容，请重试。", attributes: [.foregroundColor: NSColor.black])
            #endif
        }
    }
} // 结束 ReaderViewModel 类

