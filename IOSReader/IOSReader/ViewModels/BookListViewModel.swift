import SwiftUI
import Combine

@MainActor
class BookListViewModel: ObservableObject, @unchecked Sendable {
    @Published var books: [Book] = []
    @Published var isLoading = false
    @Published var error: Error?
    @Published var availableCategories: [String] = [] // 新增：从BookSourceManager获取的可用分类
    @Published var selectedCategory: String? = nil // 新增：当前选定的分类
    @Published var searchText: String = "" // 新增：搜索文本
    
    private var currentTask: Task<Void, Never>?
    private var currentPage = 1
    private var hasMoreData = true
    private var sourceManager: BookSourceManager
    private var cancellables = Set<AnyCancellable>() // 新增：用于管理订阅
    
    init(sourceManager: BookSourceManager) {
        self.sourceManager = sourceManager

        // 订阅 BookSourceManager 的当前书源分类
        sourceManager.$currentSourceCategories
            .receive(on: DispatchQueue.main)
            .sink { [weak self] categories in
                self?.availableCategories = categories // 更新本地的分类列表以供Picker使用
                // 如果之前没有选定分类，或者选定的分类不在新的列表中，则尝试选择第一个，或清空
                if let currentSelected = self?.selectedCategory, !categories.contains(currentSelected) {
                    self?.selectedCategory = categories.first
                } else if self?.selectedCategory == nil {
                    self?.selectedCategory = categories.first
                }
                // 当currentSourceCategories变化时，意味着书源已切换或书源的分类信息已更新
                // 此时应该刷新书籍列表
                self?.refresh()
            }
            .store(in: &cancellables)
        
        // 订阅搜索文本变化
        $searchText
            .debounce(for: .milliseconds(500), scheduler: DispatchQueue.main)
            .removeDuplicates()
            .sink { [weak self] _ in
                self?.refresh()
            }
            .store(in: &cancellables)

        // 订阅 BookSourceManager 的当前书源变化
        // 当 currentSource 变化时，currentSourceCategories 会自动更新 (在 BookSourceManager 中处理)
        // 上面对 $currentSourceCategories 的订阅会处理分类变化并刷新列表
        // 因此，这里不再需要直接订阅 $currentSource 来刷新，避免重复刷新
        // 保留此订阅块的注释，以备将来可能需要对 currentSource 的其他特定响应
        /*
        sourceManager.$currentSource
            .receive(on: DispatchQueue.main)
            .sink { [weak self] _ in
                // self?.refresh() // 已由 $currentSourceCategories 的订阅处理
            }
            .store(in: &cancellables)
        */
        
        // 初始加载一次分类和书籍
        // 初始化时，从 BookSourceManager 获取当前书源的分类
        self.availableCategories = sourceManager.currentSourceCategories
        self.selectedCategory = sourceManager.currentSourceCategories.first
        loadBooks() // ViewModel 初始化时加载一次书籍
    }
    
    func refresh() {
        currentPage = 1
        hasMoreData = true
        loadBooks()
    }
    
    func loadMore() {
        guard !isLoading && hasMoreData else { return }
        currentPage += 1
        loadBooks()
    }
    
    func loadBooks() { // 改为 internal 访问级别
        guard !isLoading else { return }
        isLoading = true
        
        // 取消之前的任务
        currentTask?.cancel()
        
        currentTask = Task { @MainActor in
            do {
                // 获取当前选中的书源
                guard let source = sourceManager.currentSource else {
                    throw NSError(domain: "BookListViewModel", code: -1, userInfo: [NSLocalizedDescriptionKey: "未选择书源"])
                }
                
                // 构建请求
                let request = try await sourceManager.buildRequest(
                    source: source,
                    page: currentPage,
                    category: selectedCategory,
                    keyword: searchText.isEmpty ? nil : searchText
                )

                
                // 发送请求获取数据
                let (data, _) = try await URLSession.shared.data(for: request)
                
                // 解析数据
                let newBooks = try await sourceManager.parseBookList(data: data, source: source)
                
                // 更新数据
                if currentPage == 1 {
                    books = newBooks
                } else {
                    books.append(contentsOf: newBooks)
                }
                
                // 判断是否还有更多数据
                hasMoreData = !newBooks.isEmpty
                
            } catch {
                self.error = error
            }
            
            isLoading = false
        }
    }
    
    func cancelLoading() {
        currentTask?.cancel()
        currentTask = nil
        isLoading = false
    }

    // 切换书籍的书架状态
    func toggleShelfStatus(for book: Book) {
        // 在 books 数组中找到对应的书籍并更新其状态
        if let index = books.firstIndex(where: { $0.id == book.id }) {
            books[index].isInShelf.toggle()
            // 手动触发UI更新，因为Book是类，其内部属性变化不会自动触发View更新
            // 对于SwiftData @Model对象，其属性变化应该会自动更新视图，
            // 但如果Book不是@Model或者在某些情况下更新不及时，可以显式通知
            objectWillChange.send()
            
            // TODO: 如果需要持久化存储书架状态（例如使用SwiftData或UserDefaults），在此处添加相应逻辑
            print("Toggled shelf status for \(book.title) to \(books[index].isInShelf)")
        } else {
            print("Book not found in the list: \(book.title)")
        }
    }
}