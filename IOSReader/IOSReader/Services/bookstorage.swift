import Foundation
import UIKit

/// 书籍存储错误类型
enum BookStorageError: LocalizedError {
    case documentsDirectoryNotFound
    case fileNotFound
    case fileOperationFailed(String)
    
    var errorDescription: String? {
        switch self {
        case .documentsDirectoryNotFound:
            return "无法访问文档目录"
        case .fileNotFound:
            return "文件不存在"
        case .fileOperationFailed(let message):
            return "文件操作失败: \(message)"
        }
    }
}

/// 书籍存储管理器
/// 负责管理书籍文件的存储、复制和下载
/// 所有书籍文件将统一存储在应用的私有目录中
actor BookStorage: Sendable {
    // MARK: - 单例实例
    private static var _shared: BookStorage? // 存储单例实例

    // 提供一个异步方法来获取单例实例
    static func shared() async throws -> BookStorage {
        if let existingShared = _shared {
            return existingShared
        }
        let newShared = try await BookStorage()
        _shared = newShared
        return newShared
    }
    
    // MARK: - 私有属性
    private let fileManager = FileManager.default
    
    // 书籍存储目录
    private let booksDirectory: URL
    
    // MARK: - 初始化
    private init() async throws {
        // 创建书籍存储目录（在Documents/Books目录下）
        guard let documentsDirectory = fileManager.urls(for: .documentDirectory, in: .userDomainMask).first else {
            throw BookStorageError.documentsDirectoryNotFound
        }
        booksDirectory = documentsDirectory.appendingPathComponent("Books", isDirectory: true)
        
        // 确保目录存在
        try createDirectoryIfNeeded(at: booksDirectory)
    }
    
    // MARK: - 公共方法
    
    /// 获取书籍存储目录
    func getBooksDirectory() -> URL {
        return booksDirectory
    }
    
    /// 获取书籍在应用内的相对路径
    func getRelativePath(for url: URL) -> String {
        // 如果已经是应用内路径，直接返回相对路径
        if url.path.contains(booksDirectory.path) {
            return url.lastPathComponent
        }
        return url.lastPathComponent
    }
    
    /// 根据相对路径获取完整的文件URL
    func getFullPath(for relativePath: String) -> URL {
        return booksDirectory.appendingPathComponent(relativePath)
    }
    
    /// 导入本地书籍文件到应用私有目录
    /// - Parameter url: 原始文件URL
    /// - Returns: 导入后的文件URL和相对路径
    func importLocalBook(from url: URL) async throws -> (fileURL: URL, relativePath: String) {
        // 生成目标文件名（使用原始文件名）
        let fileName = url.lastPathComponent
        let destinationURL = booksDirectory.appendingPathComponent(fileName)
        
        // 如果目标文件已存在，先删除
        if fileManager.fileExists(atPath: destinationURL.path) {
            try fileManager.removeItem(at: destinationURL)
        }
        
        // 复制文件到应用私有目录
        try fileManager.copyItem(at: url, to: destinationURL)
        
        // 返回导入后的文件URL和相对路径
        return (destinationURL, fileName)
    }
    
    /// 下载网络书籍到应用私有目录
    /// - Parameters:
    ///   - url: 网络书籍URL
    ///   - fileName: 保存的文件名（如果为nil，则使用URL的最后一部分）
    ///   - completion: 完成回调，返回下载后的文件URL和相对路径
    func downloadNetworkBook(from url: URL, fileName: String? = nil) async throws -> (URL, String) {
        // 生成目标文件名
        let targetFileName = fileName ?? url.lastPathComponent
        let destinationURL = booksDirectory.appendingPathComponent(targetFileName)
        
        // 使用 async/await 下载文件
        let (tempFileURL, _) = try await URLSession.shared.download(from: url)
        
        // 如果目标文件已存在，先删除
        if fileManager.fileExists(atPath: destinationURL.path) {
            try fileManager.removeItem(at: destinationURL)
        }
        
        // 将临时文件移动到目标位置
        try fileManager.moveItem(at: tempFileURL, to: destinationURL)
        
        return (destinationURL, targetFileName)
    }
    
    /// 删除书籍文件
    /// - Parameter relativePath: 书籍的相对路径
    /// - Returns: 是否删除成功
    func deleteBook(relativePath: String) async throws {
        let fileURL = booksDirectory.appendingPathComponent(relativePath)
        
        if fileManager.fileExists(atPath: fileURL.path) {
            try fileManager.removeItem(at: fileURL)
        }
    }
    
    /// 检查书籍文件是否存在
    /// - Parameter relativePath: 书籍的相对路径
    /// - Returns: 文件是否存在
    func bookExists(relativePath: String) -> Bool {
        let fileURL = booksDirectory.appendingPathComponent(relativePath)
        return fileManager.fileExists(atPath: fileURL.path)
    }
    
    // MARK: - 私有方法
    
    /// 创建目录（如果不存在）
    private func createDirectoryIfNeeded(at url: URL) throws {
        if !fileManager.fileExists(atPath: url.path) {
            try fileManager.createDirectory(at: url, withIntermediateDirectories: true, attributes: nil)
        }
    }
}