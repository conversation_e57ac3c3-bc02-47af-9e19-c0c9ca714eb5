import Foundation
import SwiftData

/// 章节存储相关错误
enum ChapterError: LocalizedError {
    case fileNotFound
    case decodingFailed
    case encodingFailed
    case savingFailed
    case deletionFailed
    
    var errorDescription: String? {
        switch self {
        case .fileNotFound:
            return "未找到章节数据文件"
        case .decodingFailed:
            return "章节数据解码失败"
        case .encodingFailed:
            return "章节数据编码失败"
        case .savingFailed:
            return "保存章节数据失败"
        case .deletionFailed:
            return "删除章节数据失败"
        }
    }
}

// 持久化管理器
public actor ChapterStorage: Sendable {
    public static let shared: ChapterStorage = ChapterStorage()
    private let fileManager = FileManager.default
    private let chaptersDirectoryName = "BookChapters"

    private init() {
        // 初始化时不需要立即创建目录，将在首次使用时创建
    }

    /// 获取存储特定书籍章节信息的JSON文件路径
    /// - Parameter bookId: 书籍的唯一标识符
    /// - Returns: 对应的JSON文件URL
    /// - Throws: 如果无法获取文档目录则抛出错误
    private func getChaptersFileURL(for bookId: UUID) throws -> URL {
        guard let documentsDirectory = fileManager.urls(for: .documentDirectory, in: .userDomainMask).first else {
            throw ChapterError.savingFailed
        }
        let chaptersDirectory = documentsDirectory.appendingPathComponent(chaptersDirectoryName, isDirectory: true)
        try createChaptersDirectoryIfNeeded()
        return chaptersDirectory.appendingPathComponent("\(bookId.uuidString).json")
    }

    /// 如果章节存储目录不存在，则创建它
    /// - Throws: 如果无法创建目录则抛出错误
    private func createChaptersDirectoryIfNeeded() throws {
        guard let documentsDirectory = fileManager.urls(for: .documentDirectory, in: .userDomainMask).first else {
            throw ChapterError.savingFailed
        }
        let chaptersDirectoryURL = documentsDirectory.appendingPathComponent(chaptersDirectoryName)
        if !fileManager.fileExists(atPath: chaptersDirectoryURL.path) {
            try fileManager.createDirectory(at: chaptersDirectoryURL, withIntermediateDirectories: true, attributes: nil)
            print("章节存储目录已创建: \(chaptersDirectoryURL.path)")
        }
    }

    /// 保存指定书籍的章节列表到文件
    /// - Parameters:
    ///   - chapters: 要保存的章节数组
    ///   - bookId: 书籍的唯一标识符
    /// - Throws: 保存过程中的错误
    public func saveChapters(_ chapters: [Chapter], for bookId: UUID) async throws {
        let chapterDataArray = await MainActor.run {
            chapters.map { $0.toChapterData() }
        }
        try await saveChapterData(chapterDataArray, for: bookId)
    }
    
    /// 保存指定书籍的章节数据到文件
    /// - Parameters:
    ///   - chapterData: 要保存的章节数据数组
    ///   - bookId: 书籍的唯一标识符
    /// - Throws: 保存过程中的错误
    private func saveChapterData(_ chapterData: [ChapterData], for bookId: UUID) async throws {
        let fileURL = try getChaptersFileURL(for: bookId)
        let encoder = JSONEncoder()
        encoder.outputFormatting = .prettyPrinted // 可选，使JSON更易读

        do {
            let data = try encoder.encode(chapterData)
            try data.write(to: fileURL, options: .atomic)
            print("章节数据已保存到: \(fileURL.path)")
        } catch {
            throw ChapterError.savingFailed
        }
    }

    /// 从文件加载指定书籍的章节列表
    /// - Parameter bookId: 书籍的唯一标识符
    /// - Returns: 加载到的章节数组
    /// - Throws: 加载过程中的错误
    public func loadChapters(for bookId: UUID) async throws -> [Chapter] {
        let fileURL = try getChaptersFileURL(for: bookId)
        guard fileManager.fileExists(atPath: fileURL.path) else {
            throw ChapterError.fileNotFound
        }

        do {
            let data = try Data(contentsOf: fileURL)
            let decoder = JSONDecoder()
            let chapterDataArray: [ChapterData] = try decoder.decode([ChapterData].self, from: data)
            // 在主线程上创建Chapter对象
            return await MainActor.run {
                let chapters = chapterDataArray.map { data in
                    Chapter(from: data)
                }
                print("从 \(fileURL.path) 加载了 \(chapters.count) 个章节 for bookId \(bookId.uuidString)")
                return chapters
            }
        } catch {
            throw ChapterError.decodingFailed
        }
    }

    /// 删除指定书籍的章节数据文件
    /// - Parameter bookId: 书籍的唯一标识符
    /// - Throws: 删除过程中的错误
    func deleteChapters(for bookId: UUID) async throws {
        let fileURL = try getChaptersFileURL(for: bookId)
        if fileManager.fileExists(atPath: fileURL.path) {
            do {
                try fileManager.removeItem(at: fileURL)
                print("已删除书籍 \(bookId.uuidString) 的章节数据: \(fileURL.path)")
            } catch {
                throw ChapterError.deletionFailed
            }
        }
    }
    
    /// 检查指定书籍是否存在章节缓存
    /// - Parameter bookId: 书籍的唯一标识符
    /// - Returns: 如果存在章节缓存则返回true，否则返回false
    public func hasChapters(for bookId: UUID) throws -> Bool {
        let fileURL = try getChaptersFileURL(for: bookId)
        return fileManager.fileExists(atPath: fileURL.path)
    }
}