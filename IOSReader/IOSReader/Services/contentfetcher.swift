/// IOSReader项目的核心内容获取模块
/// 主要功能：
/// 1. 智能发现下一页链接：通过正则表达式高效匹配网页中的下一页链接
/// 2. 章节缓存系统：异步缓存章节内容到本地，优化阅读体验
/// 3. EPUB/TXT文件解析引擎：支持多种电子书格式解析，包含EPUB解压和XML解析
/// 
/// 性能优化点：
/// - 使用预编译正则表达式提高匹配效率
/// - 异步文件操作避免阻塞主线程
/// - 内存高效处理大型文本文件

import Foundation
import SwiftUI
import ZIPFoundation
// 导入自定义HTML解析工具，替代Kanna

// 添加缺失的编码支持
extension String.Encoding {
    static let gb_18030_2000 = String.Encoding(rawValue: 0x80000632)
    static let big5 = String.Encoding(rawValue: 0x80000A03)
}

class ContentFetcher {
    // 文档解析器
    private let pdfParser = PDFParser()
    private lazy var epubParser: EPUBParser = {
        EPUBParser()
    }()
    // private let mobiParser = MOBIParser() // MOBI 已移除
    
    init() {
    }
    
    /// 解析文档内容
    /// - Parameter path: 文档路径
    /// - Returns: 解析后的文本内容
    func parseDocument(at path: URL) async throws -> String {
        guard let format = BookFormat.detect(from: path) else {
            throw NSError(domain: "ContentFetcher", code: -1, userInfo: [NSLocalizedDescriptionKey: "不支持的文件格式"])
        }
        
        switch format {
        case .txt:
            // 直接读取文本文件
            return try String(contentsOf: path, encoding: .utf8)
        case .html:
            // 解析HTML文件
            let htmlContent = try String(contentsOf: path, encoding: .utf8)
            return extractTextFromHTML(htmlContent) ?? ""
        case .pdf:
            return try await pdfParser.parse(url: path)
        case .epub:
            return self.parseEPUB(at: path.path) ?? ""
        // case .mobi: // MOBI 已移除
            // MOBI解析逻辑已移除，可以抛出错误或返回空字符串
            // throw NSError(domain: "ContentFetcher", code: -1, userInfo: [NSLocalizedDescriptionKey: "MOBI format is no longer supported"])
        }
    }
    
    /// 获取文档页数
    /// - Parameter path: 文档路径
    /// - Returns: 总页数
    func getDocumentPageCount(at path: URL) throws -> Int {
        guard let format = BookFormat.detect(from: path) else {
            throw NSError(domain: "ContentFetcher", code: -1, userInfo: [NSLocalizedDescriptionKey: "不支持的文件格式"])
        }
        
        switch format {
        case .txt:
            // 文本文件需要先读取内容再计算页数
            let content = try String(contentsOf: path, encoding: .utf8)
            return content.components(separatedBy: .newlines).count
        case .html:
            // HTML文件需要先提取文本再计算页数
            let htmlContent = try String(contentsOf: path, encoding: .utf8)
            let textContent = extractTextFromHTML(htmlContent) ?? ""
            return textContent.components(separatedBy: .newlines).count
        case .pdf:
            return try pdfParser.getPageCount(url: path)
        case .epub:
            if let content = self.parseEPUB(at: path.path) {
                return content.components(separatedBy: .newlines).count
            }
            return 0
        // case .mobi: // MOBI 已移除
            // MOBI解析逻辑已移除
            // return 0
        }
    }
    
    /// 获取指定页面的内容
    /// - Parameters:
    ///   - path: 文档路径
    ///   - pageIndex: 页码（从0开始）
    /// - Returns: 页面内容
    func getPageContent(at path: URL, pageIndex: Int) throws -> String {
        guard let format = BookFormat.detect(from: path) else {
            throw NSError(domain: "ContentFetcher", code: -1, userInfo: [NSLocalizedDescriptionKey: "不支持的文件格式"])
        }
        
        switch format {
        case .txt:
            // 文本文件需要先读取内容再获取指定页
            let content = try String(contentsOf: path, encoding: .utf8)
            let lines = content.components(separatedBy: .newlines)
            guard pageIndex >= 0 && pageIndex < lines.count else {
                throw NSError(domain: "ContentFetcher", code: -1, userInfo: [NSLocalizedDescriptionKey: "无效的页码"])
            }
            return lines[pageIndex]
        case .html:
            // HTML文件需要先提取文本再获取指定页
            let htmlContent = try String(contentsOf: path, encoding: .utf8)
            let textContent = extractTextFromHTML(htmlContent) ?? ""
            let lines = textContent.components(separatedBy: .newlines)
            guard pageIndex >= 0 && pageIndex < lines.count else {
                throw NSError(domain: "ContentFetcher", code: -1, userInfo: [NSLocalizedDescriptionKey: "无效的页码"])
            }
            return lines[pageIndex]
        case .pdf:
            return try pdfParser.getPageContent(url: path, pageIndex: pageIndex)
        case .epub:
            if let content = self.parseEPUB(at: path.path) {
                let lines = content.components(separatedBy: .newlines)
                guard pageIndex >= 0 && pageIndex < lines.count else {
                    throw NSError(domain: "ContentFetcher", code: -1, userInfo: [NSLocalizedDescriptionKey: "无效的页码"])
                }
                return lines[pageIndex]
            }
            return ""
        // case .mobi: // MOBI 已移除
            // MOBI解析逻辑已移除
            // return ""
        }
    }
    
    // 智能发现算法
    func discoverNextPageLink(from html: String) -> String? {
        // 使用预编译正则表达式提高效率
        let patterns = [
            "<a[^>]*href=\"([^\"]*)\"[^>]*>(?:下一页|下页|下一章|>\\\\s*\\u{203a}\\\\s*<|>\\\\s*\\u{00bb}\\\\s*<)|<a[^>]*href='([^']*)'[^>]*>(?:下一页|下页|下一章|>\\\\s*\\u{203a}\\\\s*<|>\\\\s*\\u{00bb}\\\\s*<)",
            "<a[^>]*rel=\"next\"[^>]*href=\"([^\"]*)\"[^>]*>|<a[^>]*rel='next'[^>]*href='([^']*)'[^>]*>",
            "<link[^>]*rel=\"next\"[^>]*href=\"([^\"]*)\"[^>]*>|<link[^>]*rel='next'[^>]*href='([^']*)'[^>]*>"
        ].compactMap { pattern in
            do {
                return try NSRegularExpression(pattern: pattern, options: [.caseInsensitive, .dotMatchesLineSeparators])
            } catch {
                print("正则表达式编译失败: \(pattern), 错误: \(error.localizedDescription)")
                return nil
            }
        }
        
        let range = NSRange(location: 0, length: html.utf16.count)
        
        for regex in patterns {
            if let match = regex.firstMatch(in: html, options: [], range: range),
               let matchRange = Range(match.range(at: 1), in: html) ?? Range(match.range(at: 2), in: html) {
                return String(html[matchRange])
            }
        }
        
        return nil
    }
    
    // HTML文本提取
    func extractTextFromHTML(_ html: String) -> String? {
        // 使用HTMLParserUtils替代Kanna
        // 移除HTML标签
        var text = html.replacingOccurrences(of: "<[^>]+>", with: "", options: .regularExpression)
        
        // 移除多余的空白字符
        text = text.replacingOccurrences(of: "\\\\s+", with: " ", options: .regularExpression)
        
        // 解码HTML实体
        text = HTMLParserUtils.decodeHTMLEntities(text)
        
        // 移除空行
        text = text.components(separatedBy: .newlines)
            .filter { !$0.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty }
            .joined(separator: "\n")
        
        return text.trimmingCharacters(in: .whitespacesAndNewlines)
    }
    
    // EPUB解析引擎
    func parseEPUB(at path: String) -> String? {
        // 检查文件路径是否为空
        guard !path.isEmpty else {
            print("EPUB解析错误: 文件路径为空")
            return nil
        }
        
        // 使用fileURLWithPath创建文件URL，以正确处理本地文件路径（包括中文字符）
        let epubURL = URL(fileURLWithPath: path)
        
        // 检查文件是否存在
        guard FileManager.default.fileExists(atPath: path) else {
            let error = NSError(domain: "ContentFetcher", code: -1, 
                              userInfo: [NSLocalizedDescriptionKey: "无效的EPUB文件路径或文件不存在: \(path)"])
            print("EPUB解析错误: \(error.localizedDescription)")
            return nil
        }
        
        // 创建临时目录用于解压EPUB文件
        let tempDirURL = FileManager.default.temporaryDirectory.appendingPathComponent(UUID().uuidString)
        
        do {
            // 创建临时目录
            try FileManager.default.createDirectory(at: tempDirURL, withIntermediateDirectories: true)
            
            // 使用ZIPFoundation解压EPUB文件
            let archive: Archive
            do {
                archive = try Archive(url: epubURL, accessMode: .read)
            } catch {
                print("无法打开EPUB文件进行元数据提取: \(epubURL.path), error: \(error)")
                return nil
            }
            
            // 解压文件
            try archive.forEach { entry in
                let entryURL = tempDirURL.appendingPathComponent(entry.path)
                
                // 创建必要的目录结构
                if entry.type == .directory {
                    try FileManager.default.createDirectory(at: entryURL, withIntermediateDirectories: true)
                } else {
                    // 确保父目录存在
                    try FileManager.default.createDirectory(at: entryURL.deletingLastPathComponent(), 
                                                         withIntermediateDirectories: true)
                    
                    // 提取文件内容
                    _ = try archive.extract(entry, to: entryURL)
                }
            }
            
            // 查找OPF文件（包含电子书元数据和内容引用）
            let opfURL = try findOPFFile(in: tempDirURL)
            
            // 解析OPF文件，提取内容文件路径
            let contentFiles = try parseOPFFile(at: opfURL)
            
            // 合并所有内容文件
            var fullContent = ""
            for contentFile in contentFiles {
                let contentURL = opfURL.deletingLastPathComponent().appendingPathComponent(contentFile)
                if let htmlContent = try? String(contentsOf: contentURL, encoding: .utf8) {
                    // 使用HTML解析方法清理内容
                    if let cleanContent = parseHTMLContent(htmlContent) {
                        fullContent += cleanContent + "\n\n"
                    }
                }
            }
            
            // 清理临时目录
            try? FileManager.default.removeItem(at: tempDirURL)
            
            return fullContent.isEmpty ? nil : fullContent
            
        } catch {
            print("EPUB解析错误: \(error.localizedDescription)")
            // 确保清理临时目录
            try? FileManager.default.removeItem(at: tempDirURL)
            return nil
        }
    }
    
    // 查找OPF文件
    private func findOPFFile(in directoryURL: URL) throws -> URL {
        // 首先查找container.xml，它指向OPF文件
        let containerURL = directoryURL.appendingPathComponent("META-INF/container.xml")
        
        guard FileManager.default.fileExists(atPath: containerURL.path) else {
            throw NSError(domain: "ContentFetcher", code: -1, 
                         userInfo: [NSLocalizedDescriptionKey: "EPUB格式错误: 找不到container.xml"])
        }
        
        // 解析container.xml以找到OPF文件路径
        let containerXML = try String(contentsOf: containerURL, encoding: .utf8)
        
        // 使用正则表达式提取OPF文件路径
        let pattern = "full-path=['\"]([^'\"]*?)['\"]" 
        do {
            let regex = try NSRegularExpression(pattern: pattern, options: [])
            let range = NSRange(location: 0, length: containerXML.utf16.count)
            
            if let match = regex.firstMatch(in: containerXML, options: [], range: range),
               let matchRange = Range(match.range(at: 1), in: containerXML) {
                let opfPath = String(containerXML[matchRange])
                return directoryURL.appendingPathComponent(opfPath)
            } else {
                throw NSError(domain: "ContentFetcher", code: -1, 
                             userInfo: [NSLocalizedDescriptionKey: "EPUB格式错误: 无法在container.xml中找到OPF文件路径"])
            }
        } catch {
            throw NSError(domain: "ContentFetcher", code: -1, 
                         userInfo: [NSLocalizedDescriptionKey: "EPUB解析错误: \(error.localizedDescription)"])
        }
    }
    
    // 解析OPF文件，提取内容文件路径
    private func parseOPFFile(at opfURL: URL) throws -> [String] {
        let opfXML = try String(contentsOf: opfURL, encoding: .utf8)
        var contentFiles: [String] = []
        
        // 使用正则表达式提取内容文件路径
        // 查找spine中的itemref，然后通过idref找到manifest中对应的item
        let itemPattern = "<item[^>]*id=['\"]([^'\"]*?)['\"][^>]*href=['\"]([^'\"]*?)['\"][^>]*"
        let itemrefPattern = "<itemref[^>]*idref=['\"]([^'\"]*?)['\"][^>]*"
        
        do {
            // 提取所有item
            let itemRegex = try NSRegularExpression(pattern: itemPattern, options: [])
            let itemRange = NSRange(location: 0, length: opfXML.utf16.count)
            var itemMap: [String: String] = [:] // id -> href
            
            itemRegex.enumerateMatches(in: opfXML, options: [], range: itemRange) { match, _, _ in
                guard let match = match,
                      let idRange = Range(match.range(at: 1), in: opfXML),
                      let hrefRange = Range(match.range(at: 2), in: opfXML) else { return }
                
                let id = String(opfXML[idRange])
                let href = String(opfXML[hrefRange])
                itemMap[id] = href
            }
            
            // 提取spine中的itemref顺序
            let itemrefRegex = try NSRegularExpression(pattern: itemrefPattern, options: [])
            let itemrefRange = NSRange(location: 0, length: opfXML.utf16.count)
            
            itemrefRegex.enumerateMatches(in: opfXML, options: [], range: itemrefRange) { match, _, _ in
                guard let match = match,
                      let idrefRange = Range(match.range(at: 1), in: opfXML) else { return }
                
                let idref = String(opfXML[idrefRange])
                if let href = itemMap[idref] {
                    contentFiles.append(href)
                }
            }
            
            return contentFiles
            
        } catch {
            throw NSError(domain: "ContentFetcher", code: -1, 
                         userInfo: [NSLocalizedDescriptionKey: "OPF解析错误: \(error.localizedDescription)"])
        }
    }
    
    // 解析HTML内容
    private func parseHTMLContent(_ html: String) -> String? {
        // 使用正则表达式去除HTML标签
        let patterns = [
            "<[^>]+>", // 基本HTML标签
            "\\s{2,}", // 多个连续空格
            "\\n{3,}"  // 多个连续换行
        ]
        
        var cleanText = html
        
        for pattern in patterns {
            do {
                let regex = try NSRegularExpression(pattern: pattern, options: [.caseInsensitive])
                let range = NSRange(location: 0, length: cleanText.utf16.count)
                cleanText = regex.stringByReplacingMatches(in: cleanText, options: [], range: range, withTemplate: pattern == "<[^>]+>" ? "" : " ")
            } catch {
                print("HTML内容解析警告: \(error.localizedDescription)")
            }
        }
        
        return cleanText.trimmingCharacters(in: .whitespacesAndNewlines)
    }
    
    // AZW解析引擎
    func parseAZW(at path: String) -> String? {
        guard URL(string: path) != nil else { return nil }
        
        // AZW文件解析逻辑
        // 注意：实际实现需要集成Kindle相关解析库
        print("AZW解析错误: 需要集成Kindle解析库 - \(path)")
        return nil
    }
    
    // PDF解析引擎
    func parsePDF(at path: String) -> String? {
        guard URL(string: path) != nil else { return nil }
        
        // PDF文件解析逻辑
        // 注意：实际实现需要集成PDFKit或类似库
        print("PDF解析错误: 需要集成PDFKit - \(path)")
        return nil
    }
    
    // HTML解析引擎
    func parseHTML(at path: String) -> String? {
        // 检查文件路径是否为空
        guard !path.isEmpty else {
            print("HTML解析错误: 文件路径为空")
            return nil
        }
        
        // 使用fileURLWithPath创建文件URL，以正确处理本地文件路径（包括中文字符）
        let htmlURL = URL(fileURLWithPath: path)
        
        // 检查文件是否存在
        guard FileManager.default.fileExists(atPath: path) else {
            let error = NSError(domain: "ContentFetcher", code: -1, 
                              userInfo: [NSLocalizedDescriptionKey: "无效的文件路径或文件不存在: \(path)"])
            print("HTML解析错误: \(error.localizedDescription)")
            return nil
        }
        
        do {
            let htmlString = try String(contentsOf: htmlURL, encoding: .utf8)
            // 使用正则表达式去除HTML标签
            let patterns = [
                "<[^>]+>", // 基本HTML标签
                "\\s{2,}", // 多个连续空格
                "\\n{3,}"  // 多个连续换行
            ]
            
            var cleanText = htmlString
            
            for pattern in patterns {
                do {
                    let regex = try NSRegularExpression(pattern: pattern, options: [.caseInsensitive])
                    let range = NSRange(location: 0, length: cleanText.utf16.count)
                    cleanText = regex.stringByReplacingMatches(in: cleanText, options: [], range: range, withTemplate: pattern == "<[^>]+>" ? "" : " ")
                } catch {
                    let errorInfo = [
                        "Error": error.localizedDescription,
                        "Pattern": pattern,
                        "File": path
                    ]
                    print("HTML解析警告: \(errorInfo)")
                }
            }
            
            return cleanText.trimmingCharacters(in: .whitespacesAndNewlines)
        } catch {
            print("HTML解析错误: \(error.localizedDescription)")
            return nil
        }
    }
    
    // TXT解析引擎
    func parseTXT(at path: String) -> String? {
        // 检查文件路径是否为空
        guard !path.isEmpty else {
            print("TXT解析错误: 文件路径为空")
            return nil
        }
        
        // 使用fileURLWithPath创建文件URL，而不是URL(string:)，以正确处理本地文件路径（包括中文字符）
        let txtURL = URL(fileURLWithPath: path)
        
        // 检查文件是否存在
        guard FileManager.default.fileExists(atPath: path) else {
            let error = NSError(domain: "ContentFetcher", code: -1, 
                              userInfo: [NSLocalizedDescriptionKey: "无效的TXT文件路径或文件不存在: \(path)"])
            print("TXT解析错误: \(error.localizedDescription)")
            return nil
        }
        
        // 尝试不同的编码格式读取TXT文件
        let encodings: [String.Encoding] = [
            .utf8,
            .gb_18030_2000, // 中文简体
            .big5,          // 中文繁体
            .ascii,
            .unicode,
            .utf16,
            .utf16LittleEndian,
            .utf16BigEndian
        ]
        
        var fileContent: String? = nil
        var usedEncoding: String.Encoding? = nil
        
        // 尝试使用不同编码读取文件
        for encoding in encodings {
            do {
                fileContent = try String(contentsOf: txtURL, encoding: encoding)
                usedEncoding = encoding
                break
            } catch {
                continue
            }
        }
        
        // 如果所有编码都失败，尝试使用Data读取并检测编码
        if fileContent == nil {
            do {
                // 使用Data直接读取文件内容
                let data = try Data(contentsOf: txtURL)
                
                // 记录文件大小，帮助诊断
                print("成功读取文件数据，大小: \(data.count) 字节")
                
                // 检查BOM标记以确定编码
                if data.count >= 3 && data[0] == 0xEF && data[1] == 0xBB && data[2] == 0xBF {
                    // UTF-8 with BOM
                    fileContent = String(data: data.subdata(in: 3..<data.count), encoding: .utf8)
                    print("检测到UTF-8 BOM标记")
                } else if data.count >= 2 && data[0] == 0xFF && data[1] == 0xFE {
                    // UTF-16 Little Endian
                    fileContent = String(data: data.subdata(in: 2..<data.count), encoding: .utf16LittleEndian)
                    print("检测到UTF-16 Little Endian BOM标记")
                } else if data.count >= 2 && data[0] == 0xFE && data[1] == 0xFF {
                    // UTF-16 Big Endian
                    fileContent = String(data: data.subdata(in: 2..<data.count), encoding: .utf16BigEndian)
                    print("检测到UTF-16 Big Endian BOM标记")
                } else {
                    // 尝试使用系统默认编码
                    fileContent = String(data: data, encoding: .utf8)
                    print("未检测到BOM标记，尝试使用UTF-8编码")
                }
            } catch {
                print("TXT解析错误: 无法读取文件内容 - \(error.localizedDescription)")
                print("文件路径: \(txtURL.path)")
                return nil
            }
        }
        
        guard let content = fileContent else {
            print("TXT解析错误: 无法识别文件编码")
            return nil
        }
        
        // 处理文本内容，移除多余的空行和空格
        let cleanedContent = content
            .replacingOccurrences(of: "\r\n", with: "\n") // 统一换行符
            .replacingOccurrences(of: "\r", with: "\n")   // 统一换行符
            .replacingOccurrences(of: "\n{3,}", with: "\n\n", options: .regularExpression) // 移除多余空行
            .replacingOccurrences(of: "\t", with: "    ") // 替换制表符
            .trimmingCharacters(in: .whitespacesAndNewlines) // 移除首尾空白
        
        if let encoding = usedEncoding {
            print("TXT文件成功解析，使用编码: \(encoding)")
        } else {
            print("TXT文件成功解析，使用自动检测编码")
        }
        
        return cleanedContent
    }
    
    // 统一解析入口
    func parseBook(at path: String, ofType type: String) -> String? {
        switch type.lowercased() {
        case "epub": return parseEPUB(at: path)
        // case "mobi": return parseMOBI(at: path) // MOBI support removed
        case "azw": return parseAZW(at: path)
        case "pdf": return parsePDF(at: path)
        case "txt": return parseTXT(at: path)
        case "html", "htm": return parseHTML(at: path)
        default: return nil
        }
    }
}