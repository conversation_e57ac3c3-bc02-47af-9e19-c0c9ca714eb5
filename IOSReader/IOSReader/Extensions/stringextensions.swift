//
// StringExtensions.swift
// String类型的扩展方法
// 功能：
// 1. 字符串分块功能

import Foundation

extension String {
    /// 将字符串按指定大小分割成多个子字符串
    /// - Parameter size: 每个子字符串的大小
    /// - Returns: 分割后的子字符串数组
    func chunked(by size: Int) -> [String] {
        guard size > 0 else { return [self] }
        
        return stride(from: 0, to: self.count, by: size).map {
            let start = self.index(self.startIndex, offsetBy: $0)
            let end = self.index(start, offsetBy: min(size, self.count - $0))
            return String(self[start..<end])
        }
    }
}

// MARK: - HTML Entity Decoding

extension String {
    /// 将字符串中的 HTML 字符实体（例如 &amp;）解码为其对应的字符。
    /// 基于 Stack Overflow 上的解决方案：https://stackoverflow.com/a/30141700/106244
    /// 和 Gist：https://gist.github.com/mwaterfall/25b4a6a06dc3309d9555
    func decodingHTMLEntities() -> String {
        guard self.contains("&") else { return self } // 优化：如果字符串不包含 '&'，则无需解码

        var result = ""
        var position = startIndex

        // 定义 HTML 实体映射
        // 仅包含最常见的实体以简化，可以根据需要扩展
        let characterEntities: [String: Character] = [
            // XML predefined entities:
            "&quot;": "\"",
            "&amp;": "&",
            "&apos;": "'",
            "&lt;": "<",
            "&gt;": ">",
            // HTML character entity references:
            "&nbsp;": "\u{00A0}",
            "&copy;": "\u{00A9}",
            "&reg;": "\u{00AE}",
            // 添加更多需要的实体...
        ]

        // 解码数字实体（例如 &#65; 或 &#x41;）
        func decodeNumeric(_ string: String, base: Int) -> Character? {
            guard let code = UInt32(string, radix: base), let uniScalar = UnicodeScalar(code) else { return nil }
            return Character(uniScalar)
        }

        // 解码命名或数字实体
        func decodeEntity(_ entity: String) -> Character? {
            if entity.hasPrefix("&#x") || entity.hasPrefix("&#X") {
                let hexString = String(entity.dropFirst(3).dropLast())
                return decodeNumeric(hexString, base: 16)
            } else if entity.hasPrefix("&#") {
                let decimalString = String(entity.dropFirst(2).dropLast())
                return decodeNumeric(decimalString, base: 10)
            } else {
                return characterEntities[entity]
            }
        }

        // 遍历字符串查找实体
        while let ampRange = self[position..<self.endIndex].range(of: "&") {
            result.append(contentsOf: self[position..<ampRange.lowerBound])
            position = ampRange.lowerBound // Move position to the start of '&'

            // Search for ';' starting from *after* the '&'
            let searchRangeForSemi = self.index(after: position)..<self.endIndex
            if position < self.endIndex, let semiRange = self[searchRangeForSemi].range(of: ";") {
                // Extract the entity content between '&' and ';'
                let entityContentRange = self.index(after: position)..<semiRange.lowerBound
                let entityName = String(self[entityContentRange]) // e.g., "amp", "lt", "#60"

                // Reconstruct the full entity string (e.g., "&amp;", "&#60;") for decoding
                let fullEntity = "&\(entityName);"

                if let decodedChar = decodeEntity(fullEntity) {
                    result.append(decodedChar)
                    position = semiRange.upperBound // Move position past ';'
                } else {
                    // If not decodable, append the original '&' and move position past it
                    result.append("&")
                    position = self.index(after: ampRange.lowerBound) // Move position past '&'
                }
            } else {
                // No semicolon found after '&', treat '&' literally
                result.append("&")
                position = self.index(after: ampRange.lowerBound) // Move position past '&'
            }
        }

        // 添加剩余部分
        if position < self.endIndex {
            result.append(contentsOf: self[position..<self.endIndex])
        }

        return result
    }
}