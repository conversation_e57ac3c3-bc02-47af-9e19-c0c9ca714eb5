import Foundation

// MARK: - HTMLParserUtils扩展
// 添加parse方法以解析HTML内容
extension HTMLParserUtils {
    /// 解析HTML内容为XPathNode对象
    /// - Parameter html: HTML字符串
    /// - Returns: XPathNode对象，如果解析失败则返回nil
    static func parse(html: String) -> HTMLParserUtils.XPathNode? {
        // 清理HTML内容
        let cleanedHTML = html.trimmingCharacters(in: .whitespacesAndNewlines)
        
        // 检查HTML是否为空
        guard !cleanedHTML.isEmpty else {
            print("HTML内容为空")
            return nil
        }
        
        // 使用HTMLParserUtils的parseHTML方法解析HTML
        let rootNode = HTMLParserUtils.parseHTML(cleanedHTML)
        
        // 返回解析结果
        return rootNode
    }
    
    /// 为XPathNode添加xpath方法，以支持XPath查询
    static func xpath(_ node: HTMLParserUtils.XPathNode, expression: String) throws -> [HTMLParserUtils.XPathNode] {
        // 简单实现，仅支持基本的XPath表达式
        // 完整实现需要更复杂的XPath解析器
        
        // 处理直接子元素选择器
        if expression.hasPrefix("./") {
            let subPath = String(expression.dropFirst(2))
            return try findDirectChildren(node, path: subPath)
        }
        
        // 处理属性选择器
        if expression.hasPrefix("@") {
            let attrName = String(expression.dropFirst())
            if let attrValue = node.attributes[attrName] {
                let attrNode = HTMLParserUtils.XPathNode(name: attrName, attributes: [:], content: attrValue, children: [])
                return [attrNode]
            }
            return []
        }
        
        // 处理文本节点选择器
        if expression == "text()" {
            let textNode = HTMLParserUtils.XPathNode(name: "#text", attributes: [:], content: node.textContent, children: [])
            return [textNode]
        }
        
        // 处理简单的元素选择器
        return findAllDescendants(node, name: expression)
    }
    
    /// 查找直接子元素
    private static func findDirectChildren(_ node: HTMLParserUtils.XPathNode, path: String) throws -> [HTMLParserUtils.XPathNode] {
        return node.children.filter { $0.name == path }
    }
    
    /// 查找所有后代元素
    private static func findAllDescendants(_ node: HTMLParserUtils.XPathNode, name: String) -> [HTMLParserUtils.XPathNode] {
        var results: [HTMLParserUtils.XPathNode] = []
        
        // 检查当前节点
        if node.name == name {
            results.append(node)
        }
        
        // 递归检查子节点
        for child in node.children {
            results.append(contentsOf: findAllDescendants(child, name: name))
        }
        
        return results
    }
}

// 为XPathNode添加xpath方法
extension HTMLParserUtils.XPathNode {
    /// 使用XPath表达式查询节点
    /// - Parameter expression: XPath表达式
    /// - Returns: 匹配的节点数组
    func xpath(_ expression: String) throws -> [HTMLParserUtils.XPathNode] {
        return try HTMLParserUtils.xpath(self, expression: expression)
    }
    
    /// 获取节点的文本内容
    var text: String {
        return self.textContent
    }
}