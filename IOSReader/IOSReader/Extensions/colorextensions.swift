//  ColorExtensions.swift
//  IOSReader
//
//  颜色扩展工具，提供通过十六进制字符串快速创建Color对象的便捷方法，便于主题和自定义颜色管理。
//  主要扩展：
//  - Color(hex:)：支持多种格式的十六进制字符串转Color，兼容RGB/ARGB格式。
//
import SwiftUI
#if canImport(UIKit)
import UIKit
#elseif canImport(AppKit)
import AppKit
#endif

// MARK: - Color Extensions
extension Color {
    init(hex: String) {
        let hex = hex.trimmingCharacters(in: CharacterSet.alphanumerics.inverted)
        var int: UInt64 = 0
        Scanner(string: hex).scanHexInt64(&int)
        let a, r, g, b: UInt64
        switch hex.count {
        case 3: // RGB (12-bit)
            (a, r, g, b) = (255, (int >> 8) * 17, (int >> 4 & 0xF) * 17, (int & 0xF) * 17)
        case 6: // RGB (24-bit)
            (a, r, g, b) = (255, int >> 16, int >> 8 & 0xFF, int & 0xFF)
        case 8: // ARGB (32-bit)
            (a, r, g, b) = (int >> 24, int >> 16 & 0xFF, int >> 8 & 0xFF, int & 0xFF)
        default:
            (a, r, g, b) = (255, 0, 0, 0)
        }

        self.init(
            .sRGB,
            red: Double(r) / 255,
            green: Double(g) / 255,
            blue: Double(b) / 255,
            opacity: Double(a) / 255
        )
    }
    
    /// 将 SwiftUI Color 转换为平台原生颜色
    #if canImport(UIKit)
    func toUIColor() -> UIColor {
        return UIColor(self)
    }
    #elseif canImport(AppKit)
    func toNSColor() -> NSColor {
        return NSColor(self)
    }
    #endif
}