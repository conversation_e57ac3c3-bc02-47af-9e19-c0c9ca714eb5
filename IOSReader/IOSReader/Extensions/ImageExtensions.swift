import SwiftUI
import UIKit

extension Image {
    @MainActor
    func asUIImage() -> UIImage? {
        // 将 Image 包装在 UIHostingController 中以渲染为 UIImage
        // 添加 .resizable().scaledToFit() 尝试使图像尺寸行为更可预测
        let controller = UIHostingController(rootView: self.resizable().scaledToFit())
        
        guard let view = controller.view else {
            // 如果无法获取视图，则返回 nil
            return nil
        }

        let targetSize = view.intrinsicContentSize
        
        // 如果 intrinsicContentSize 是 .zero，这可能发生在某些类型的 Image (如 SF Symbols 未指定大小)
        // 在这种情况下，渲染一个零尺寸的图像没有意义，并且可能导致 UIGraphicsImageRenderer 崩溃或产生空图像。
        // 返回 nil 或使用一个默认尺寸是更安全的选择。这里选择返回 nil。
        if targetSize == .zero {
            print("Warning: intrinsicContentSize is .zero for Image. Cannot render to UIImage without a valid size.")
            return nil
        }
        
        view.bounds = CGRect(origin: .zero, size: targetSize)
        view.backgroundColor = .clear // 确保背景透明

        // 使用 UIGraphicsImageRenderer 将视图渲染为图像
        let renderer = UIGraphicsImageRenderer(size: targetSize)

        return renderer.image { _ in
            // 确保绘制操作在主线程执行，因为 UIKit 操作通常要求在主线程
            if Thread.isMainThread {
                view.drawHierarchy(in: view.bounds, afterScreenUpdates: true)
            } else {
                DispatchQueue.main.sync {
                    view.drawHierarchy(in: view.bounds, afterScreenUpdates: true)
                }
            }
        }
    }
}
