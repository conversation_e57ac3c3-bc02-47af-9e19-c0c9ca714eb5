import Foundation

// 正则表达式模式，用于匹配章节标题
// 增加了对一些常见英文章节标题和特殊格式的兼容
let chapterPatterns = [
    // 标准章节格式
    "^第[一二三四五六七八九十百千万零〇０-９0-9]+章\\s*[：:：]?\\s*[^\\n]*", // 第X章：标题
    "^第\\s*[一二三四五六七八九十百千万零〇０-９0-9]+\\s*章\\s*[：:：]?\\s*[^\\n]*", // 第 X 章：标题
    "^Chapter\\s+[0-9IVXLCDMivxlcdm]+\\s*[：:：]?\\s*[^\\n]*", // Chapter X: Title
    "^CHAPTER\\s+[0-9IVXLCDMivxlcdm]+\\s*[：:：]?\\s*[^\\n]*", // CHAPTER X: Title
    
    // 卷、部、篇格式
    "^第[一二三四五六七八九十百千万零〇０-９0-9]+[卷部篇]\\s*[：:：]?\\s*[^\\n]*",
    "^卷[一二三四五六七八九十百千万零〇０-９0-9]+\\s*[：:：]?\\s*[^\\n]*",
    
    // 特殊章节
    "^\\s*序章\\s*[：:：]?\\s*[^\\n]*",
    "^\\s*楔子\\s*[：:：]?\\s*[^\\n]*",
    "^\\s*引子\\s*[：:：]?\\s*[^\\n]*",
    "^\\s*前言\\s*[：:：]?\\s*[^\\n]*",
    "^\\s*后记\\s*[：:：]?\\s*[^\\n]*",
    "^\\s*尾声\\s*[：:：]?\\s*[^\\n]*",
    "^\\s*番外\\s*[：:：]?\\s*[^\\n]*",
    "^\\s*终章\\s*[：:：]?\\s*[^\\n]*",
    "^\\s*结局\\s*[：:：]?\\s*[^\\n]*",
    
    // 数字格式
    "^[0-9]+\\s*[：:：、．.]\\s*[^\\n]+", // 1: 标题 或 1. 标题
    "^[一二三四五六七八九十百千万零〇]+\\s*[：:：、．.]\\s*[^\\n]+", // 一、标题
    
    // 括号格式
    "^（[一二三四五六七八九十百千万零〇０-９0-9]+）\\s*[^\\n]+", // （一）标题
    "^\\([一二三四五六七八九十百千万零〇０-９0-9]+\\)\\s*[^\\n]+", // (一)标题
    
    // Markdown格式
    "^#{1,6}\\s+[^\\n]+", // # 标题
    
    // 网络小说格式
    "^\\s*正文\\s*第[一二三四五六七八九十百千万零〇０-９0-9]+章\\s*[：:：]?\\s*[^\\n]*",
    "^\\s*【[^】]+】\\s*第[一二三四五六七八九十百千万零〇０-９0-9]+章\\s*[：:：]?\\s*[^\\n]*",
    
    // 分隔线格式
    "^\\s*[-=*]{3,}\\s*[^\\n]*\\s*[-=*]{3,}\\s*$", // ===标题===
    "^\\s*[\\*]{3,}\\s*[^\\n]*\\s*[\\*]{3,}\\s*$" // ***标题***
]

@MainActor
class ChapterParser: @unchecked Sendable {
    private let patterns: [String]
    private var currentBookId: UUID? // 当前解析的书籍ID，用于关联章节

    init(patterns: [String] = chapterPatterns, bookId: UUID? = nil) {
        self.patterns = patterns
        self.currentBookId = bookId
    }

    /// 从给定的文本内容中解析章节列表
    /// - Parameters:
    ///   - text: 要解析的完整文本内容
    ///   - bookId: 可选，关联的书籍ID
    /// - Returns: 解析出的章节数组
    func parseChapters(from text: String, bookId: UUID? = nil) -> [Chapter] {
        self.currentBookId = bookId // 更新当前书籍ID
        var chapters: [Chapter] = []
        var currentPosition = 0 // 使用 Int 来跟踪字符位置
        var chapterIndex = 0

        // 将文本按行分割，同时保留原始的行分隔符长度信息，以便更精确计算位置
        // enumerateLines 对于多种换行符 (\n, \r, \r\n) 都能正确处理
        // 但如果文本中包含了 \r 或 \r\n 但没有 \n 的情况，enumerateLines 可能会错误地将它们视为一行
        // 因此，我们需要更精确地处理换行符，以确保准确计算章节位置
        // 目前的实现使用了 Int 来跟踪字符位置，这可能会导致一些问题，特别是在处理非 ASCII 字符时
        // 例如，对于包含中文字符的文本，UTF-8 编码的字符可能需要多个 Int 位置来表示
        // 对于 UTF-16 编码的文本，每个字符通常需要两个 Int 位置来表示
        // 对于 UTF-8 编码的文本，每个字符通常需要一个或多个 Int 位置来表示
        // 因此，对于包含中文字符的文本，我们可能需要使用更精确的字符位置计算方法

        // 但为了精确计算 startPosition，我们需要知道每行的确切字节数或UTF-16单元数
        // 这里我们假设 position 是基于 UTF-16 code units 的

        var lastChapterEndPosition = 0

        text.enumerateLines { [weak self] line, stop in
            guard let self = self else { return }

            let lineLength = line.utf16.count // 当前行的长度 (UTF-16 code units)
            // 换行符的长度，通常是1 (\n 或 \r) 或 2 (\r\n)。enumerateLines 会移除它们。
            // 为了简化，我们假设每行后有一个换行符，长度为1。更精确的计算需要分析原始文本的换行符。
            let newlineLength = 1 // 假设换行符长度为1，实际应根据文本确定

            if let detectedTitle = self.detectChapterTitle(line: line) {
                // 如果 chapters 非空，且最后一章的 endPosition 尚未设置，则更新它
                if !chapters.isEmpty, chapters.indices.contains(chapters.count - 1), chapters[chapters.count - 1].endPosition == nil {
                    chapters[chapters.count - 1].endPosition = currentPosition
                }

                let chapter = Chapter(
                    index: chapterIndex,
                    title: detectedTitle.trimmingCharacters(in: .whitespacesAndNewlines),
                    startPosition: currentPosition,
                    bookId: self.currentBookId
                )
                chapters.append(chapter)
                chapterIndex += 1
                // lastChapterEndPosition = currentPosition // 这行似乎不需要了，因为我们直接在检测到新章节时设置上一章的结束位置
            }
            // 更新当前位置，加上行长和估算的换行符长度
            currentPosition += lineLength + newlineLength
        }

        // 设置最后一章的结束位置为文本末尾
        if !chapters.isEmpty && chapters.indices.contains(chapters.count - 1) && chapters[chapters.count - 1].endPosition == nil {
            chapters[chapters.count - 1].endPosition = currentPosition
        }
        
        // 填充章节内容
        // 这一步可以根据需要决定是否执行，如果只是需要章节列表，则可以省略
        // 如果执行，可能会消耗较多内存和时间
        // chapters = fillChapterContents(chapters: chapters, originalText: text)

        return chapters
    }

    /// 检测单行文本是否为章节标题
    /// - Parameter line: 要检测的行文本
    /// - Returns: 如果是章节标题，则返回标题字符串，否则返回 nil
    private func detectChapterTitle(line: String) -> String? {
        let trimmedLine = line.trimmingCharacters(in: .whitespacesAndNewlines)
        
        // 基本过滤条件
        guard !trimmedLine.isEmpty else { return nil }
        guard trimmedLine.count >= 2 && trimmedLine.count <= 100 else { return nil }
        
        // 排除明显不是章节标题的行
        if isExcludedLine(trimmedLine) {
            return nil
        }

        for pattern in patterns {
            do {
                let regex = try NSRegularExpression(pattern: pattern, options: .caseInsensitive)
                let range = NSRange(trimmedLine.startIndex..<trimmedLine.endIndex, in: trimmedLine)
                if let match = regex.firstMatch(in: trimmedLine, options: [], range: range) {
                    // 进一步验证是否为有效章节标题
                    if isValidChapterTitle(trimmedLine) {
                        return trimmedLine
                    }
                }
            } catch {
                print("无效的正则表达式: \(pattern) - \(error.localizedDescription)")
                continue
            }
        }
        return nil
    }
    
    /// 检查是否为应排除的行
    private func isExcludedLine(_ line: String) -> Bool {
        let excludePatterns = [
            "^\\s*[0-9]{4}[-/年][0-9]{1,2}[-/月][0-9]{1,2}[日]?", // 日期格式
            "^\\s*[0-9]{1,2}:[0-9]{2}", // 时间格式
            "^\\s*第[0-9]+页", // 页码
            "^\\s*[0-9]+\\s*$", // 纯数字
            "^\\s*[-=*]{5,}\\s*$", // 纯分隔线
            "^\\s*[。！？.!?]+\\s*$", // 纯标点
            "^\\s*www\\.", // 网址
            "^\\s*http[s]?://", // 网址
            "^\\s*[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}", // 邮箱
            "^\\s*作者[:：]", // 作者信息
            "^\\s*来源[:：]", // 来源信息
            "^\\s*更新时间[:：]", // 更新时间
            "^\\s*字数[:：]", // 字数信息
        ]
        
        for pattern in excludePatterns {
            do {
                let regex = try NSRegularExpression(pattern: pattern, options: .caseInsensitive)
                let range = NSRange(line.startIndex..<line.endIndex, in: line)
                if regex.firstMatch(in: line, options: [], range: range) != nil {
                    return true
                }
            } catch {
                continue
            }
        }
        
        return false
    }
    
    /// 验证是否为有效的章节标题
    private func isValidChapterTitle(_ title: String) -> Bool {
        // 检查是否包含足够的文字内容
        let chineseCharCount = title.filter { char in
            return ("\u{4e00}"..."\u{9fff}").contains(char)
        }.count
        
        let englishCharCount = title.filter { char in
            return char.isLetter && char.isASCII
        }.count
        
        // 至少包含一些有意义的文字
        return chineseCharCount >= 1 || englishCharCount >= 3
    }
    
    /// 填充章节内容（可选步骤）
    /// - Parameters:
    ///   - chapters: 已解析的章节列表（只有标题和位置信息）
    ///   - originalText: 原始书籍文本
    /// - Returns: 填充了内容字段的章节列表
    private func fillChapterContents(chapters: [Chapter], originalText: String) -> [Chapter] {
        var updatedChapters = chapters
        let textUTF16 = Array(originalText.utf16)

        for i in 0..<updatedChapters.count {
            let chapter = updatedChapters[i]
            let startIndex = chapter.startPosition
            let endIndex: Int // 明确指定类型为 Int
            // 如果 endPosition 为 nil，说明是最后一章，内容到文本末尾
            if let explicitEnd = chapter.endPosition {
                endIndex = explicitEnd
            // 否则内容到下一章的 startPosition
            } else if i + 1 < updatedChapters.count {
                endIndex = updatedChapters[i+1].startPosition
            // 或者当前章节的 endPosition (如果都没有，则到文本末尾)
            } else {
                endIndex = textUTF16.count
            }

            guard endIndex > startIndex else {
                updatedChapters[i].content = ""
                continue
            }
            
            // 安全地截取子数组
            let start = textUTF16.index(textUTF16.startIndex, offsetBy: startIndex, limitedBy: textUTF16.endIndex) ?? textUTF16.startIndex
            let end = textUTF16.index(textUTF16.startIndex, offsetBy: endIndex, limitedBy: textUTF16.endIndex) ?? textUTF16.endIndex
            
            if start < end {
                let chapterContentSlice = textUTF16[start..<end]
                // 将 ArraySlice<UInt16> 转换为 [UInt16]，然后获取其指针
                let chapterContentArray = Array(chapterContentSlice)
                updatedChapters[i].content = chapterContentArray.withUnsafeBufferPointer { bufferPointer -> String? in
                    guard let baseAddress = bufferPointer.baseAddress else { return nil }
                    return String(utf16CodeUnits: baseAddress, count: chapterContentArray.count)
                } ?? ""
            } else {
                updatedChapters[i].content = ""
            }
        }
        return updatedChapters
    }
}

// 示例用法 (可以放在测试代码中)
/*
func testChapterParser() {
    let bookContent = """
    第一章 初始
    这是第一章的内容。
    第二章 发展
    这是第二章的内容。
    它有很多行。
    第三章 结束
    结局了。
    """
    let parser = ChapterParser()
    let chapters = parser.parseChapters(from: bookContent)
    
    for chapter in chapters {
        print("章节 \(chapter.index): \(chapter.title) (位置: \(chapter.startPosition) - \(chapter.endPosition ?? -1))")
        // 如果执行了 fillChapterContents，可以打印内容
        // print("内容: \(chapter.content ?? "无")")
        // print("-----")
    }
    
    // 测试填充内容
    let chaptersWithContent = parser.fillChapterContents(chapters: chapters, originalText: bookContent)
    for chapter in chaptersWithContent {
        print("章节 \(chapter.index): \(chapter.title) (位置: \(chapter.startPosition) - \(chapter.endPosition ?? -1))")
        print("内容: \(chapter.content ?? "无")")
        print("-----")
    }
}
*/