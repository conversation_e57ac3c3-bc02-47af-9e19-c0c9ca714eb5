import Foundation

/// HTML解析器
class HTMLParser: DocumentParser {
    private let contentFetcher = ContentFetcher()
    
    func parse(url: URL) async throws -> String {
        // 尝试使用 UTF-8 编码读取，如果失败则尝试 GBK
        let htmlContent: String
        do {
            htmlContent = try String(contentsOf: url, encoding: .utf8)
        } catch {
            // 如果 UTF-8 失败，尝试 GBK (GB18030 是更完整的中文编码)
            let gbkEncoding = CFStringConvertEncodingToNSStringEncoding(CFStringEncoding(CFStringEncodings.GB_18030_2000.rawValue))
            htmlContent = try String(contentsOf: url, encoding: String.Encoding(rawValue: gbkEncoding))
        }
        guard let content = contentFetcher.extractTextFromHTML(htmlContent) else {
            throw NSError(domain: "HTMLParser", code: -1, userInfo: [NSLocalizedDescriptionKey: "HTML解析失败"])
        }
        return content
    }
    
    func getPageCount(url: URL) throws -> Int {
        // HTML文档视为单页
        return 1
    }
    
    func getPageContent(url: URL, pageIndex: Int) throws -> String {
        guard pageIndex == 0 else {
            throw NSError(domain: "HTMLParser", code: -1, userInfo: [NSLocalizedDescriptionKey: "HTML文档不支持分页"])
        }
        // 尝试使用 UTF-8 编码读取，如果失败则尝试 GBK
        do {
            return try String(contentsOf: url, encoding: .utf8)
        } catch {
            // 如果 UTF-8 失败，尝试 GBK (GB18030 是更完整的中文编码)
            let gbkEncoding = CFStringConvertEncodingToNSStringEncoding(CFStringEncoding(CFStringEncodings.GB_18030_2000.rawValue))
            return try String(contentsOf: url, encoding: String.Encoding(rawValue: gbkEncoding))
        }
    }
}