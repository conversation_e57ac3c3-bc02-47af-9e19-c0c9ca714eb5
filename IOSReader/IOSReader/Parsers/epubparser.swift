//
//  EPUBParser.swift
//  IOSReader
//
//  Created by Trae AI on 2025/4/1.
//
//  EPUB 文件解析器
//  职责：
//  1. 解析 META-INF/container.xml 文件，获取 OPF 文件的路径。
//  2. 解析 OPF (Open Packaging Format) 文件，获取书籍元数据、文件清单 (manifest) 和阅读顺序 (spine)。
//  3. 提供从 EPUB 文件中提取内容（如章节HTML）的接口。
//

import Foundation
// import ZIPFoundation // 移除了 ZIPFoundation 导入,因为该模块不存在
// import Kanna // 引入 Kanna 用于 XML/HTML 解析，如果已在项目中配置

enum EPUBParseError: Error, LocalizedError {
    case containerXMLNotFound
    case opfFileNotFound(String)
    case xmlParsingError(String)
    case requiredElementMissing(String)

    var errorDescription: String? {
        switch self {
        case .containerXMLNotFound:
            return "未找到 META-INF/container.xml 文件。"
        case .opfFileNotFound(let path):
            return "未找到 OPF 文件：\(path)。"
        case .xmlParsingError(let details):
            return "XML 解析失败：\(details)。"
        case .requiredElementMissing(let elementName):
            return "XML 中缺少必要的元素：\(elementName)。"
        }
    }
}

struct EPUBMetadata {
    var title: String?
    var creator: String? // 作者
    var language: String?
    var identifier: String?
    var publisher: String?
    var coverImageId: String? // 封面图片在 manifest 中的 ID
    // 可以根据需要添加更多元数据字段
}

struct EPUBManifestItem {
    var id: String
    var href: String
    var mediaType: String
}

struct EPUBSpineItemRef {
    var idref: String
    var linear: Bool // 通常为 "yes" 或 "no"
}

class EPUBParser {
    public init() {}

    private let fileManager = FileManager.default

    /// 解析 META-INF/container.xml 文件，获取 OPF 文件的完整路径。
    /// - Parameter containerXMLURL: container.xml 文件的 URL。
    /// - Returns: OPF 文件的相对路径 (相对于 EPUB 解压后的根目录)。
    /// - Throws: EPUBParseError 如果解析失败。
    func getOPFPath(from containerXMLURL: URL) throws -> String {
        // TODO: 实现 container.xml 的解析逻辑
        // 1. 读取 containerXMLURL 的内容
        // 2. 使用 XML 解析器 (如 XMLParser 或 Kanna) 解析内容
        // 3. 找到 <rootfile> 元素的 full-path 属性值
        // 示例占位符，需要替换为实际的解析代码
        print("EPUBParser: 开始解析 container.xml at \(containerXMLURL.path)")
        guard let xmlData = try? Data(contentsOf: containerXMLURL) else {
            throw EPUBParseError.xmlParsingError("无法读取 container.xml 文件内容。")
        }

        // 这里需要一个 XML 解析库，或者使用 Foundation 的 XMLParser
        // 假设使用 XMLParser
        let parserDelegate = ContainerXMLParserDelegate()
        let xmlParser = XMLParser(data: xmlData)
        xmlParser.delegate = parserDelegate
        if xmlParser.parse() {
            if let opfPath = parserDelegate.opfPath {
                print("EPUBParser: 从 container.xml 中成功解析出 OPF 路径: \(opfPath)")
                return opfPath
            } else {
                throw EPUBParseError.requiredElementMissing("container.xml 中未找到 rootfile 的 full-path 属性")
            }
        } else {
            throw EPUBParseError.xmlParsingError("解析 container.xml 失败: \(xmlParser.parserError?.localizedDescription ?? "未知错误")")
        }
    }

    /// 解析 OPF 文件。
    /// - Parameter opfFileURL: OPF 文件的 URL。
    /// - Parameter opfFileBaseURL: OPF 文件所在目录的 URL，用于解析相对路径。
    /// - Returns: 一个包含元数据、manifest 和 spine 的元组。
    /// - Throws: EPUBParseError 如果解析失败。
    func parseOPF(at opfFileURL: URL, opfFileBaseURL: URL) throws -> (metadata: EPUBMetadata, manifest: [String: EPUBManifestItem], spine: [EPUBSpineItemRef]) {
        // TODO: 实现 OPF 文件的解析逻辑
        // 1. 读取 opfFileURL 的内容
        // 2. 解析元数据 (metadata)
        // 3. 解析文件清单 (manifest)
        // 4. 解析阅读顺序 (spine)
        print("EPUBParser: 开始解析 OPF 文件 at \(opfFileURL.path)")
        guard let xmlData = try? Data(contentsOf: opfFileURL) else {
            throw EPUBParseError.xmlParsingError("无法读取 OPF 文件内容。")
        }

        let parserDelegate = OPFParserDelegate(baseURL: opfFileBaseURL)
        let xmlParser = XMLParser(data: xmlData)
        xmlParser.delegate = parserDelegate

        if xmlParser.parse() {
            print("EPUBParser: 成功解析 OPF 文件。")
            return (parserDelegate.metadata, parserDelegate.manifest, parserDelegate.spine)
        } else {
            throw EPUBParseError.xmlParsingError("解析 OPF 文件失败: \(xmlParser.parserError?.localizedDescription ?? "未知错误")")
        }
    }
    
    // TODO: 添加用于加载特定章节内容的方法，可能需要传入 manifest 和 spine 信息以及解压目录的 URL
}

// MARK: - XML Parser Delegates

/// `XMLParserDelegate` 用于解析 `container.xml`
private class ContainerXMLParserDelegate: NSObject, XMLParserDelegate {
    var opfPath: String?
    private var currentElement: String = ""
    private var foundRootfile = false

    func parser(_ parser: XMLParser, didStartElement elementName: String, namespaceURI: String?, qualifiedName qName: String?, attributes attributeDict: [String : String] = [:]) {
        currentElement = elementName
        if elementName == "rootfile" {
            if let fullPath = attributeDict["full-path"],
               attributeDict["media-type"] == "application/oebps-package+xml" {
                opfPath = fullPath
                foundRootfile = true
                parser.abortParsing() // 找到后即可停止解析
            }
        }
    }
}

/// `XMLParserDelegate` 用于解析 OPF 文件
private class OPFParserDelegate: NSObject, XMLParserDelegate {
    var metadata = EPUBMetadata()
    var manifest: [String: EPUBManifestItem] = [:]
    var spine: [EPUBSpineItemRef] = []

    private var currentElement: String = ""
    private var currentText: String = ""
    private var currentManifestItem: EPUBManifestItem?
    private var currentSpineItemRef: EPUBSpineItemRef?
    private let baseURL: URL // OPF 文件所在目录的 URL，用于解析相对路径

    init(baseURL: URL) {
        self.baseURL = baseURL
        super.init()
    }

    func parser(_ parser: XMLParser, didStartElement elementName: String, namespaceURI: String?, qualifiedName qName: String?, attributes attributeDict: [String : String] = [:]) {
        currentElement = elementName
        currentText = ""

        switch elementName {
        case "metadata":
            // 通常 metadata 标签本身不直接包含文本，其子元素包含
            break
        case "dc:title":
            break // 等待 characters
        case "dc:creator":
            break // 等待 characters
        case "dc:language":
            break // 等待 characters
        case "dc:identifier":
            // 通常 id 属性是重要的，比如 package unique identifier
            if let id = attributeDict["id"], id == attributeDict["opf:scheme"] { // 检查是否是主要的标识符
                 // 实际的标识符内容在 characters 中
            }
            break
        case "dc:publisher":
            break // 等待 characters
        case "meta": // 用于封面等
            if attributeDict["name"] == "cover", let content = attributeDict["content"] {
                metadata.coverImageId = content
            }
        case "manifest":
            break
        case "item":
            if let id = attributeDict["id"],
               let href = attributeDict["href"],
               let mediaType = attributeDict["media-type"] {
                // 将相对路径 href 转换为绝对路径或相对于解压根目录的路径
                // let fullHref = baseURL.appendingPathComponent(href).path // 这会创建绝对文件系统路径
                // EPUB内部路径通常是相对于OPF文件的，或者有时是相对于EPUB根目录的
                // 这里的href通常是相对于OPF文件所在目录的，或者需要根据具体EPUB结构调整
                currentManifestItem = EPUBManifestItem(id: id, href: href, mediaType: mediaType)
            }
        case "spine":
            // toc 属性指向 NCX 文件的 ID
            // let tocID = attributeDict["toc"]
            break
        case "itemref":
            if let idref = attributeDict["idref"] {
                let linear = (attributeDict["linear"] ?? "yes").lowercased() == "yes"
                currentSpineItemRef = EPUBSpineItemRef(idref: idref, linear: linear)
            }
        default:
            break
        }
    }

    func parser(_ parser: XMLParser, foundCharacters string: String) {
        currentText += string.trimmingCharacters(in: .whitespacesAndNewlines)
    }

    func parser(_ parser: XMLParser, didEndElement elementName: String, namespaceURI: String?, qualifiedName qName: String?) {
        switch elementName {
        case "dc:title":
            metadata.title = currentText
        case "dc:creator":
            metadata.creator = currentText
        case "dc:language":
            metadata.language = currentText
        case "dc:identifier":
            // 假设我们只取第一个 identifier 作为主要标识符
            if metadata.identifier == nil {
                metadata.identifier = currentText
            }
        case "dc:publisher":
            metadata.publisher = currentText
        case "item":
            if let item = currentManifestItem {
                manifest[item.id] = item
            }
            currentManifestItem = nil
        case "itemref":
            if let itemref = currentSpineItemRef {
                spine.append(itemref)
            }
            currentSpineItemRef = nil
        default:
            break
        }
        currentElement = ""
        currentText = ""
    }
    
    func parser(_ parser: XMLParser, parseErrorOccurred parseError: Error) {
        print("OPF Parsing Error: \(parseError.localizedDescription)")
        // 可以选择在这里中止解析或记录错误
    }
}