//  DocumentParser.swift
//  IOSReader
//
//  文档解析模块，负责支持多种电子书格式（TXT、PDF、EPUB、MOBI、HTML）的内容解析与分页。
//  主要内容：
//  - BookFormat：电子书格式自动识别
//  - DocumentParser协议：统一解析接口
//  - 各格式解析器实现（如TXT、PDF等）
//  - 提供异步解析、分页、内容提取等能力，供阅读器核心模块调用
//
import Foundation
import PDFKit
import SwiftUI
import ZIPFoundation

/// 支持的电子书格式
public enum BookFormat {
    case txt
    case pdf
    case epub
    // case mobi // MOBI 已移除
    case html
    
    /// 根据文件扩展名判断格式
    public static func detect(from url: URL) -> BookFormat? {
        switch url.pathExtension.lowercased() {
        case "txt":
            return .txt
        case "pdf":
            return .pdf
        case "epub":
            return .epub
        // case "mobi": // MOBI 已移除
        //    return .mobi
        case "html", "htm":
            return .html
        default:
            return nil
        }
    }
}

/// 文档解析器协议
protocol DocumentParser {
    func parse(url: URL) async throws -> String
    func getPageCount(url: URL) throws -> Int
    func getPageContent(url: URL, pageIndex: Int) throws -> String
}

/// TXT解析器
class TXTParser: DocumentParser {
    func parse(url: URL) async throws -> String {
        return try String(contentsOf: url, encoding: .utf8)
    }
    
    func getPageCount(url: URL) throws -> Int {
        let content = try String(contentsOf: url, encoding: .utf8)
        let pageSize = CGSize(width: 375, height: 667)
        return Self.calculatePageCount(text: content, pageSize: pageSize)
    }
    
    func getPageContent(url: URL, pageIndex: Int) throws -> String {
        let content = try String(contentsOf: url, encoding: .utf8)
        let pageSize = CGSize(width: 375, height: 667)
        let pages = Self.paginate(text: content, pageSize: pageSize)
        guard pageIndex >= 0 && pageIndex < pages.count else { return "" }
        return pages[pageIndex]
    }
    
    static func calculatePageCount(text: String, pageSize: CGSize) -> Int {
        let charsPerLine = Int(pageSize.width / 16) - 4
        let lineHeight: CGFloat = 20
        let linesPerPage = Int(pageSize.height / lineHeight) - 4
        let charsPerPage = charsPerLine * linesPerPage
        return max(1, Int(ceil(Double(text.count) / Double(charsPerPage))))
    }
    
    static func paginate(text: String, pageSize: CGSize) -> [String] {
        let charsPerLine = Int(pageSize.width / 16) - 4
        let lineHeight: CGFloat = 20
        let linesPerPage = Int(pageSize.height / lineHeight) - 4
        let charsPerPage = charsPerLine * linesPerPage
        let totalPages = max(1, Int(ceil(Double(text.count) / Double(charsPerPage))))
        var pages: [String] = []
        for i in 0..<totalPages {
            let start = text.index(text.startIndex, offsetBy: min(i * charsPerPage, text.count))
            let end = text.index(start, offsetBy: min(charsPerPage, text.count - i * charsPerPage))
            pages.append(String(text[start..<end]))
        }
        return pages
    }
}

/// PDF解析器
class PDFParser: DocumentParser {
    func parse(url: URL) async throws -> String {
        guard let document = PDFDocument(url: url) else {
            throw NSError(domain: "PDFParser", code: -1, userInfo: [NSLocalizedDescriptionKey: "无法打开PDF文档"])
        }
        var content = ""
        for i in 0..<document.pageCount {
            if let page = document.page(at: i) {
                content += page.string ?? ""
                content += "\n\n"
            }
        }
        return content
    }
    
    func getPageCount(url: URL) throws -> Int {
        guard let document = PDFDocument(url: url) else {
            throw NSError(domain: "PDFParser", code: -1, userInfo: [NSLocalizedDescriptionKey: "无法打开PDF文档"])
        }
        return document.pageCount
    }
    
    func getPageContent(url: URL, pageIndex: Int) throws -> String {
        guard let document = PDFDocument(url: url) else {
            throw NSError(domain: "PDFParser", code: -1, userInfo: [NSLocalizedDescriptionKey: "无法打开PDF文档"])
        }
        guard pageIndex >= 0 && pageIndex < document.pageCount,
              let page = document.page(at: pageIndex) else {
            throw NSError(domain: "PDFParser", code: -1, userInfo: [NSLocalizedDescriptionKey: "无效的页码"])
        }
        return page.string ?? ""
    }
}