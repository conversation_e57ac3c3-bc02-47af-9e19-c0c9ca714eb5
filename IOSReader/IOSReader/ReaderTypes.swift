import SwiftUI

/// 文本块结构
/// 用于存储和管理阅读器中的文本内容
struct TextChunk: Identifiable, Codable, Hashable {
    var id = UUID()
    var content: String
    var isChapterTitle: Bool
    var chapterIndex: Int?
    
    init(content: String, isChapterTitle: Bool = false, chapterIndex: Int? = nil) {
        self.content = content
        self.isChapterTitle = isChapterTitle
        self.chapterIndex = chapterIndex
    }
}

/// 阅读器设置
/// 管理阅读器的显示和行为配置
@MainActor
class ReaderSettings: ObservableObject, @preconcurrency Codable, @preconcurrency Equatable, @unchecked Sendable {
    @Published var fontSize: CGFloat = 18
    @Published var lineSpacing: CGFloat = 8
    @Published var paragraphSpacing: CGFloat = 12
    @Published var fontFamily: String = ".AppleSystemUIFont"
    @Published var textColor: Color = .primary
    @Published var backgroundColor: Color = .white
    @Published var pageTransitionStyle: PageTransitionStyle = .scroll
    @Published var horizontalPadding: CGFloat = 20
    @Published var accentColor: Color = .blue
    @Published var backgroundImage: String = "default"
    @Published var fontName: String = "System"
    @Published var letterSpacing: CGFloat = 0
    
    enum CodingKeys: String, CodingKey {
        case fontSize, lineSpacing, paragraphSpacing, fontFamily, fontName, letterSpacing
        case textColor, backgroundColor, pageTransitionStyle, horizontalPadding, accentColor
    }
    
    // 主题预设
    func applyDayMode() {
        backgroundColor = .white
        textColor = .black
    }
    
    func applyNightMode() {
        backgroundColor = Color(red: 0.1, green: 0.1, blue: 0.1)
        textColor = Color(red: 0.9, green: 0.9, blue: 0.9)
    }
    
    func applySepia() {
        backgroundColor = Color(red: 0.98, green: 0.94, blue: 0.85)
        textColor = Color(red: 0.35, green: 0.25, blue: 0.15)
    }
    
    init() {}
    
    required init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        fontSize = try container.decode(CGFloat.self, forKey: .fontSize)
        lineSpacing = try container.decode(CGFloat.self, forKey: .lineSpacing)
        paragraphSpacing = try container.decode(CGFloat.self, forKey: .paragraphSpacing)
        fontFamily = try container.decode(String.self, forKey: .fontFamily)
        fontName = try container.decode(String.self, forKey: .fontName)
        letterSpacing = try container.decode(CGFloat.self, forKey: .letterSpacing)
        
        // 使用 Codable 解码 Color
        textColor = try container.decode(CodableColor.self, forKey: .textColor).color
        backgroundColor = try container.decode(CodableColor.self, forKey: .backgroundColor).color
        accentColor = try container.decode(CodableColor.self, forKey: .accentColor).color
        
        pageTransitionStyle = try container.decode(PageTransitionStyle.self, forKey: .pageTransitionStyle)
        horizontalPadding = try container.decode(CGFloat.self, forKey: .horizontalPadding)
    }
    
    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(fontSize, forKey: .fontSize)
        try container.encode(lineSpacing, forKey: .lineSpacing)
        try container.encode(paragraphSpacing, forKey: .paragraphSpacing)
        try container.encode(fontFamily, forKey: .fontFamily)
        try container.encode(fontName, forKey: .fontName)
        try container.encode(letterSpacing, forKey: .letterSpacing)
        
        // 使用 Codable 编码 Color
        try container.encode(CodableColor(color: textColor), forKey: .textColor)
        try container.encode(CodableColor(color: backgroundColor), forKey: .backgroundColor)
        try container.encode(CodableColor(color: accentColor), forKey: .accentColor)
        
        try container.encode(pageTransitionStyle, forKey: .pageTransitionStyle)
        try container.encode(horizontalPadding, forKey: .horizontalPadding)
    }
    
    // MARK: - Equatable
    static func == (lhs: ReaderSettings, rhs: ReaderSettings) -> Bool {
        return lhs.fontSize == rhs.fontSize &&
               lhs.lineSpacing == rhs.lineSpacing &&
               lhs.paragraphSpacing == rhs.paragraphSpacing &&
               lhs.fontFamily == rhs.fontFamily &&
               lhs.fontName == rhs.fontName &&
               lhs.letterSpacing == rhs.letterSpacing &&
               lhs.textColor == rhs.textColor &&
               lhs.backgroundColor == rhs.backgroundColor &&
               lhs.pageTransitionStyle == rhs.pageTransitionStyle &&
               lhs.horizontalPadding == rhs.horizontalPadding &&
               lhs.accentColor == rhs.accentColor &&
               lhs.backgroundImage == rhs.backgroundImage
    }
}

// MARK: - Color Codable Helper

/// 用于使 SwiftUI Color 符合 Codable 的辅助结构体
struct CodableColor: Codable {
    var red: Double
    var green: Double
    var blue: Double
    var opacity: Double

    var color: Color {
        Color(.sRGB, red: red, green: green, blue: blue, opacity: opacity)
    }

    init(color: Color) {
        // 尝试获取 RGBA 值
        // 注意：这可能不适用于所有 Color 类型（例如系统颜色），需要更健壮的处理
        // 一个更安全的方法是使用 UIColor 来获取组件
        #if canImport(UIKit)
        let uiColor = UIColor(color)
        var r: CGFloat = 0
        var g: CGFloat = 0
        var b: CGFloat = 0
        var a: CGFloat = 0
        uiColor.getRed(&r, green: &g, blue: &b, alpha: &a)
        self.red = Double(r)
        self.green = Double(g)
        self.blue = Double(b)
        self.opacity = Double(a)
        #else
        // macOS 或其他平台的回退（可能不准确）
        // 这是一个简化的示例，实际应用中可能需要更复杂的逻辑
        // 或者限制只能存储可分解为 RGBA 的颜色
        print("Warning: Color to RGBA conversion might be inaccurate on this platform.")
        // 默认设为黑色
        self.red = 0
        self.green = 0
        self.blue = 0
        self.opacity = 1
        #endif
    }
}

/// 页面过渡样式
enum PageTransitionStyle: String, Codable {
    case scroll     // 滚动
    case overlay    // 覆盖
    case simulation // 模拟翻书
}

/// 文件类型
/// 字体类型别名
#if canImport(UIKit)
import UIKit
typealias FontType = UIFont
#elseif canImport(AppKit)
import AppKit
typealias FontType = NSFont
#else
#error("Unsupported platform")
#endif

enum FileType: String, Codable {
    case txt      // 文本文件
    case pdf      // PDF文件
    case epub     // EPUB电子书
    case html     // HTML文件
    // case mobi     // MOBI电子书 - 已移除
    case unknown  // 未知类型
    
    /// 根据文件路径判断文件类型
    static func detectType(from path: String) -> FileType {
        let fileExtension = (path as NSString).pathExtension.lowercased()
        switch fileExtension {
        case "txt":
            return .txt
        case "pdf":
            return .pdf
        case "epub":
            return .epub
        case "html", "htm":
            return .html
        // case "mobi": // MOBI 已移除
        //    return .mobi
        default:
            return .unknown
        }
    }
}

protocol ErrorHandling {
    func handleError(_ error: Error) async -> Bool
    func logError(_ error: Error) async
}

extension ReaderCore: ErrorHandling {
    func handleError(_ error: Error) async -> Bool {
        await logError(error)
        return await autoRepair(error)
    }
    
    func logError(_ error: Error) async {
        print("错误发生: \(error.localizedDescription)")
    }
    
    func recoverFromError(_ error: Error) async -> Bool {
        return await autoRepair(error)
    }
}
