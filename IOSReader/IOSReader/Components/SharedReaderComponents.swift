import SwiftUI
import AVFoundation

/// 共享阅读器组件
/// 这个文件包含在不同阅读器视图之间共享的UI组件
/// 设计为同时支持ReaderViewModel和ReaderCoordinator

// MARK: - 书签管理视图
@MainActor // 将结构体标记为 @MainActor
struct SharedBookmarkManagementView: View {
    // 使用泛型支持不同的数据源
    var bookmarks: [String] = []
    var onSelectBookmark: (Int) -> Void
    var onAddBookmark: () -> Void
    var onDeleteBookmark: (Int) -> Void
    
    @Environment(\.presentationMode) var presentationMode
    
    // 初始化方法 - 支持ReaderViewModel
    init(vm: ReaderViewModel) {
        // 从ViewModel获取书签数据
        self.bookmarks = vm.bookmarks.map { $0.displayText() } 
        
        // 实现跳转到书签
        self.onSelectBookmark = { index in
            if index < vm.bookmarks.count {
                Task {
                    await vm.jumpToBookmark(vm.bookmarks[index])
                }
            }
        }
        
        // 实现添加书签
        self.onAddBookmark = {
            Task {
                // 提供一个空的 description，假设 ReaderViewModel.addBookmark 需要此参数
                await vm.addBookmark(description: "")
            }
        }
        
        // 实现删除书签
        self.onDeleteBookmark = { index in
            if index < vm.bookmarks.count {
                Task {
                    try await vm.deleteBookmark(vm.bookmarks[index])
                }
            }
        }
    }
    
    // 初始化方法 - 支持ReaderCoordinator
    init(coordinator: ReaderCoordinator) {
        // 存储coordinator引用以便在闭包中使用
        let coord = coordinator
        
        // 从当前书籍获取书签数据
        if let book = coord.currentBook { // 使用 currentBook 以确保是当前正在阅读的书籍
            let bookId = book.id.uuidString
            // 直接从 coordinator 的 bookmarks 字典获取书签
            let bookBookmarks = coord.bookmarks[bookId] ?? [] // 如果没有书签则返回空数组
            self.bookmarks = bookBookmarks.map { $0.displayText() }
            
            // 实现跳转到书签
            self.onSelectBookmark = { index in
                if index < bookBookmarks.count {
                    coord.jumpToBookmark(bookBookmarks[index])
                }
            }
            
            // 实现添加书签
            self.onAddBookmark = {
                // 获取当前页码
                // let pageNumber = coord.currentPage // 不再需要，因为 addBookmarkAtCurrentPage 会处理
                
                // 创建书签内容预览
                // let textSnippet = "第\(coord.currentChapterIndex + 1)章 第\(pageNumber + 1)页" // 不再需要
                
                // 添加书签
                // coord.addBookmark(bookID: bookId, pageNumber: pageNumber, textSnippet: textSnippet) // 旧的调用方式
                Task {
                    await coord.addBookmarkAtCurrentPage() // 新的调用方式，ReaderCoordinator 中定义了此方法
                }
            }
            
            // 实现删除书签
            self.onDeleteBookmark = { index in
                if index < bookBookmarks.count {
                    coord.deleteBookmark(bookBookmarks[index])
                }
            }
        } else {
            // 如果没有书籍，使用空实现
            self.bookmarks = []
            self.onSelectBookmark = { _ in }
            self.onAddBookmark = { }
            self.onDeleteBookmark = { _ in }
        }
    }
    
    var body: some View {
        NavigationView {
            List {
                if bookmarks.isEmpty {
                    Text("暂无书签")
                        .foregroundColor(.gray)
                        .frame(maxWidth: .infinity, alignment: .center)
                        .padding()
                } else {
                    ForEach(0..<bookmarks.count, id: \.self) { index in
                        Button(action: {
                            onSelectBookmark(index)
                            presentationMode.wrappedValue.dismiss()
                        }) {
                            Text(bookmarks[index])
                                .padding(.vertical, 8)
                        }
                    }
                    .onDelete { indexSet in
                        if let index = indexSet.first {
                            Task {
                                onDeleteBookmark(index)
                            }
                        }
                    }
                }
            }
            .navigationTitle("书签管理")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("完成") {
                        presentationMode.wrappedValue.dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarLeading) {
                    Button(action: onAddBookmark) {
                        Image(systemName: "plus")
                    }
                }
            }
        }
    }
}

// MARK: - 章节列表视图
@MainActor // 将结构体标记为 @MainActor
struct SharedChapterListView: View {
    private var chapters: [(title: String, index: Int)] = []
    private var onSelectChapter: (Int) -> Void
    
    @Environment(\.presentationMode) var presentationMode
    
    // 初始化方法 - 支持ReaderViewModel
    init(vm: ReaderViewModel, onSelect: @escaping (Int) -> Void) {
        // 从ViewModel获取章节数据
        self.chapters = vm.chapters.enumerated().map { (index, chapter) in 
            let trimmedTitle = chapter.title.trimmingCharacters(in: .whitespacesAndNewlines)
            let displayTitle = trimmedTitle.isEmpty ? "章节 \(index + 1)" : trimmedTitle
            return (title: displayTitle, index: index)
        }
        self.onSelectChapter = onSelect // 使用传入的闭包
    }
    
    // 初始化方法 - 支持ReaderCoordinator
    init(coordinator: ReaderCoordinator, onSelect: @escaping (Int) -> Void) {
        // 从 Coordinator 获取章节数据
        self.chapters = coordinator.chapters.enumerated().map { (enumIndex, chapterTuple) in // chapterTuple is (title: String, index: Int)
            // chapterTuple.title from coordinator.chapters is String (non-optional)
            let trimmedTitle = chapterTuple.title.trimmingCharacters(in: .whitespacesAndNewlines)
            // 如果处理后的标题为空，则使用章节的原始索引生成默认标题
            let displayTitle = trimmedTitle.isEmpty
                      ? "章节 \(chapterTuple.index + 1)" // 使用章节自身的索引
                      : trimmedTitle
            // 返回包含显示标题和原始章节索引的元组
            return (title: displayTitle, index: chapterTuple.index) // 使用章节自身的索引
        }
        self.onSelectChapter = onSelect // 使用传入的闭包
    }

    // ... (保留原有的 init(vm: ReaderViewModel) 初始化方法)
    
    var body: some View {
        NavigationView {
            List {
                if chapters.isEmpty {
                    Text("未检测到章节")
                        .foregroundColor(.gray)
                        .frame(maxWidth: .infinity, alignment: .center)
                        .padding()
                } else {
                    // 使用 chapters 数组的索引作为 ID
                    ForEach(chapters.indices, id: \.self) { chapterIndex in
                        Button(action: {
                            // 传递 chapters 数组的索引给 onSelectChapter
                            onSelectChapter(chapterIndex)
                            // presentationMode.wrappedValue.dismiss() // Dismissal should be handled by the caller (UnifiedReaderView)
                        }) {
                            // 显示章节标题
                            Text(chapters[chapterIndex].title)
                                .padding(.vertical, 8)
                        }
                    }
                }
            }
            .navigationTitle("目录")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("完成") {
                        presentationMode.wrappedValue.dismiss()
                    }
                }
            }
        }
    }
}

// MARK: - 下载选项视图
struct SharedDownloadOptionsView: View {
    private var onDownloadCurrentChapter: () -> Void
    private var onDownloadAllChapters: () -> Void
    private var onClearCache: () -> Void
    
    @Environment(\.presentationMode) var presentationMode
    
    // 初始化方法 - 支持ReaderViewModel
    init(vm: ReaderViewModel) {
        self.onDownloadCurrentChapter = {
            // 实现缓存当前章节的功能
        }
        
        self.onDownloadAllChapters = {
            // 实现缓存全书的功能
        }
        
        self.onClearCache = {
            // 实现清除缓存的功能
        }
    }
    
    // 初始化方法 - 支持ReaderCoordinator
    init(coordinator: ReaderCoordinator) {
        self.onDownloadCurrentChapter = {
            // 实现缓存当前章节的功能
        }
        
        self.onDownloadAllChapters = {
            // 实现缓存全书的功能
        }
        
        self.onClearCache = {
            // 实现清除缓存的功能
        }
    }
    
    var body: some View {
        NavigationView {
            List {
                Button(action: {
                    onDownloadCurrentChapter()
                    presentationMode.wrappedValue.dismiss()
                }) {
                    Label("缓存当前章节", systemImage: "arrow.down.circle")
                }
                
                Button(action: {
                    onDownloadAllChapters()
                    presentationMode.wrappedValue.dismiss()
                }) {
                    Label("缓存全书", systemImage: "arrow.down.doc.fill")
                }
                
                Button(action: {
                    onClearCache()
                    presentationMode.wrappedValue.dismiss()
                }) {
                    Label("清除缓存", systemImage: "trash")
                        .foregroundColor(.red)
                }
            }
            .navigationTitle("缓存管理")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("完成") {
                        presentationMode.wrappedValue.dismiss()
                    }
                }
            }
        }
    }
}
