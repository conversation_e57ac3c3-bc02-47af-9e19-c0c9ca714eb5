import SwiftUI

/// 阅读器工具栏组件
/// 包含顶部和底部工具栏的视图逻辑
@MainActor // 将结构体标记为 @MainActor
struct ReaderToolbars {
    // MARK: - 顶部工具栏
    @ViewBuilder
    static func topToolbar(
        coordinator: ReaderCoordinator,
        backgroundColor: Color,
        textColor: Color,
        readerToolbarHeight: CGFloat,
        dismiss: DismissAction
    ) -> some View {
        HStack {
            // 返回按钮
            Button {
                dismiss()
            } label: {
                Image(systemName: "chevron.left")
                    .foregroundColor(textColor)
            }
            .padding(.leading)
            
            Spacer()
            
            // 书名
            Text(coordinator.readerViewModel.currentBook?.title ?? "")
                .font(.headline)
                .lineLimit(1)
                .truncationMode(.tail)
            
            Spacer()
            
            // 听书按钮
            Button {
                Task { // 使用 Task 包装异步调用
                    await coordinator.toggleListening()
                }
            } label: {
                Image(systemName: coordinator.isListening ? "speaker.wave.2.fill" : "speaker.wave.2")
                    .foregroundColor(textColor)
            }
            
            // 书签按钮
            Button {
                Task { // 使用 Task 包装异步调用
                    await coordinator.addBookmarkAtCurrentPage()
                }
            } label: {
                Image(systemName: "bookmark")
                    .foregroundColor(textColor)
            }
            .padding(.trailing)
        }
        .background(backgroundColor.opacity(0.8).edgesIgnoringSafeArea(.top))
        .frame(height: readerToolbarHeight)
    }
    
    // MARK: - 底部工具栏
    @ViewBuilder
    static func bottomToolbar(
        coordinator: ReaderCoordinator,
        backgroundColor: Color,
        textColor: Color,
        readerToolbarHeight: CGFloat
    ) -> some View {
        VStack(spacing: 0) {
            // 上一行：上一章，下一章，阅读进度
            HStack {
                Button {
                    Task {
                        await coordinator.readerViewModel.previousPage()
                    }
                } label: {
                    Text("上一章")
                        .foregroundColor(textColor)
                }
                
                Spacer()
                Text("\(coordinator.readerViewModel.currentChapterIndex + 1)/\(coordinator.readerViewModel.chapters.count) \(coordinator.readerViewModel.currentChapterTitle)")
                    .font(.caption)
                    .foregroundColor(textColor)
                Spacer()
                
                Button {
                    Task {
                        await coordinator.readerViewModel.nextPage()
                    }
                } label: {
                    Text("下一章")
                        .foregroundColor(textColor)
                }
            }
            .padding(.horizontal)
            .frame(height: 44)
            
            // 下一行：目录，亮度，深色，设置
            HStack {
                Spacer()
                ToolbarButton(icon: "list.bullet", text: "目录") {
                    coordinator.showChapters = true
                }
                .foregroundColor(textColor)
                Spacer()
                ToolbarButton(icon: "sun.max", text: "亮度") {
                    coordinator.adjustBrightness()
                }
                .foregroundColor(textColor)
                Spacer()
                ToolbarButton(icon: coordinator.readingMode == .day ? "moon.fill" : "sun.max.fill", text: "深色") {
                    coordinator.toggleReadingMode()
                }
                .foregroundColor(textColor)
                Spacer()
                ToolbarButton(icon: "gearshape", text: "设置") {
                    coordinator.showSettings = true
                }
                .foregroundColor(textColor)
                Spacer()
            }
            .padding(.horizontal)
            .frame(height: 44)
        }
        .background(backgroundColor.opacity(0.8).edgesIgnoringSafeArea(.bottom))
        .frame(height: readerToolbarHeight)
    }
}

/// 工具栏按钮组件
struct ToolbarButton: View {
    let icon: String
    let text: String
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 4) {
                Image(systemName: icon)
                Text(text)
                    .font(.system(size: 10))
            }
        }
    }
}