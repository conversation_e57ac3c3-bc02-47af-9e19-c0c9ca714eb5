//
// ReaderRenderer.swift
// IOSReader
//
// 核心渲染模块，负责文本分页渲染和漫画图像处理
// 主要功能：
// 1. 文本分页渲染 - 使用CoreText实现高性能分页渲染，支持并行处理
// 2. 漫画压缩包解析 - 支持ZIP/RAR等压缩包格式的漫画图像直读
// 3. 双页识别分割 - 自动识别并分割双页漫画为单页
//
// 性能优化点：
// - 并行渲染提高文本分页速度
// - 并发控制处理压缩包内容
// - 智能识别双页漫画

import Foundation
@preconcurrency import CoreText
import ZIPFoundation
import SwiftUI

// FontType 移至 ReaderTypes.swift
typealias ImageType = Image

// 使用@unchecked Sendable标记类，解决在@Sendable闭包中捕获self的警告
class ReaderRenderer: @unchecked Sendable {
    
    /// 获取字符串中所有匹配文本的NSRange
    static func getRangeStr(text: String, findText: String) -> [NSRange] {
        var arrayRanges = [NSRange]()
        guard !findText.isEmpty else {
            return arrayRanges
        }
        
        var rang = (text as NSString).range(of: findText, options: .regularExpression)
        if rang.location != NSNotFound {
            arrayRanges.append(rang)
            
            var location = rang.location + rang.length
            var length = text.count - location
            
            while location < text.count {
                rang = (text as NSString).range(of: findText, options: .regularExpression, range: NSRange(location: location, length: length))
                if rang.location == NSNotFound {
                    break
                }
                arrayRanges.append(rang)
                location = rang.location + rang.length
                length = text.count - location
            }
        }
        return arrayRanges
    }
    
    /// 根据章节标记分割文本
    static func getChapterArrWithString(text: String) -> [String] {
        let chapterRanges = getRangeStr(text: text, findText: "\\n第.{1,}章.*\\r\\n")
        var chapters = [String]()
        var lastRange = NSRange(location: 0, length: 0)
        
        for range in chapterRanges {
            let chapter = (text as NSString).substring(with: NSRange(location: lastRange.location, length: range.location - lastRange.location))
            chapters.append(chapter)
            lastRange = range
        }
        
        // 添加最后一章
        let lastChapter = (text as NSString).substring(from: lastRange.location)
        chapters.append(lastChapter)
        
        return chapters
    }
    
    /// 渲染单个页面
    func renderPage(content: String, pageIndex: Int, font: FontType, pageSize: CGSize) async throws -> ImageType {
        // 分段并验证索引
        let segments = content.components(separatedBy: "\n\n")
        
        // 检查空文档
        guard !segments.isEmpty else {
            throw NSError(domain: "ReaderError", code: -4, userInfo: [
                NSLocalizedDescriptionKey: "渲染失败 - 文档内容为空",
                NSLocalizedFailureReasonErrorKey: "文档不包含任何可渲染内容",
                NSLocalizedRecoverySuggestionErrorKey: "请检查源文档是否包含有效文本内容"
            ])
        }
        
        guard pageIndex >= 0 else {
            throw NSError(domain: "ReaderError", code: -1, userInfo: [
                NSLocalizedDescriptionKey: "渲染失败 - 无效页面索引: \(pageIndex)",
                NSLocalizedFailureReasonErrorKey: "页码不能为负数",
                NSLocalizedRecoverySuggestionErrorKey: "请检查页码是否正确，有效范围是0到\(segments.count - 1)"
            ])
        }
        
        guard pageIndex < segments.count else {
            throw NSError(domain: "ReaderError", code: -1, userInfo: [
                NSLocalizedDescriptionKey: "渲染失败 - 无法渲染页面\(pageIndex + 1): 页面索引越界",
                NSLocalizedFailureReasonErrorKey: "请求的页面\(pageIndex + 1)超出文档范围(共\(segments.count)页)",
                NSLocalizedRecoverySuggestionErrorKey: "请检查页码是否正确，有效范围是0到\(segments.count - 1)"
            ])
        }
        
        // 获取目标段落
        let segment = segments[pageIndex]
        guard !segment.isEmpty else {
            throw NSError(domain: "ReaderError", code: -3, userInfo: [
                NSLocalizedDescriptionKey: "渲染失败 - 无法渲染页面\(pageIndex + 1): 空页面内容",
                NSLocalizedFailureReasonErrorKey: "索引\(pageIndex)处的页面内容为空",
                NSLocalizedRecoverySuggestionErrorKey: "可能是文档格式问题，请尝试重新加载文档或检查源文件"
            ])
        }
        
        // 渲染页面
        return try await withCheckedThrowingContinuation { continuation in // 添加try关键字
            DispatchQueue.global(qos: .userInitiated).async {
                let pages = self.renderTextPages(text: segment, font: font, pageSize: pageSize)
                if let firstPage = pages.first {
                    continuation.resume(returning: firstPage)
                } else {
                    // 创建一个符合 Error 协议的错误类型实例
                    struct RenderPageError: Error, LocalizedError {
                        var errorDescription: String? { "Failed to render page" }
                    }
                    continuation.resume(throwing: RenderPageError())
                }
            }
        }
    }
    
    // MARK: - 文本渲染
    
    /// 使用CoreText实现分页渲染
    func renderTextPages(text: String, font: FontType, pageSize: CGSize) -> [ImageType] {
        // 检查空文本
        guard !text.isEmpty else {
            return []
        }
        
        // 使用并行渲染提高性能
        // 明确使用 ctFont 与 CoreText 交互
        let ctFont = font as CTFont
        let attributedString = NSAttributedString(string: text, attributes: [.font: ctFont])
        let framesetter = CTFramesetterCreateWithAttributedString(attributedString)
        
        var pages: [ImageType] = []
        var currentOffset = 0
        
        // 预计算所有页的范围
        var pageRanges: [CFRange] = []
        while currentOffset < text.count {
            let path = CGPath(rect: CGRect(origin: .zero, size: pageSize), transform: nil)
            let frame = CTFramesetterCreateFrame(framesetter, CFRangeMake(currentOffset, 0), path, nil)
            
            let range = CTFrameGetVisibleStringRange(frame)
            pageRanges.append(range)
            currentOffset += range.length
        }
        
        // 并行渲染页面
        var tempPages = [ImageType?](repeating: nil, count: pageRanges.count)
        DispatchQueue.concurrentPerform(iterations: pageRanges.count) { index in
            let range = pageRanges[index]
            let path = CGPath(rect: CGRect(origin: .zero, size: pageSize), transform: nil)
            let frame = CTFramesetterCreateFrame(framesetter, range, path, nil)
            #if canImport(UIKit)
            let renderer = UIGraphicsImageRenderer(size: pageSize)
            let uiImage = renderer.image { context in
                UIColor.white.setFill()
                context.fill(CGRect(origin: .zero, size: pageSize))
                CTFrameDraw(frame, context.cgContext)
            }
            tempPages[index] = Image(uiImage: uiImage)
            #else
            let nsImage = NSImage(size: pageSize, flipped: false) { rect in
                NSColor.white.setFill()
                rect.fill()
                if let context = NSGraphicsContext.current?.cgContext {
                    CTFrameDraw(frame, context)
                } else {
                    // 处理 context 为 nil 的情况，例如记录错误或返回
                    print("Error: Failed to get graphics context for rendering page.")
                }
                return true
            }
            tempPages[index] = Image(nsImage)
            #endif
        }
        pages = tempPages.compactMap { $0 }
        
        return pages
    }
    
    /// 实时分页计算
    func calculatePageCount(text: String, font: FontType, pageSize: CGSize) -> Int {
        // 明确使用 ctFont 与 CoreText 交互
        let ctFont = font as CTFont
        let attributedString = NSAttributedString(string: text, attributes: [.font: ctFont])
        let framesetter = CTFramesetterCreateWithAttributedString(attributedString)
        
        _ = CFRangeMake(0, 0)
        var pageCount = 0
        var currentOffset = 0
        
        while currentOffset < text.count {
            let path = CGPath(rect: CGRect(origin: .zero, size: pageSize), transform: nil)
            let frame = CTFramesetterCreateFrame(framesetter, CFRangeMake(currentOffset, 0), path, nil)
            
            let range = CTFrameGetVisibleStringRange(frame)
            currentOffset += range.length
            pageCount += 1
        }
        
        return pageCount
    }
    
    // MARK: - 漫画模式
    
    /// ZIP/RAR压缩包直读
    func readComicFromArchive(fileURL: URL) -> [ImageType]? {
        guard let archive = Archive(url: fileURL, accessMode: .read) else {
            print("无法打开压缩文件: \(fileURL.path)")
            return nil
        }

        var collectedImages: [ImageType] = []
        
        // 过滤出图片文件条目，并可以根据需要按名称排序
        let imageEntries = archive.filter { entry in
            guard entry.type == .file else { return false }
            let pathExtension = (entry.path as NSString).pathExtension.lowercased()
            // 常见图片格式后缀
            return ["jpg", "jpeg", "png", "gif", "bmp", "tiff"].contains(pathExtension)
        }.sorted { $0.path < $1.path } // 按路径排序以保证顺序

        for entry in imageEntries {
            do {
                var imageData = Data()
                // 从条目中提取数据
                _ = try archive.extract(entry, consumer: { data in imageData.append(data) })

                #if canImport(UIKit)
                if let uiImage = UIImage(data: imageData) {
                    collectedImages.append(Image(uiImage: uiImage))
                }
                #elseif canImport(AppKit)
                if let nsImage = NSImage(data: imageData) {
                    collectedImages.append(Image(nsImage: nsImage))
                }
                #else
                // 平台不支持 UIImage 或 NSImage
                print("警告: 当前平台不支持 UIImage 或 NSImage，无法加载图片条目: \(entry.path)")
                #endif
            } catch {
                print("处理压缩文件条目失败: \(entry.path), 错误: \(error.localizedDescription)")
                // 选择继续处理下一个条目或抛出错误
            }
        }
        
        return collectedImages.isEmpty ? nil : collectedImages
    }
    
    /// 双页拼接识别
    @MainActor
    func detectAndSplitDoublePages(images: [ImageType]) -> [ImageType] {
        var result: [ImageType] = []
        
        for image in images {
            // 将 SwiftUI.Image 转换为 UIImage 以获取尺寸和进行裁剪
            guard let uiImage = image.asUIImage() else { // 假设存在 asUIImage() 扩展
                result.append(image) // 如果转换失败，保留原始图像
                continue
            }
            let size = uiImage.size
            // 如果宽高比小于1.3，认为是单页
            if size.width / size.height < 1.3 {
                result.append(image)
                continue
            }
            // 分割双页
            let leftRect = CGRect(x: 0, y: 0, width: size.width/2, height: size.height)
            let rightRect = CGRect(x: size.width/2, y: 0, width: size.width/2, height: size.height)
            
            var croppedLeftSwiftUIImage: ImageType? = nil
            if let cgImageLeft = uiImage.cgImage?.cropping(to: leftRect) {
                croppedLeftSwiftUIImage = Image(uiImage: UIImage(cgImage: cgImageLeft))
            }

            var croppedRightSwiftUIImage: ImageType? = nil
            if let cgImageRight = uiImage.cgImage?.cropping(to: rightRect) {
                croppedRightSwiftUIImage = Image(uiImage: UIImage(cgImage: cgImageRight))
            }

            if let leftImage = croppedLeftSwiftUIImage {
                // 检查转换后的 UIImage 是否有有效尺寸，这里我们直接添加转换后的 SwiftUI Image
                // if leftImage.asUIImage()?.size != .zero { // 避免再次转换，直接添加
                result.append(leftImage)
            } else {
                 // result.append(image) // 如果左侧裁剪失败，则不添加，因为右侧可能会添加，或者在循环末尾统一处理
            }

            if let rightImage = croppedRightSwiftUIImage {
                // if rightImage.asUIImage()?.size != .zero {
                result.append(rightImage)
            } else if croppedLeftSwiftUIImage == nil {
                // 如果左右裁剪都失败，则不添加任何内容，或根据需求添加原始图像
            }
            // if croppedLeft?.size != .zero { // 旧的判断方式
                // result.append(croppedLeft ?? image) // 多余且错误的代码
            // }
            // if croppedRight?.size != .zero { // 多余且错误的代码
                // result.append(croppedRight ?? image) // croppedRight 未定义
            // }
            // 如果经过上述处理后，result仍为空（左右分割都失败），则添加原始图像
            if result.isEmpty {
                result.append(image)
            } else if result.count == 1 && croppedLeftSwiftUIImage != nil && croppedRightSwiftUIImage == nil {
                // 如果只有左半边，且原始图像是双页，则可能需要添加右半边（或原始图像）
                // 但当前逻辑是如果左边成功就添加左边，右边成功就添加右边
                // 如果只成功了一个，那么就只添加那一个
            } else if result.count == 1 && croppedLeftSwiftUIImage == nil && croppedRightSwiftUIImage != nil {
                // 同上，如果只有右半边
            } else if result.count == 0 { // 确保在分割失败时至少添加原始图像
                 result.append(image)
            }
            // 保留原始图像作为最后的手段，如果分割后的图像不足两页（意味着分割可能不完全或不适用）
            // 且当前result中没有图像，或者只有一张图像（可能分割不完全）
            if images.count == 1 && result.isEmpty { // 如果输入只有一张图且分割失败
                 result.append(image)
            } else if images.count == 1 && result.count == 1 { // 输入一张，输出一张，可能是单页或分割后的一半
                 // 此时result中已经是处理过的图像，无需再次添加原始图像
            } else if result.isEmpty && images.count > 0 { // 如果输入多张，但分割后一张都没有，则添加第一张原始图像作为回退
                result.append(images[0])
            }
        }
        
        return result
    }
}