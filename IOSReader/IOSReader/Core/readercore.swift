//

import Foundation

/// 阅读器核心处理类
/// 主要功能:
/// 1. 智能分段算法 - 基于段落语义和标点符号进行文本智能分段
/// 2. 正则清理 - 使用正则表达式清理文本中的特定模式
/// 3. 分页算法 - 提供三种分页模式(滚动/覆盖/模拟)将文本分页
@MainActor
class ReaderCore: MemoryCleanable {
    
    /// 处理文本内容
    /// - Parameter text: 需要处理的原始文本
    /// - Returns: 处理后的文本数组
    func processTextContent(_ text: String) -> [String] {
        // 启动内存监控
        MemoryManager.shared.startMonitoring()
        return intelligentSegmentation(text: text)
    }
    
    /// 实现MemoryCleanable协议
    func cleanupMemory() async {
        // 清理缓存的文本内容
        autoreleasepool {
            // 执行内存密集型操作的清理
        }
    }
    
    /// 智能分段算法
    /// - Parameter text: 需要分段的文本
    /// - Returns: 分段后的文本数组
    /// 
    /// 功能说明:
    /// 1. 基于段落语义和标点符号进行智能分段
    /// 2. 优化了正则表达式性能，预编译正则表达式
    /// 3. 处理文本边界情况，确保不丢失内容
    /// 4. 返回非空分段数组，若分段失败则返回原始文本
    func intelligentSegmentation(text: String) -> [String] {
        // 预编译正则表达式，提高性能
        let chapterPattern = "(?m)^\\s*第[0-9一二三四五六七八九十百千]+[章节卷集部篇].*$"
        let paragraphPattern = "(?<=\\n{2}|\\r\\n{2}|[.!?。！？]\\s{1,2})"
        
        do {
            // 先按章节分割
            let chapterRegex = try NSRegularExpression(pattern: chapterPattern)
            let chapterRange = NSRange(location: 0, length: text.utf16.count)
            let chapterMatches = chapterRegex.matches(in: text, range: chapterRange)
            
            var chapters = [String]()
            var lastChapterIndex = text.startIndex
            
            // 处理每个章节
            for match in chapterMatches {
                if let rangeEnd = Range(match.range, in: text) {
                    // 获取章节标题前的内容作为上一章节
                    let chapterContent = String(text[lastChapterIndex..<rangeEnd.lowerBound])
                        .trimmingCharacters(in: .whitespacesAndNewlines)
                    
                    if !chapterContent.isEmpty {
                        chapters.append(chapterContent)
                    }
                    
                    // 保存章节标题
                    let chapterTitle = String(text[rangeEnd])
                        .trimmingCharacters(in: .whitespacesAndNewlines)
                    if !chapterTitle.isEmpty {
                        chapters.append(chapterTitle)
                    }
                    
                    lastChapterIndex = rangeEnd.upperBound
                }
            }
            
            // 处理最后一章
            let lastChapter = String(text[lastChapterIndex...])
                .trimmingCharacters(in: .whitespacesAndNewlines)
            if !lastChapter.isEmpty {
                chapters.append(lastChapter)
            }
            
            // 如果没有找到章节，则按段落分割
            if chapters.isEmpty {
                let paragraphRegex = try NSRegularExpression(pattern: paragraphPattern, options: .anchorsMatchLines)
                let paragraphRange = NSRange(location: 0, length: text.utf16.count)
                let paragraphMatches = paragraphRegex.matches(in: text, range: paragraphRange)
                
                var segments = [String]()
                var lastIndex = text.startIndex
                
                // 如果没有段落匹配，直接返回整个文本
                if paragraphMatches.isEmpty {
                    return [text.trimmingCharacters(in: .whitespacesAndNewlines)].filter { !$0.isEmpty }
                }
                
                for match in paragraphMatches {
                    if let rangeEnd = Range(match.range, in: text) {
                        let segment = String(text[lastIndex..<rangeEnd.lowerBound])
                            .trimmingCharacters(in: .whitespacesAndNewlines)
                        
                        if !segment.isEmpty {
                            segments.append(segment)
                        }
                        lastIndex = rangeEnd.lowerBound
                    }
                }
                
                let remainingSegment = String(text[lastIndex...])
                    .trimmingCharacters(in: .whitespacesAndNewlines)
                if !remainingSegment.isEmpty {
                    segments.append(remainingSegment)
                }
                
                return segments.isEmpty ? [text] : segments
            }
            
            return chapters
        } catch {
            print("正则表达式编译错误: \(error)")
            return [text]
        }
    }
    
    /// 正则表达式清理文本
    /// - Parameters:
    ///   - text: 需要清理的文本
    ///   - patterns: 正则表达式模式数组
    /// - Returns: 清理后的文本
    /// 
    /// 功能说明:
    /// 1. 按顺序应用多个正则模式清理文本
    /// 2. 自动跳过无法编译的正则表达式
    /// 3. 返回清理后的文本，保留原始文本若发生错误
    func cleanTextWithRegex(text: String, patterns: [String]) -> String {
        var result = text
        
        for pattern in patterns {
            do {
                let regex = try NSRegularExpression(pattern: pattern)
                let range = NSRange(location: 0, length: result.utf16.count)
                result = regex.stringByReplacingMatches(
                    in: result,
                    range: range,
                    withTemplate: ""
                )
            } catch {
                print("正则表达式编译错误: \(error)")
                continue
            }
        }
        
        return result
    }
    
    /// 文本分页算法
    /// - Parameters:
    ///   - text: 需要分页的文本
    ///   - pageSize: 每页最大字符数
    ///   - algorithm: 分页算法类型
    /// - Returns: 分页后的文本数组
    /// 
    /// 功能说明:
    /// 1. 支持三种分页模式:
    ///    - scroll: 滚动模式，按固定字符数分页
    ///    - overlay: 覆盖模式，直接使用分段结果
    ///    - simulation: 模拟翻书效果，智能合并段落
    /// 2. 优化内存使用，预分配字符串容量
    /// 3. 处理空文本情况，返回空数组
    func paginateText(text: String, pageSize: Int, algorithm: PaginationAlgorithm) -> [String] {
        // 基本参数验证
        guard !text.isEmpty else { return [] }
        guard pageSize > 0 else { return [text] }
        
        // 处理特别短的文本
        if text.count <= pageSize {
            return [text]
        }
        
        // 获取分段结果，确保至少有一个段落
        let segments = intelligentSegmentation(text: text)
        guard !segments.isEmpty else { return [text] }
        
        var pages = [String]()
        
        switch algorithm {
        case .scroll:
            // 滚动模式：按固定字符数分页，优化内存使用
            var currentPage = ""
            currentPage.reserveCapacity(pageSize * 2)
            
            for segment in segments {
                // 处理超长段落
                if segment.count > pageSize {
                    // 如果当前页不为空，先保存
                    if !currentPage.isEmpty {
                        pages.append(currentPage)
                        currentPage = ""
                    }
                    
                    // 按字符数切分超长段落
                    var start = segment.startIndex
                    while start < segment.endIndex {
                        let remainingDistance = segment.distance(from: start, to: segment.endIndex)
                        let offsetDistance = min(pageSize, remainingDistance)
                        if offsetDistance <= 0 { break } // 防止无效的偏移量
                        
                        // 安全计算结束位置
                        guard let end = segment.index(start, offsetBy: offsetDistance, limitedBy: segment.endIndex) else {
                            break // 如果计算结束位置失败，终止循环
                        }
                        
                        // 确保范围有效
                        if start < end {
                            pages.append(String(segment[start..<end]))
                            start = end
                        } else {
                            break // 防止无限循环
                        }
                    }
                    continue
                }
                
                // 正常段落处理
                if currentPage.isEmpty || currentPage.count + segment.count <= pageSize {
                    currentPage += segment
                } else {
                    pages.append(currentPage)
                    currentPage = segment
                }
            }
            if !currentPage.isEmpty {
                pages.append(currentPage)
            }
            
        case .overlay:
            // 覆盖模式：按段落分页，直接使用分段结果
            pages = segments
            
        case .simulation:
            // 模拟翻书效果：智能合并段落，优化内存分配
            var currentPage = ""
            currentPage.reserveCapacity(pageSize * 2)
            
            for segment in segments {
                // 处理超长段落
                if segment.count > pageSize {
                    // 如果当前页不为空，先保存
                    if !currentPage.isEmpty {
                        pages.append(currentPage)
                        currentPage = ""
                    }
                    
                    // 按字符数切分超长段落
                    var start = segment.startIndex
                    while start < segment.endIndex {
                        let remainingDistance = segment.distance(from: start, to: segment.endIndex)
                        let offsetDistance = min(pageSize, remainingDistance)
                        if offsetDistance <= 0 { break } // 防止无效的偏移量
                        
                        // 安全计算结束位置
                        guard let end = segment.index(start, offsetBy: offsetDistance, limitedBy: segment.endIndex) else {
                            break // 如果计算结束位置失败，终止循环
                        }
                        
                        // 确保范围有效
                        if start < end {
                            pages.append(String(segment[start..<end]))
                            start = end
                        } else {
                            break // 防止无限循环
                        }
                    }
                    continue
                }
                
                // 正常段落处理
                if currentPage.isEmpty || currentPage.count + segment.count <= pageSize {
                    currentPage += segment
                } else {
                    pages.append(currentPage)
                    currentPage = segment
                }
            }
            if !currentPage.isEmpty {
                pages.append(currentPage)
            }
        }
        
        return pages.isEmpty ? [text] : pages
    }
    
    /// 自动修复尝试
    func autoRepair(_ error: Error) async -> Bool {
        switch error {
        case EPUBError.invalidData:
            // 尝试重新解析
            return await retryParsing()
        case EPUBError.missingRootfile:
            // 尝试查找备用文件
            return findAlternativeRootfile()
        default:
            return false
        }
    }
    
    /// 重试解析
    private func retryParsing() async -> Bool {
        // 清理缓存
        await cleanupMemory()
        // 重新加载
        return true
    }
    
    /// 查找备用文件
    private func findAlternativeRootfile() -> Bool {
        // 实现备用文件查找逻辑
        return false
    }
}

enum PaginationAlgorithm {
    case scroll
    case overlay
    case simulation
}