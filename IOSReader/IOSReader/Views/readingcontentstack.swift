import SwiftUI

/// 阅读内容堆栈视图组件
/// 用于显示文本内容的滚动视图
struct ReadingContentStack: View {
    @ObservedObject var viewModel: ReaderViewModel
    
    var body: some View {
        GeometryReader { proxy in
            ScrollViewReader { scrollProxy in
                ScrollView(.vertical) {
                    LazyVStack(alignment: .leading, spacing: viewModel.settings.paragraphSpacing) {
                        if let visibleContent = viewModel.visibleContent {
                            Text(AttributedString(visibleContent))
                                .font(.system(size: viewModel.settings.fontSize))
                                .foregroundColor(viewModel.settings.textColor)
                                .lineSpacing(viewModel.settings.lineSpacing)
                                .padding(.horizontal, viewModel.settings.horizontalPadding)
                                .id(viewModel.currentPage) // 使用页码作为标识符
                        } else {
                            Text("加载中...")
                                .font(.system(size: viewModel.settings.fontSize))
                                .foregroundColor(.secondary)
                                .frame(maxWidth: .infinity, alignment: .center)
                                .padding()
                        }
                    }
                    .padding(.top, safeAreaInsets.top)
                    .padding(.bottom, safeAreaInsets.bottom)
                }
                .onChange(of: viewModel.currentPage) { _, newPage in
                    withAnimation {
                        scrollProxy.scrollTo(newPage, anchor: .top)
                    }
                }
            }
        }
    }
    
    private var safeAreaInsets: EdgeInsets {
        guard let windowScene = UIApplication.shared.connectedScenes.first(where: { $0.activationState == .foregroundActive }) as? UIWindowScene,
              let window = windowScene.windows.first(where: { $0.isKeyWindow }) else {
            // 在无法获取 window 或 windowScene 的情况下（例如 SwiftUI 预览），返回零边距
            return EdgeInsets(top: 0, leading: 0, bottom: 0, trailing: 0)
        }
        return EdgeInsets(
            top: window.safeAreaInsets.top,
            leading: window.safeAreaInsets.left,
            bottom: window.safeAreaInsets.bottom,
            trailing: window.safeAreaInsets.right
        )
    }
}