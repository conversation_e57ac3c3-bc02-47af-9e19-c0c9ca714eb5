//
// TemplateEditorView.swift
// 模板编辑器视图
// 功能：
// 1. 提供模板名称和正则表达式模式的编辑界面
// 2. 保存和取消操作

import SwiftUI

struct TemplateEditorView: View {
    @Binding var name: String
    @Binding var pattern: String
    var onSave: () -> Void
    var onCancel: () -> Void
    
    @State private var tempName: String = ""
    @State private var tempPattern: String = ""
    
    var body: some View {
        NavigationView {
            Form {
                Section(header: Text("模板名称")) {
                    TextField("输入模板名称", text: $tempName)
                }
                
                Section(header: Text("正则表达式")) {
                    TextEditor(text: $tempPattern)
                        .frame(height: 120)
                        .font(.system(.body, design: .monospaced))
                }
            }
            .navigationTitle("编辑模板")
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    But<PERSON>("取消") {
                        onCancel()
                    }
                }
                ToolbarItem(placement: .navigationBarTrailing) {
                    But<PERSON>("保存") {
                        name = tempName
                        pattern = tempPattern
                        onSave()
                    }
                    .disabled(tempName.isEmpty || tempPattern.isEmpty)
                }
            }
            .onAppear {
                tempName = name
                tempPattern = pattern
            }
        }
    }
}

struct TemplateEditorView_Previews: PreviewProvider {
    static var previews: some View {
        TemplateEditorView(
            name: .constant(""),
            pattern: .constant(""),
            onSave: {},
            onCancel: {}
        )
    }
}