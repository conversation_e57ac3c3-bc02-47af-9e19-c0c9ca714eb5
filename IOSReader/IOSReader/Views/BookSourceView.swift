import SwiftUI
import SwiftData

/// 书源管理视图
/// 主要功能:
/// 1. 展示用户的书源列表
/// 2. 提供书源的添加、编辑和删除功能
/// 3. 支持书源有效性测试
/// 主要UI组件:
/// - 书源列表视图
/// - 书源操作按钮(添加/编辑/删除)
/// - 书源测试功能界面

struct BookSourceView: View {
    @EnvironmentObject var coordinator: ReaderCoordinator
    @State private var showingAddSheet = false
    @State private var showingEditSheet = false
    @State private var selectedSource: BookSource? = nil
    @State private var showingDeleteAlert = false
    @State private var showingTestResult = false
    @State private var testResultMessage = ""
    // 用于存储不同分类的书源
    @State private var categorizedSources: [BookSourceCategory: [BookSource]] = [:]
    @State private var selectedCategory: BookSourceCategory = .novel // 默认选中小说分类
    
    // 加载分类书源
    private func loadCategorizedSources() {
        var newCategorizedSources: [BookSourceCategory: [BookSource]] = [:]
        
        // 初始化所有分类的空数组
        for category in BookSourceCategory.allCases {
            newCategorizedSources[category] = []
        }
        
        // 将书源按分类分组
        for source in coordinator.bookSources {
            if let category = BookSourceCategory(rawValue: source.category) {
                newCategorizedSources[category]?.append(source)
            }
        }
        
        // 更新状态
        categorizedSources = newCategorizedSources
    }
    
    // 添加书源的选项
    enum AddSourceOption: String, CaseIterable, Identifiable {
        case local = "本地导入"
        case network = "网络URL"
        case clipboard = "剪切板"
        var id: String { self.rawValue }
    }
    @State private var showingAddSourceOptions = false

    var body: some View {
        VStack {
            Picker("书源分类", selection: $selectedCategory) {
                ForEach(BookSourceCategory.allCases) { category in
                    Text(category.rawValue).tag(category)
                }
            }
            .pickerStyle(SegmentedPickerStyle())
            .padding(.horizontal)

            List {
                if let sources = categorizedSources[selectedCategory], !sources.isEmpty {
                    ForEach(sources) { source in
                        bookSourceRow(source: source)
                    }
                    .onDelete(perform: deleteBookSources)
                } else {
                    emptySourceViewForCategory
                }
            }
        }
        .navigationTitle("书源管理")
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                Button(action: { showingAddSourceOptions = true }) {
                    Image(systemName: "plus")
                }
            }
        }
        .onAppear(perform: loadCategorizedSources) // 视图出现时加载分类书源
        .actionSheet(isPresented: $showingAddSourceOptions) { // 添加书源选项
            ActionSheet(title: Text("添加书源"), message: Text("请选择添加方式"), buttons: AddSourceOption.allCases.map { option in
                ActionSheet.Button.default(Text(option.rawValue)) { // 使用 ActionSheet.Button
                    // 根据选项执行操作，例如打开不同的添加表单
                    // 这里暂时都指向同一个添加表单，后续可以扩展
                    showingAddSheet = true 
                }
            } + [ActionSheet.Button.cancel()]) // 使用 ActionSheet.Button
        }
        .sheet(isPresented: $showingAddSheet) {
            NavigationView {
                AddEditSourceFormView(isEdit: false, dismissAction: {
                    showingAddSheet = false
                })
                .environmentObject(coordinator)
                .navigationTitle("添加书源")
                .toolbar {
                    ToolbarItem(placement: .navigationBarLeading) {
                        Button("取消") {
                            showingAddSheet = false
                        }
                    }
                }
            }
        }
        .sheet(isPresented: $showingEditSheet, onDismiss: { selectedSource = nil }) {
            if let source = selectedSource {
                NavigationView {
                    AddEditSourceFormView(source: source, isEdit: true, dismissAction: {
                        showingEditSheet = false
                    })
                    .environmentObject(coordinator)
                    .navigationTitle("编辑书源")
                    .toolbar {
                        ToolbarItem(placement: .navigationBarLeading) {
                            Button("取消") {
                                showingEditSheet = false
                            }
                        }
                    }
                }
            }
        }
        .alert("测试结果", isPresented: $showingTestResult) {
            Button("确定", role: .cancel) {}
        } message: {
            Text(testResultMessage)
        }
        .alert("确认删除", isPresented: $showingDeleteAlert) {
            Button("取消", role: .cancel) {}
            Button("删除", role: .destructive) {
                // 直接修改 ObservedObject 的属性
                if let source = selectedSource,
                   let index = coordinator.bookSources.firstIndex(where: { $0.name == source.name }) {
                    coordinator.bookSources.remove(at: index)
                }
            }
        } message: {
            Text("确定要删除这个书源吗？此操作不可撤销。")
        }
    }
    
    // 特定分类的空书源视图
    private var emptySourceViewForCategory: some View {
        VStack(spacing: 20) {
            Image(systemName: "doc.text.magnifyingglass")
                .font(.system(size: 50))
                .foregroundColor(.gray)
            Text("该分类下还没有书源")
                .font(.title2.bold())
            Text("你可以切换分类查看，或添加新的书源到当前分类。")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)

            Button {
                showingAddSourceOptions = true
            } label: {
                Label("立即添加书源", systemImage: "plus.circle.fill")
            }
            .buttonStyle(.borderedProminent)
            .padding(.top, 20)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .padding()
        .listRowSeparator(.hidden)
    }

    // 所有分类都为空时的视图
    private var emptySourceView: some View {
        VStack(spacing: 20) {
            Image(systemName: "tray.and.arrow.down.fill") // 更换一个更合适的图标
                .font(.system(size: 50))
                .foregroundColor(.gray)
            Text("书源空空如也")
                .font(.title2.bold())
            Text("添加一些书源，开始你的阅读之旅吧！你可以从以下方式添加：")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
            
            VStack(alignment: .leading, spacing: 10) {
                Text("• 本地文件导入 (.txt, .json等)")
                Text("• 网络URL链接 (例如：https://...)")
                Text("• 从剪切板粘贴书源信息")
            }
            .font(.callout)
            .padding(.top, 5)

            Button {
                showingAddSourceOptions = true // 点击按钮显示添加选项
            } label: {
                Label("立即添加书源", systemImage: "plus.circle.fill")
            }
            .buttonStyle(.borderedProminent)
            .padding(.top, 20)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .padding()
        .background(Color(.systemGroupedBackground))
        .listRowSeparator(.hidden)
    }
    
    // 书源行视图
    private func bookSourceRow(source: BookSource) -> some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text(source.name)
                    .font(.headline)
                    .foregroundColor(coordinator.currentBookSource?.id == source.id ? .accentColor : .primary) // 高亮选中的书源
                Text(source.url)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            // 书源显隐控制的眼睛图标
            Button {
                toggleSourceVisibility(source)
            } label: {
                Image(systemName: source.isEnabled ? "eye.fill" : "eye.slash.fill")
                    .foregroundColor(source.isEnabled ? .blue : .gray)
            }
            .buttonStyle(.borderless) // 移除按钮边框，使其看起来更像一个图标
            
            Menu {
                Button(action: {
                    // 直接赋值给 ObservedObject 的属性
                    coordinator.currentBookSource = source
                }) {
                    Label("设为默认", systemImage: "checkmark")
                }
                
                Button(action: {
                    selectedSource = source
                    showingEditSheet = true
                }) {
                    Label("编辑", systemImage: "pencil")
                }
                
                Button(action: {
                    testBookSource(source)
                }) {
                    Label("测试", systemImage: "wrench")
                }
                
                Button(action: {
                    selectedSource = source
                    showingDeleteAlert = true
                }) {
                    Label("删除", systemImage: "trash")
                        .foregroundColor(.red)
                }
            } label: {
                Image(systemName: "ellipsis.circle")
                    .foregroundColor(.gray)
            }
        }
        .contentShape(Rectangle())
        .onTapGesture {
            coordinator.currentBookSource = source
        }
    }
    

    
    // 删除书源
    private func deleteBookSources(at offsets: IndexSet) {
        guard let sources = categorizedSources[selectedCategory] else { return }
        let sourcesToDelete = offsets.map { sources[$0] }
        for sourceToDelete in sourcesToDelete {
            coordinator.modelContainer.mainContext.delete(sourceToDelete)
        }
        try? coordinator.modelContainer.mainContext.save()
        loadCategorizedSources() // 删除后重新加载
    }
    

    
    // 切换书源的显示状态
    private func toggleSourceVisibility(_ source: BookSource) {
        source.isEnabled.toggle()
        try? coordinator.modelContainer.mainContext.save()
        // 无需手动调用 loadCategorizedSources()，因为 categorizedSources 会通过 @State 自动更新视图
        // 如果遇到视图不更新的问题，可以取消下一行的注释
        // objectWillChange.send()
    }

    // 添加示例书源 (如果需要，可以保留或移除)
    // private func addExampleSource() {
    //     let exampleSource = BookSource(name: "示例小说书源", url: "https://example.com/novels", category: .novel, isEnabled: true)
    //     coordinator.modelContainer.mainContext.insert(exampleSource)
    //     try? coordinator.modelContainer.mainContext.save()
    //     loadCategorizedSources()
    // }
    
    // 添加新书源 (已在 AddEditSourceFormView 中处理，此处可移除或保留为辅助方法)
    // private func addNewBookSource(name: String, url: String, category: BookSource.Category = .novel) {
    //     let newSource = BookSource(name: name, url: url, category: category, isEnabled: true)
    //     coordinator.modelContainer.mainContext.insert(newSource)
    //     try? coordinator.modelContainer.mainContext.save()
    //     loadCategorizedSources()
    // }
    
    // 更新现有书源 (已在 AddEditSourceFormView 中处理，此处可移除或保留为辅助方法)
    // private func updateBookSource(_ source: BookSource, newName: String, newUrl: String, newCategory: BookSource.Category) {
    //     source.name = newName
    //     source.url = newUrl
    //     source.category = newCategory
    //     try? coordinator.modelContainer.mainContext.save()
    //     loadCategorizedSources()
    // }
    
    // 测试书源
    private func testBookSource(_ source: BookSource) {
        // 模拟测试过程
        testResultMessage = "测试成功：书源 \(source.name) 可以正常访问"
        showingTestResult = true
    }
}

// ViewBookSource已被BookSource替代，使用SwiftData的BookSource类型

struct AddEditSourceFormView: View {
    @EnvironmentObject var coordinator: ReaderCoordinator
    @State private var sourceName: String
    @State private var sourceUrl: String
    @State private var selectedCategory: BookSourceCategory
    
    var source: BookSource?
    var isEdit: Bool
    var dismissAction: () -> Void
    
    init(source: BookSource? = nil, isEdit: Bool, dismissAction: @escaping () -> Void) {
        self.source = source
        self.isEdit = isEdit
        self.dismissAction = dismissAction
        _sourceName = State(initialValue: source?.name ?? "")
        _sourceUrl = State(initialValue: source?.url ?? "")
        _selectedCategory = State(initialValue: BookSourceCategory(rawValue: source?.category ?? BookSourceCategory.novel.rawValue) ?? .novel) // 默认为小说
    }
    
    var body: some View {
        Form {
            Section(header: Text("基本信息")) {
                TextField("书源名称", text: $sourceName)
                TextField("书源URL", text: $sourceUrl)
                Picker("书源分类", selection: $selectedCategory) {
                    ForEach(BookSourceCategory.allCases) { category in
                        Text(category.rawValue).tag(category)
                    }
                }
            }
            
            Section(header: Text("解析规则")) {
                Text("规则编辑功能待更新") // 临时占位符
            }
            
            Section {
                Button(isEdit ? "保存修改" : "添加书源") {
                    if isEdit, let sourceToEdit = source {
                        // 更新现有书源
                        sourceToEdit.name = sourceName
                        sourceToEdit.url = sourceUrl
                        sourceToEdit.category = selectedCategory.rawValue // 更新分类
                        try? coordinator.modelContainer.mainContext.save()
                    } else {
                        // 添加新书源，并传入分类
                        let newSource = BookSource(name: sourceName, url: sourceUrl, category: selectedCategory.rawValue, isEnabled: true)
                        coordinator.modelContainer.mainContext.insert(newSource)
                        try? coordinator.modelContainer.mainContext.save()
                    }
                    dismissAction()
                }
                .frame(maxWidth: .infinity)
                .buttonStyle(.borderedProminent)
                .disabled(sourceName.isEmpty || sourceUrl.isEmpty)
            }
        }
    }
}

struct BookSourceView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationView {
            // 使用Group避免控制流语句问题
            Group {
                let schema = Schema([
                    Book.self,
                    Bookmark.self,
                    Item.self,
                    BookSource.self
                ])
                let configuration = ModelConfiguration(schema: schema, isStoredInMemoryOnly: true)
                let container = try! ModelContainer(for: schema, configurations: [configuration])
                let coordinator = ReaderCoordinator(modelContainer: container)
                
                BookSourceView().environmentObject(coordinator)
                    .modelContainer(container)
            }
        }
    }
}