import SwiftUI
import SwiftData // 导入 SwiftData 以使用 Schema, ModelConfiguration, ModelContainer

/// 章节列表视图
/// 核心功能:
/// 1. 显示书籍的所有章节列表
/// 2. 允许用户点击章节跳转到对应阅读位置
///
/// 主要UI组件:
/// - 章节列表 (List)
/// - 导航栏 (NavigationView)
///
/// 状态管理:
/// - 使用 @ObservedObject 接收 ReaderViewModel
/// - 使用 @Environment 关闭视图
struct ChapterListView: View {
    // 使用 ObservedObject 接收 ViewModel
    @ObservedObject var viewModel: ReaderViewModel

    @Environment(\.presentationMode) var presentationMode

    var body: some View {
        NavigationView {
            List {
                // 遍历章节
                ForEach(viewModel.chapters, id: \.id) { chapter in // 明确指定id
                    ChapterRowView(chapter: chapter, viewModel: viewModel, presentationMode: presentationMode)
                }
                }
            }
            .navigationTitle("目录")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        presentationMode.wrappedValue.dismiss()
                    }
                }
            }
        }
    }


// 章节行视图组件
struct ChapterRowView: View {
    let chapter: Chapter
    let viewModel: ReaderViewModel
    let presentationMode: Binding<PresentationMode>
    
    var body: some View {
        // 使用章节标题，如果为空则提供默认值
        let chapterTitle = chapter.title.isEmpty ? "第 \(chapter.index + 1) 章" : chapter.title
        
        Button(chapterTitle) {
            // 调用 viewModel 的方法跳转到章节
            Task {
                await viewModel.jumpToChapter(chapter.index)
            }
            // 选择后关闭工作表
            presentationMode.wrappedValue.dismiss()
        }
        .foregroundColor(.primary) // 确保文本颜色合适
    }
}

#Preview {
    Group {
        if let container = try? ModelContainer(for: User.self, Book.self, BookSource.self, Bookmark.self, ReadingProgress.self) {
            let previewViewModel = {
                let vm = ReaderViewModel(modelContainer: container)
                let bookIdForPreview = UUID()
                let previewChapters = [
                    Chapter(index: 0, title: "第一章预览", startPosition: 0, content: "内容...", bookId: bookIdForPreview),
                    Chapter(index: 1, title: "第二章预览", startPosition: 100, content: "内容...", bookId: bookIdForPreview)
                ]
                vm.chapters = previewChapters
                vm.currentChapterIndex = 0
                return vm
            }()
            
            NavigationView {
                ChapterListView(viewModel: previewViewModel)
            }
        } else {
            Text("预览错误")
        }
    }
}