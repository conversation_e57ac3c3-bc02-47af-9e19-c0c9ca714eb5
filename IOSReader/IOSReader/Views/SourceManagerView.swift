import SwiftUI
import SwiftData
import UniformTypeIdentifiers
#if canImport(UIKit)
import UIKit
#endif

// 书源状态枚举
enum SourceStatus: String, CaseIterable {
    case all = "全部"
    case enabled = "已启用"
    case disabled = "已禁用"
    case normal = "正常"
    case error = "有异常"
    case untested = "未检测"
}

// 书源管理视图 - 用于管理书源的导入、检测、配置等功能
struct SourceManagerView: View {
    @EnvironmentObject var coordinator: ReaderCoordinator
    @Environment(\.dismiss) private var dismiss

    // 搜索和筛选状态
    @State private var searchText = ""
    @State private var selectedSourceType = BookSourceCategory.novel // 默认选中小说
    @State private var selectedStatus = SourceStatus.all // 状态筛选
    let sourceTypes = BookSourceCategory.allCases // 可选的分类

    // 导入相关状态
    @State private var showImportOptions = false
    @State private var showFileImporter = false
    @State private var showURLInput = false
    @State private var urlInput = ""
    @State private var importProgress = 0.0
    @State private var isImporting = false
    @State private var importResult: String?

    // 批量操作状态
    @State private var isInSelectionMode = false
    @State private var selectedSources: Set<String> = []

    // 检测相关状态
    @State private var showDetectionSheet = false
    @State private var detectionProgress = 0.0
    @State private var isDetecting = false

    // 搜索栏
    private var searchBar: some View {
        HStack {
            Image(systemName: "magnifyingglass")
                .foregroundColor(.secondary)

            TextField("搜索书源", text: $searchText)
                .textFieldStyle(RoundedBorderTextFieldStyle())
        }
        .padding(.horizontal)
    }

    // 分类选择器
    private var sourceTypePicker: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 12) {
                ForEach(sourceTypes, id: \.self) { type in
                    Button(action: {
                        selectedSourceType = type
                    }) {
                        Text(type.localizedName)
                            .font(.system(size: 14, weight: .medium))
                            .padding(.horizontal, 16)
                            .padding(.vertical, 8)
                            .background(
                                RoundedRectangle(cornerRadius: 20)
                                    .fill(selectedSourceType == type ? Color.blue : Color.gray.opacity(0.2))
                            )
                            .foregroundColor(selectedSourceType == type ? .white : .primary)
                    }
                }
            }
            .padding(.horizontal)
        }
    }

    // 状态筛选器
    private var statusFilter: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 8) {
                ForEach(SourceStatus.allCases, id: \.self) { status in
                    Button(action: {
                        selectedStatus = status
                    }) {
                        HStack(spacing: 4) {
                            if status != .all {
                                Circle()
                                    .fill(statusColor(for: status))
                                    .frame(width: 6, height: 6)
                            }
                            Text(status.rawValue)
                                .font(.system(size: 12))
                        }
                        .padding(.horizontal, 12)
                        .padding(.vertical, 6)
                        .background(
                            RoundedRectangle(cornerRadius: 15)
                                .fill(selectedStatus == status ? Color.blue.opacity(0.2) : Color.clear)
                        )
                        .foregroundColor(selectedStatus == status ? .blue : .secondary)
                    }
                }
            }
            .padding(.horizontal)
        }
    }

    // 筛选后的书源列表
    private var filteredSources: [BookSource] {
        coordinator.sourceManager.bookSources.filter { source in
            // 分类筛选
            let categoryMatch = source.category == selectedSourceType.rawValue

            // 搜索文本筛选
            let searchMatch = searchText.isEmpty ||
                             source.name.localizedCaseInsensitiveContains(searchText) ||
                             source.url.localizedCaseInsensitiveContains(searchText)

            // 状态筛选
            let statusMatch: Bool
            switch selectedStatus {
            case .all:
                statusMatch = true
            case .enabled:
                statusMatch = source.enabled
            case .disabled:
                statusMatch = !source.enabled
            case .normal:
                statusMatch = source.enabled // 简化：启用的视为正常
            case .error:
                statusMatch = !source.enabled // 简化：禁用的视为异常
            case .untested:
                statusMatch = source.respondTime == nil // 未检测的
            }

            return categoryMatch && searchMatch && statusMatch
        }
    }

    // 统计信息
    private var statisticsView: some View {
        HStack {
            Text("共计: \(coordinator.sourceManager.bookSources.count)")
            Spacer()
            Text("正常: \(enabledSourcesCount)")
            Spacer()
            Text("无效: \(disabledSourcesCount)")
            Spacer()
            Text("未检测: \(untestedSourcesCount)")
        }
        .font(.caption)
        .foregroundColor(.secondary)
        .padding(.horizontal)
        .padding(.vertical, 4)
    }

    // 计算属性
    private var enabledSourcesCount: Int {
        coordinator.sourceManager.bookSources.filter { $0.enabled }.count
    }

    private var disabledSourcesCount: Int {
        coordinator.sourceManager.bookSources.filter { !$0.enabled }.count
    }

    private var untestedSourcesCount: Int {
        coordinator.sourceManager.bookSources.filter { $0.respondTime == nil }.count
    }

    // 底部操作栏
    private var bottomToolbar: some View {
        VStack(spacing: 0) {
            if isInSelectionMode {
                // 选择模式下的操作栏
                HStack {
                    Button("全选") {
                        if selectedSources.count == filteredSources.count {
                            selectedSources.removeAll()
                        } else {
                            selectedSources = Set(filteredSources.map { $0.id.uuidString })
                        }
                    }
                    .disabled(filteredSources.isEmpty)

                    Spacer()

                    Button("启用") {
                        batchToggleSources(enable: true)
                    }
                    .disabled(selectedSources.isEmpty)

                    Spacer()

                    Button("禁用") {
                        batchToggleSources(enable: false)
                    }
                    .disabled(selectedSources.isEmpty)

                    Spacer()

                    Button("检测") {
                        batchDetectSources()
                    }
                    .disabled(selectedSources.isEmpty)

                    Spacer()

                    Button("删除", role: .destructive) {
                        batchDeleteSources()
                    }
                    .disabled(selectedSources.isEmpty)
                }
                .padding()
                .background(Color(UIColor.systemBackground))
                .overlay(
                    Rectangle()
                        .frame(height: 0.5)
                        .foregroundColor(Color(UIColor.separator)),
                    alignment: .top
                )
            } else {
                // 正常模式下的操作栏
                HStack(spacing: 16) {
                    Button(action: { showImportOptions = true }) {
                        VStack(spacing: 4) {
                            Image(systemName: "plus.circle")
                            Text("导入")
                                .font(.caption)
                        }
                    }

                    Button(action: { showDetectionSheet = true }) {
                        VStack(spacing: 4) {
                            Image(systemName: "network")
                            Text("检测")
                                .font(.caption)
                        }
                    }

                    Button(action: { exportSources() }) {
                        VStack(spacing: 4) {
                            Image(systemName: "square.and.arrow.up")
                            Text("导出")
                                .font(.caption)
                        }
                    }

                    Button(action: { toggleSelectionMode() }) {
                        VStack(spacing: 4) {
                            Image(systemName: "checkmark.circle")
                            Text("管理")
                                .font(.caption)
                        }
                    }
                }
                .padding()
                .background(Color(UIColor.systemBackground))
                .overlay(
                    Rectangle()
                        .frame(height: 0.5)
                        .foregroundColor(Color(UIColor.separator)),
                    alignment: .top
                )
            }
        }
    }

    private var sourceListSection: some View {
        List {
            ForEach(filteredSources) { source in
                SourceManagementRow(
                    source: source,
                    isInSelectionMode: isInSelectionMode,
                    isSelected: selectedSources.contains(source.id.uuidString)
                ) { isSelected in
                    if isSelected {
                        selectedSources.insert(source.id.uuidString)
                    } else {
                        selectedSources.remove(source.id.uuidString)
                    }
                }
            }
            .onDelete(perform: deleteSources)
        }
    }

    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // 搜索栏
                searchBar
                    .padding(.top, 8)

                // 分类选择器
                sourceTypePicker
                    .padding(.vertical, 8)

                // 状态筛选器
                statusFilter
                    .padding(.bottom, 8)

                // 统计信息
                statisticsView

                // 进度指示器
                if isImporting {
                    ProgressView("导入中...", value: importProgress, total: 1.0)
                        .padding()
                }

                if isDetecting {
                    ProgressView("检测中...", value: detectionProgress, total: 1.0)
                        .padding()
                }

                // 书源列表
                sourceListSection

                // 底部操作栏
                bottomToolbar
            }
            .navigationTitle("源名/URL")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button(action: { dismiss() }) {
                        Image(systemName: "chevron.left")
                    }
                }
                ToolbarItem(placement: .navigationBarTrailing) {
                    if isInSelectionMode {
                        Button("完成") {
                            toggleSelectionMode()
                        }
                    } else {
                        Menu {
                            Button("导出书源") {
                                exportSources()
                            }
                            Button("备份设置") {
                                backupSettings()
                            }
                            Button("全局设置") {
                                // TODO: 打开全局设置页面
                            }
                        } label: {
                            Image(systemName: "ellipsis.circle")
                        }
                    }
                }
            }
        }
        .sheet(isPresented: $showImportOptions) {
            ImportOptionsSheet(
                showFileImporter: $showFileImporter,
                showURLInput: $showURLInput,
                urlInput: $urlInput,
                onImportFromClipboard: importFromClipboard
            )
        }
        .fileImporter(
            isPresented: $showFileImporter,
            allowedContentTypes: [.json, .text],
            allowsMultipleSelection: true
        ) { result in
            handleFileImport(result)
        }
        .alert("导入结果", isPresented: .constant(importResult != nil)) {
            Button("确定") {
                importResult = nil
            }
        } message: {
            if let result = importResult {
                Text(result)
            }
        }
        .sheet(isPresented: $showDetectionSheet) {
            DetectionOptionsSheet(
                onDetectUntested: { detectUntestedSources() },
                onDetectFailed: { detectFailedSources() },
                onDetectAll: { detectAllSources() }
            )
        }
    }

    // MARK: - 功能方法

    private func toggleSelectionMode() {
        isInSelectionMode.toggle()
        if !isInSelectionMode {
            selectedSources.removeAll()
        }
    }

    private func deleteSources(at offsets: IndexSet) {
        let sourcesToDelete = offsets.map { filteredSources[$0] }
        for source in sourcesToDelete {
            coordinator.sourceManager.deleteSource(source)
        }
    }

    private func batchDetectSources() {
        let sources = filteredSources.filter { selectedSources.contains($0.id.uuidString) }
        performDetection(sources: sources)
    }

    private func batchToggleSources(enable: Bool) {
        let sources = filteredSources.filter { selectedSources.contains($0.id.uuidString) }
        for source in sources {
            source.isEnabled = enable
        }
        // 保存到数据库
        try? coordinator.modelContainer.mainContext.save()
    }

    private func batchDeleteSources() {
        let sources = filteredSources.filter { selectedSources.contains($0.id.uuidString) }
        for source in sources {
            coordinator.sourceManager.deleteSource(source)
        }
        selectedSources.removeAll()
    }

    private func detectUntestedSources() {
        let untestedSources = coordinator.sourceManager.bookSources.filter { $0.respondTime == nil }
        performDetection(sources: untestedSources)
    }

    private func detectFailedSources() {
        let failedSources = coordinator.sourceManager.bookSources.filter { !$0.enabled }
        performDetection(sources: failedSources)
    }

    private func detectAllSources() {
        performDetection(sources: coordinator.sourceManager.bookSources)
    }

    private func performDetection(sources: [BookSource]) {
        guard !sources.isEmpty else { return }

        isDetecting = true
        detectionProgress = 0.0

        Task { @Sendable in
            for (index, source) in sources.enumerated() {
                // TODO: 实现实际的书源检测逻辑
                // 这里只是模拟检测过程
                try? await Task.sleep(nanoseconds: 100_000_000) // 0.1秒

                await MainActor.run {
                    // 模拟检测结果
                    source.respondTime = Int.random(in: 100...2000)
                    detectionProgress = Double(index + 1) / Double(sources.count)
                }
            }

            await MainActor.run {
                isDetecting = false
                try? coordinator.modelContainer.mainContext.save()
            }
        }
    }

    private func statusColor(for status: SourceStatus) -> Color {
        switch status {
        case .enabled, .normal:
            return .green
        case .disabled, .error:
            return .red
        case .untested:
            return .orange
        case .all:
            return .clear
        }
    }

    private func importFromURL(_ url: String) {
        guard !url.isEmpty, let urlObj = URL(string: url) else {
            importResult = "URL格式错误"
            return
        }

        isImporting = true
        importProgress = 0.0

        Task { @Sendable in
            do {
                await MainActor.run {
                    importProgress = 0.2
                }

                // 从URL下载数据
                let (data, _) = try await URLSession.shared.data(from: urlObj)

                await MainActor.run {
                    importProgress = 0.6
                }

                // 调用BookSourceManager的导入方法
                try await coordinator.sourceManager.importBookSources(from: data)

                await MainActor.run {
                    importProgress = 1.0
                    isImporting = false
                    let successCount = coordinator.sourceManager.importSuccessCount
                    let failureCount = coordinator.sourceManager.importFailureCount
                    importResult = "从URL导入完成：成功 \(successCount) 个，失败 \(failureCount) 个"
                }
            } catch {
                await MainActor.run {
                    isImporting = false
                    importResult = "从URL导入失败: \(error.localizedDescription)"
                }
            }
        }
    }

    private func importFromClipboard() {
        guard let content = UIPasteboard.general.string else {
            importResult = "剪贴板内容为空"
            return
        }

        isImporting = true
        importProgress = 0.0

        Task { @Sendable in
            do {
                // 尝试解析剪贴板内容为JSON
                guard let jsonData = content.data(using: .utf8) else {
                    await MainActor.run {
                        isImporting = false
                        importResult = "剪贴板内容格式错误"
                    }
                    return
                }

                await MainActor.run {
                    importProgress = 0.3
                }

                // 调用BookSourceManager的导入方法
                try await coordinator.sourceManager.importBookSources(from: jsonData)

                await MainActor.run {
                    importProgress = 1.0
                    isImporting = false
                    let successCount = coordinator.sourceManager.importSuccessCount
                    let failureCount = coordinator.sourceManager.importFailureCount
                    importResult = "从剪贴板导入完成：成功 \(successCount) 个，失败 \(failureCount) 个"
                }
            } catch {
                await MainActor.run {
                    isImporting = false
                    importResult = "从剪贴板导入失败: \(error.localizedDescription)"
                }
            }
        }
    }

    private func handleFileImport(_ result: Result<[URL], Error>) {
        switch result {
        case .success(let urls):
            isImporting = true
            importProgress = 0.0

            Task { @Sendable in
                var totalSuccessCount = 0
                var totalFailureCount = 0
                let totalCount = urls.count

                for (index, url) in urls.enumerated() {
                    do {
                        // 确保可以访问文件
                        _ = url.startAccessingSecurityScopedResource()
                        defer { url.stopAccessingSecurityScopedResource() }

                        let jsonData = try Data(contentsOf: url)

                        // 调用BookSourceManager的导入方法
                        try await coordinator.sourceManager.importBookSources(from: jsonData)

                        totalSuccessCount += coordinator.sourceManager.importSuccessCount
                        totalFailureCount += coordinator.sourceManager.importFailureCount
                    } catch {
                        print("导入文件失败: \(error)")
                        totalFailureCount += 1
                    }

                    await MainActor.run {
                        importProgress = Double(index + 1) / Double(totalCount)
                    }
                }

                await MainActor.run {
                    isImporting = false
                    importResult = "文件导入完成：成功 \(totalSuccessCount) 个，失败 \(totalFailureCount) 个"
                }
            }

        case .failure(let error):
            importResult = "导入失败: \(error.localizedDescription)"
        }
    }

    private func exportSources() {
        // TODO: 实现导出书源功能
        print("导出书源")
    }

    private func backupSettings() {
        // TODO: 实现备份设置功能
        print("备份设置")
    }
}

// 检测选项弹窗
struct DetectionOptionsSheet: View {
    let onDetectUntested: () -> Void
    let onDetectFailed: () -> Void
    let onDetectAll: () -> Void
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                Text("因书源兼容问题，建议先用前检测，或边搜书边检测。")
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal)

                Button("检测未测书源") {
                    onDetectUntested()
                    dismiss()
                }
                .buttonStyle(.bordered)
                .frame(maxWidth: .infinity)

                Button("检测未测+失效书源") {
                    onDetectFailed()
                    dismiss()
                }
                .buttonStyle(.bordered)
                .frame(maxWidth: .infinity)

                Button("检测全部书源") {
                    onDetectAll()
                    dismiss()
                }
                .buttonStyle(.bordered)
                .frame(maxWidth: .infinity)

                Spacer()
            }
            .padding()
            .navigationTitle("检测书源")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("关闭") {
                        dismiss()
                    }
                }
            }
        }
    }
}

// 导入选项弹窗
struct ImportOptionsSheet: View {
    @Binding var showFileImporter: Bool
    @Binding var showURLInput: Bool
    @Binding var urlInput: String
    let onImportFromClipboard: () -> Void
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                Button("从文件导入") {
                    showFileImporter = true
                    dismiss()
                }
                .buttonStyle(.bordered)
                .frame(maxWidth: .infinity)

                Button("从URL导入") {
                    showURLInput = true
                    dismiss()
                }
                .buttonStyle(.bordered)
                .frame(maxWidth: .infinity)

                Button("从剪贴板导入") {
                    onImportFromClipboard()
                    dismiss()
                }
                .buttonStyle(.bordered)
                .frame(maxWidth: .infinity)

                Spacer()
            }
            .padding()
            .navigationTitle("导入书源")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("取消") {
                        dismiss()
                    }
                }
            }
        }
        .sheet(isPresented: $showURLInput) {
            URLImportSheet(
                urlInput: $urlInput,
                onImport: { url in
                    self.importFromURL(url)
                }
            )
        }
    }
}

// MARK: - URL Import Sheet
struct URLImportSheet: View {
    @Binding var urlInput: String
    let onImport: (String) -> Void
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                Text("从URL导入书源")
                    .font(.title2)
                    .fontWeight(.semibold)

                Text("请输入书源的URL地址")
                    .font(.subheadline)
                    .foregroundColor(.secondary)

                TextField("https://example.com/sources.json", text: $urlInput)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    .autocorrectionDisabled()

                HStack(spacing: 15) {
                    Button("取消") {
                        urlInput = ""
                        dismiss()
                    }
                    .foregroundColor(.secondary)

                    Button("导入") {
                        onImport(urlInput)
                        urlInput = ""
                        dismiss()
                    }
                    .disabled(urlInput.isEmpty)
                    .buttonStyle(.borderedProminent)
                }

                Spacer()
            }
            .padding()
#if os(iOS)
            .toolbar(.hidden, for: .navigationBar)
#endif
        }
        .presentationDetents([.height(300)])
    }
}

// 书源管理行视图
struct SourceManagementRow: View {
    let source: BookSource
    let isInSelectionMode: Bool
    let isSelected: Bool
    let onSelectionChanged: (Bool) -> Void
    @EnvironmentObject var coordinator: ReaderCoordinator

    var body: some View {
        HStack {
            if isInSelectionMode {
                Button(action: {
                    onSelectionChanged(!isSelected)
                }) {
                    Image(systemName: isSelected ? "checkmark.circle.fill" : "circle")
                        .foregroundColor(isSelected ? .blue : .gray)
                }
            }

            VStack(alignment: .leading, spacing: 4) {
                HStack {
                    Text(source.name)
                        .font(.headline)

                    Spacer()

                    // 书源状态指示器
                    HStack(spacing: 4) {
                        Circle()
                            .fill(source.enabled ? .green : .red)
                            .frame(width: 8, height: 8)

                        if let respondTime = source.respondTime {
                            Text("\(respondTime)ms")
                                .font(.caption2)
                                .foregroundColor(.secondary)
                        }
                    }
                }

                Text(source.url)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .lineLimit(1)

                // 书源统计信息
                HStack {
                    Text("分类: \(source.category)")
                    Spacer()
                    if let group = source.sourceGroup, !group.isEmpty {
                        Text("分组: \(group)")
                        Spacer()
                    }
                    Text("更新: \(formatDate(source.lastUpdated))")
                }
                .font(.caption2)
                .foregroundColor(.secondary)
            }

            if !isInSelectionMode {
                VStack {
                    // 启用/禁用按钮
                    Button(action: {
                        coordinator.sourceManager.toggleSource(source)
                    }) {
                        Image(systemName: source.enabled ? "eye" : "eye.slash")
                            .foregroundColor(source.enabled ? .blue : .gray)
                    }

                    // 检测按钮
                    Button(action: {
                        detectSource()
                    }) {
                        Image(systemName: "network")
                            .foregroundColor(.orange)
                    }
                }
            }
        }
        .padding(.vertical, 4)
        .contentShape(Rectangle())
        .onTapGesture {
            if !isInSelectionMode {
                // TODO: 进入书源详情页面
                print("点击书源: \(source.name)")
            }
        }
    }

    private func detectSource() {
        // TODO: 实现单个书源检测功能
        print("检测书源: \(source.name)")
    }

    private func formatDate(_ date: Date?) -> String {
        guard let date = date else { return "未知" }
        let formatter = DateFormatter()
        formatter.dateStyle = .short
        return formatter.string(from: date)
    }
}

#Preview {
    Group {
        if let container = try? ModelContainer(for: BookSource.self, Book.self, Chapter.self, Bookmark.self, User.self, ReadingProgress.self) {
            SourceManagerView()
                .environmentObject(ReaderCoordinator(modelContainer: container))
        } else {
            Text("预览错误")
        }
    }
}
