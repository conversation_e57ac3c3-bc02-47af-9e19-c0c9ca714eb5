//  DictionaryView.swift
//  IOSReader
//
//  字典规则管理视图，负责自定义词典条目的增删改查、词性标注及权重调节。
//  主要功能：
//  - 支持用户自定义词典内容
//  - 词性标注与优先级设置
//  - 词典数据与阅读器联动
//
import SwiftUI
import SwiftData
// import IOSReader // 移除模块导入，因为在同一模块内

struct DictionaryView: View {
    @EnvironmentObject private var coordinator: ReaderCoordinator
    
    var body: some View {
        //TODO: 实现字典规则的管理界面
        Text("字典规则功能")
            .navigationTitle("字典规则")
    }
}

#Preview {
    Group {
        if let container = try? ModelContainer(for: User.self, Book.self, BookSource.self, Bookmark.self, ReadingProgress.self) {
            NavigationStack {
                DictionaryView()
            }
            .modelContainer(container)
            .environmentObject(ReaderCoordinator(modelContainer: container))
        } else {
            Text("预览错误")
        }
    }
}