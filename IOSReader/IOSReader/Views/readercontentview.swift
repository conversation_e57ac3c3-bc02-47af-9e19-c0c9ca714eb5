import SwiftUI

/// 阅读器内容视图
/// 负责显示书籍内容和相关调试信息
struct ReaderContentView: View {
    // 改为接收 Coordinator
    @ObservedObject var coordinator: ReaderCoordinator
    var textColor: Color
    
    // 添加状态变量用于调试
    @State private var contentDebugInfo: String = "等待内容..."
    @State private var showTestText: Bool = false // 用于显示测试文本
    
    var body: some View {
        let _ = print("ReaderContentView body: 渲染 ReaderContentView") // 调试日志
        let viewModel = coordinator.readerViewModel
        let _ = print("ReaderContentView body: isLoading=\(viewModel.isLoading), visibleContent is nil=\(viewModel.visibleContent == nil), visibleContent empty=\(viewModel.visibleContent?.string.isEmpty ?? true), visibleContent length=\(viewModel.visibleContent?.length ?? -1), errorMessage=\(viewModel.errorMessage ?? "nil")")
        
        GeometryReader { geometry in
            VStack(spacing: 0) {
                // 调试信息显示
                if showTestText {
                    Text("测试文本 - 如果您能看到这段文字，说明视图渲染正常")
                        .font(.headline)
                        .foregroundColor(.red)
                        .padding()
                        .background(Color.yellow.opacity(0.3))
                        .cornerRadius(8)
                        .padding(.top)
                }
                
                // 内容调试信息
                Text(contentDebugInfo)
                    .font(.caption)
                    .foregroundColor(.blue)
                    .padding(.horizontal)
                    .frame(maxWidth: .infinity, alignment: .leading)
                
                // 使用 UITextView 显示富文本内容
                if let content = viewModel.visibleContent {
                    AttributedTextView(attributedText: content)
                        .frame(maxWidth: .infinity, maxHeight: .infinity)
                        .id(viewModel.currentPage)
                        .background(Color.clear)
                        .clipped()
                        .onAppear {
                            contentDebugInfo = "内容长度: \(content.length), 页码: \(viewModel.currentPage + 1)/\(viewModel.totalPages)"
                            print("ReaderContentView.onAppear: 显示内容长度 = \(content.length), 当前页 = \(viewModel.currentPage + 1)")
                            
                            if content.length > 0 {
                                let attributes = content.attributes(at: 0, effectiveRange: nil)
                                if let color = attributes[.foregroundColor] as? UIColor {
                                    var alpha: CGFloat = 0
                                    color.getWhite(nil, alpha: &alpha)
                                    contentDebugInfo += ", 文本颜色alpha: \(alpha)"
                                    print("ReaderContentView.onAppear: 文本颜色alpha = \(alpha)")
                                } else {
                                    contentDebugInfo += ", 无文本颜色"
                                    print("ReaderContentView.onAppear: 无文本颜色属性")
                                }
                            }
                            
                            showTestText = true
                            DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
                                showTestText = false
                            }
                        }
                } else if viewModel.isLoading {
                    ProgressView("加载中...")
                        .onAppear {
                            contentDebugInfo = "状态: 加载中..."
                            print("ReaderContentView.onAppear: 显示加载中状态")
                        }
                } else {
                    Text("无法加载内容或内容为空")
                        .foregroundColor(.gray)
                        .onAppear {
                            contentDebugInfo = "状态: 无内容, needsPagination=\(viewModel.needsPagination), totalPages=\(viewModel.totalPages)"
                            print("ReaderContentView.onAppear: 无法加载内容, viewModel.visibleContent = nil")
                            
                            showTestText = true
                        }
                }
            }
        }
    }
}
