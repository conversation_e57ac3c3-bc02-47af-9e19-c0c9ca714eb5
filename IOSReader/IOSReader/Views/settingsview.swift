/// 设置主视图
/// 功能:
/// - 提供应用设置和配置选项
/// - 包含三个主要功能区域:
///   1. 核心功能: 
///      - 书源管理: 管理阅读源配置
///      - TXT目录规则: 自定义TXT文件目录解析规则
///      - 替换净化: 文本替换和净化规则
///      - 字典规则: 自定义字典和术语管理
///   2. 阅读辅助: 
///      - 书签: 管理阅读书签
///      - 阅读记录: 查看阅读历史和进度
///      - 文件管理: 管理本地和远程文件
///   3. 系统设置: 
///      - 主题模式: 日间/夜间模式切换
///      - Web服务: 配置Web服务选项
///      - 备份恢复: 数据备份和恢复功能
///      - 主题设置: 自定义应用主题
/// - 主要UI组件:
///   - NavigationView: 组织设置页面导航
///   - List: 显示设置选项列表
///   - Section: 分组设置选项
///   - NavigationLink: 跳转到子设置页面
///   - Form控件: 各种表单输入控件
/// - 通过ReaderCoordinator管理全局状态

import SwiftUI
import SwiftData

#if canImport(UIKit)
import UIKit
import UniformTypeIdentifiers
#else
import AppKit
#endif

struct SettingsView: View {
    // 使用 EnvironmentObject 注入 coordinator
    @EnvironmentObject var coordinator: ReaderCoordinator
    @Environment(\.colorScheme) private var colorScheme: ColorScheme
    @Environment(\.modelContext) private var modelContext
    
    // 账号管理器
    @StateObject private var accountManager: AccountManager
    
    // 状态管理
    @State private var showingImportAlert = false
    @State private var importError: Error?
    @State private var showingDocumentPicker = false // 添加缺失的 @State 变量
    @State private var showingNetworkImportAlert = false // 添加缺失的 @State 变量
    @State private var networkImportURL = "" // 添加缺失的 @State 变量
    @State private var showingLogin = false
    @State private var showingAccountManagement = false
    
    // 初始化
    init() {
        let container = try! ModelContainer(for: User.self)
        self._accountManager = StateObject(wrappedValue: AccountManager(modelContainer: container))
    }
    
    // 表单样式
    #if canImport(UIKit)
    private let formBackground = Color(UIColor.secondarySystemBackground).opacity(0.8)
    #else
    private let formBackground = Color(NSColor.windowBackgroundColor).opacity(0.8)
    #endif
    private let sectionHeaderFont = Font.system(.title3).weight(.semibold)
    
    // 使用SwiftData的BookSource类型
    
    // MARK: - 子视图
    
    private var appSettingsSection: some View {
        Section(header: Text("应用设置").font(sectionHeaderFont)) {
            HStack {
                Text("字体大小").frame(width: 80, alignment: .leading)
                Slider(value: $coordinator.fontSize, in: 12...24, step: 1)
                Text("\(Int(coordinator.fontSize))")
                    .frame(width: 30, alignment: .trailing)
                    .font(.system(.body, design: .monospaced))
            }
            
            ColorPicker("主题颜色", selection: $coordinator.themeColor)
            
            Picker("阅读模式", selection: $coordinator.readingMode) {
                ForEach(ReadingMode.allCases, id: \.self) { mode in
                    Text(mode.rawValue.localizedCapitalized)
                        .tag(mode)
                }
            }
            .pickerStyle(SegmentedPickerStyle())
        }
        .listRowBackground(formBackground)
    }
    
    private var emptyBookSourceView: some View {
        VStack(spacing: 16) {
            Image(systemName: "books.vertical")
                .font(.system(size: 40))
                .foregroundColor(.accentColor)
                .symbolEffect(.bounce)
            Text("暂无书源")
                .font(.title2.bold())
                .padding(.top, 8)
            Text("点击下方按钮导入书源")
                .font(.subheadline)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 24)
        .contentShape(Rectangle())
        .onTapGesture {
            Task { [coordinator] in // Explicitly capture coordinator
                do {
                    // 获取 URL 数据并导入
                    if let url = URL(string: "https://legado.aoaostar.com/sources/b778fe6b.json") { // 使用实际的 URL
                        let (data, _) = try await URLSession.shared.data(from: url)
                        try await coordinator.sourceManager.importBookSources(from: data)
                    } else {
                        print("无效的书源 URL")
                        // 可以添加错误处理逻辑
                    }
                } catch {
                    await MainActor.run {
                        importError = error
                        showingImportAlert = true
                    }
                }
            }
        }
    }
    
    private var bookSourceList: some View {
        ForEach(coordinator.sourceManager.bookSources) { source in
            HStack {
                Text(source.name)
                Spacer()
                // 切换启用状态的 Toggle
                Toggle("", isOn: Binding(
                    get: { source.isEnabled },
                    set: { newValue in
                        // 异步切换状态
                        Task {
                            await coordinator.sourceManager.toggleBookSourceEnabled(source)
                        }
                    }
                ))
                .labelsHidden()
            }
            .contentShape(Rectangle())
            // 移除 onTapGesture，因为切换由 Toggle 处理
        }
        .onDelete { indices in
            // 获取要删除的书源
            let sourcesToDelete = indices.map { coordinator.sourceManager.bookSources[coordinator.sourceManager.bookSources.index(coordinator.sourceManager.bookSources.startIndex, offsetBy: $0)] }
            // 异步删除
            Task {
                for source in sourcesToDelete {
                    do {
                        try await coordinator.sourceManager.deleteBookSource(by: source.id)
                    } catch {
                        print("删除书源失败: \(error)")
                        // 可以显示错误提示
                    }
                }
            }
        }
    }
    
    private var importBookSourceButton: some View {
        Button(action: {
            // 触发文件选择器
            showingDocumentPicker = true
        }) {
            Label("从文件导入书源", systemImage: "doc.badge.plus")
        }
    }

    // 添加网络导入按钮
    private var importFromNetworkButton: some View {
        Button(action: {
            showingNetworkImportAlert = true
        }) {
            Label("从网络导入书源", systemImage: "network.badge.shield.half.filled")
        }
    }

    // 添加 GitHub 同步按钮
    private var syncWithGitHubButton: some View {
        Button(action: {
            Task {
                // 假设 BookSourceManager 有 syncWithGitHub 方法
                // try await coordinator.sourceManager.syncWithGitHub()
                print("GitHub 同步功能待实现")
                // 可以在这里显示一个提示
                // 移除了不可达的 do-catch 块
            }
        }) {
            Label("同步 GitHub 书源", systemImage: "arrow.triangle.2.circlepath.icloud")
        }
    }
    
    private var testSourceButton: some View {
        Button(action: {
            // TODO: 实现书源测试功能
        }) {
            Label("测试书源", systemImage: "wrench.and.screwdriver")
                .frame(maxWidth: .infinity)
        }
        .buttonStyle(.bordered)
        .controlSize(.large)
    }
    
    private var importErrorAlert: some View {
        alert("导入错误", isPresented: $showingImportAlert, presenting: importError) { _ in
            Button("确定", role: .cancel) {}
        } message: { error in
            VStack {
                Text("导入书源失败")
                    .font(.headline)
                Text(error.localizedDescription)
                    .padding(.top, 4)
            }
        }
    }

    // MARK: - Body Sections (Refactored for Compiler Performance)

    @ViewBuilder
    private var coreFeaturesSection: some View {
        Section(header: Text("核心功能").font(sectionHeaderFont)) {
            // 正确传递coordinator参数
            NavigationLink(destination: BookSourceView()) {
                Label("书源管理", systemImage: "books.vertical")
            }
            .buttonStyle(PlainButtonStyle()) // 确保点击正常工作
            
            NavigationLink(destination: TXTRuleView()) {
                Label("TXT目录规则", systemImage: "text.justify")
            }
            
            NavigationLink(destination: ReplacementView()) {
                Label("替换净化", systemImage: "wand.and.stars")
            }
            
            NavigationLink(destination: DictionaryView()) {
                Label("字典规则", systemImage: "character.book.closed")
            }
        }
    }

    @ViewBuilder
    private var systemSettingsSection: some View {
        Section(header: Text("系统设置").font(sectionHeaderFont)) {
            NavigationLink(destination: ThemeModeView()) {
                Label("主题模式", systemImage: "moon")
            }
            
            NavigationLink(destination: WebServiceView()) {
                Label("Web服务", systemImage: "network")
            }
            
            NavigationLink(destination: BackupView()) {
                Label("备份与恢复", systemImage: "arrow.triangle.2.circlepath")
            }
            
            NavigationLink(destination: ThemeSettingsView()) {
                Label("主题设置", systemImage: "paintpalette")
            }
        }
    }

    @ViewBuilder
    private var accountSection: some View {
        Section(header: Text("用户账户").font(sectionHeaderFont)) {
            if accountManager.isLoggedIn, let user = accountManager.currentUser {
                // 已登录状态
                Button(action: {
                    showingAccountManagement = true
                }) {
                    HStack(spacing: 12) {
                        // 用户头像
                        Circle()
                            .fill(
                                LinearGradient(
                                    colors: [.blue, .purple],
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                )
                            )
                            .frame(width: 50, height: 50)
                            .overlay(
                                Text(user.nickname.prefix(1).uppercased())
                                    .font(.title2.bold())
                                    .foregroundColor(.white)
                            )
                        
                        VStack(alignment: .leading, spacing: 4) {
                            Text(user.nickname)
                                .font(.headline)
                                .foregroundColor(.primary)
                            
                            HStack(spacing: 6) {
                                Image(systemName: "crown.fill")
                                    .foregroundColor(.orange)
                                    .font(.caption)
                                
                                Text(user.userLevel.description)
                                    .font(.subheadline)
                                    .foregroundColor(.secondary)
                            }
                        }
                        
                        Spacer()
                        
                        Image(systemName: "chevron.right")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .padding(.vertical, 4)
                }
                .buttonStyle(PlainButtonStyle())
                
                // 账号状态信息
                HStack {
                    VStack {
                        Text("331")
                            .font(.title2.bold())
                            .foregroundColor(.primary)
                        Text("阅读时长")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    Spacer()
                    
                    VStack {
                        Text("13")
                            .font(.title2.bold())
                            .foregroundColor(.primary)
                        Text("收藏书籍")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    Spacer()
                    
                    VStack {
                        Text(user.accountStatus.rawValue)
                             .font(.subheadline.weight(.medium))
                             .foregroundColor(user.accountStatus == .active ? .green : .orange)
                        Text("账号状态")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                .padding(.vertical, 8)
                
                // VIP状态（如果是VIP用户）
                 if user.userLevel == .vip {
                    HStack {
                        Image(systemName: "crown.fill")
                            .foregroundColor(.orange)
                        
                        Text("VIP会员")
                            .font(.subheadline.weight(.medium))
                            .foregroundColor(.orange)
                        
                        Text("2025-06-07到期")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        Spacer()
                        
                        Button("续费") {
                            // TODO: 实现续费功能
                        }
                        .font(.caption)
                        .padding(.horizontal, 12)
                        .padding(.vertical, 4)
                        .background(Color.orange.opacity(0.2))
                        .foregroundColor(.orange)
                        .cornerRadius(8)
                    }
                    .padding(.vertical, 4)
                }
            } else {
                // 未登录状态
                Button(action: {
                    showingLogin = true
                }) {
                    HStack(spacing: 12) {
                        Circle()
                            .fill(Color.gray.opacity(0.3))
                            .frame(width: 50, height: 50)
                            .overlay(
                                Image(systemName: "person.fill")
                                    .font(.title2)
                                    .foregroundColor(.gray)
                            )
                        
                        VStack(alignment: .leading, spacing: 4) {
                            Text("点击登录")
                                .font(.headline)
                                .foregroundColor(.primary)
                            
                            Text("登录后享受更多功能")
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                        }
                        
                        Spacer()
                        
                        Image(systemName: "chevron.right")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .padding(.vertical, 4)
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
    }
    
    @ViewBuilder
    private var readingAuxiliarySection: some View {
        Section(header: Text("阅读辅助").font(sectionHeaderFont)) {
            NavigationLink(destination: BookmarkView()) {
                Label("书签", systemImage: "bookmark")
            }
            
            NavigationLink(destination: ReadingRecordView()) {
                Label("阅读记录", systemImage: "chart.bar")
            }
            
            NavigationLink(destination: FileManagerView()) {
                Label("文件管理", systemImage: "folder")
            }
        }
    }
    
    @ViewBuilder
    var body: some View {
        NavigationView {
            List {
                accountSection
                coreFeaturesSection
                systemSettingsSection
                readingAuxiliarySection
            }
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Text("我的").font(.headline)
                }
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: {}) {
                        Image(systemName: "ellipsis")
                    }
                }
            }
            // .onAppear block removed as initialization is handled in BookSourceManager's init or is no longer needed externally
        }
        .environmentObject(accountManager)
        .sheet(isPresented: $showingLogin) {
            LoginView()
        }
        .sheet(isPresented: $showingAccountManagement) {
            AccountManagementView()
        }
        // 将修饰符移到 NavigationView 上，确保在 body 内部
        .fileImporter(
            isPresented: $showingDocumentPicker,
            allowedContentTypes: [.json, .text], // 允许导入 JSON 和 TXT 文件
            allowsMultipleSelection: false
        ) { result in
            switch result {
            case .success(let urls):
                guard let url = urls.first else { return }
                // 异步处理文件导入
                Task {
                    do {
                        // 确保我们可以访问 URL
                        guard url.startAccessingSecurityScopedResource() else {
                            print("无法访问本地文件 URL: \(url)")
                            await MainActor.run {
                                importError = ImportError.fileAccessDenied
                                showingImportAlert = true
                            }
                            return
                        }
                        defer { url.stopAccessingSecurityScopedResource() }

                        let data = try Data(contentsOf: url)
                        try await coordinator.sourceManager.importBookSources(from: data)
                        print("成功从本地文件导入书源: \(url.lastPathComponent)")
                    } catch {
                        await MainActor.run {
                            importError = error
                            showingImportAlert = true
                        }
                    }
                }
            case .failure(let error):
                print("文件选择失败: \(error.localizedDescription)")
                // 可以显示错误提示
                Task {
                    await MainActor.run {
                        importError = error
                        showingImportAlert = true
                    }
                }
            }
        }
        // 网络导入弹窗
        .alert("从网络导入", isPresented: $showingNetworkImportAlert) {
            TextField("输入书源 URL", text: $networkImportURL)
                .keyboardType(.URL)
                .autocapitalization(.none)
            Button("导入") {
                guard let url = URL(string: networkImportURL) else {
                    print("无效的网络 URL")
                    // 显示错误提示
                    Task {
                        await MainActor.run {
                            importError = ImportError.invalidURL
                            showingImportAlert = true
                        }
                    }
                    return
                }
                // 异步处理网络导入
                Task {
                    do {
                        let (data, _) = try await URLSession.shared.data(from: url)
                        try await coordinator.sourceManager.importBookSources(from: data)
                        print("成功从网络导入书源: \(url.absoluteString)")
                    } catch {
                        await MainActor.run {
                            importError = error
                            showingImportAlert = true
                        }
                    }
                }
            }
            Button("取消", role: .cancel) {}
        } message: {
            Text("请输入包含书源列表的 JSON 或 TXT 文件的 URL。")
        }
        // 导入错误弹窗
        .alert("导入错误", isPresented: $showingImportAlert, presenting: importError) { _ in
            Button("确定", role: .cancel) {}
        } message: { error in
            Text(error.localizedDescription)
        }
    } // End of body

    // 将 ImportError 移到 SettingsView 结构体内部，但在 body 之外
    enum ImportError: Error, LocalizedError {
        case invalidURL
        case fileAccessDenied

        var errorDescription: String? {
            switch self {
            case .invalidURL:
                return "输入的 URL 无效。"
            case .fileAccessDenied:
                return "无法访问所选文件。"
            }
        }
    }
} // End of SettingsView struct

import SwiftData // 确保导入 SwiftData

#Preview {
    Group {
        if let container = try? ModelContainer(for: User.self, Book.self, BookSource.self, Bookmark.self, ReadingProgress.self) {
            SettingsView()
                .environmentObject(ReaderCoordinator(modelContainer: container))
        } else {
            Text("预览错误")
        }
    }
}


// 移除这些在顶层的修饰符和定义
/*
    // 文件选择器
    .fileImporter(
        isPresented: $showingDocumentPicker,
        allowedContentTypes: [.json, .text], // 允许导入 JSON 和 TXT 文件
        allowsMultipleSelection: false
    ) { result in
        switch result {
        case .success(let urls):
            guard let url = urls.first else { return }
            // 异步处理文件导入
            Task {
                do {
                    // 确保我们可以访问 URL
                    guard url.startAccessingSecurityScopedResource() else {
                        print("无法访问本地文件 URL: \(url)")
                        await MainActor.run {
                            importError = ImportError.fileAccessDenied
                            showingImportAlert = true
                        }
                        return
                    }
                    defer { url.stopAccessingSecurityScopedResource() }

                    let data = try Data(contentsOf: url)
                    try await coordinator.sourceManager.importBookSources(from: data)
                    print("成功从本地文件导入书源: \(url.lastPathComponent)")
                } catch {
                    await MainActor.run {
                        importError = error
                        showingImportAlert = true
                    }
                }
            }
        case .failure(let error):
            print("文件选择失败: \(error.localizedDescription)")
            // 可以显示错误提示
            Task {
                await MainActor.run {
                    importError = error
                    showingImportAlert = true
                }
            }
        }
    }
    // 网络导入弹窗
    .alert("从网络导入", isPresented: $showingNetworkImportAlert) {
        TextField("输入书源 URL", text: $networkImportURL)
            .keyboardType(.URL)
            .autocapitalization(.none)
        Button("导入") {
            guard let url = URL(string: networkImportURL) else {
                print("无效的网络 URL")
                // 显示错误提示
                Task {
                    await MainActor.run {
                        importError = ImportError.invalidURL
                        showingImportAlert = true
                    }
                }
                return
            }
            // 异步处理网络导入
            Task {
                do {
                    let (data, _) = try await URLSession.shared.data(from: url)
                    try await coordinator.sourceManager.importBookSources(from: data)
                    print("成功从网络导入书源: \(url.absoluteString)")
                } catch {
                    await MainActor.run {
                        importError = error
                        showingImportAlert = true
                    }
                }
            }
        }
        Button("取消", role: .cancel) {}
    } message: {
        Text("请输入包含书源列表的 JSON 或 TXT 文件的 URL。")
    }
    // 导入错误弹窗
    .alert("导入错误", isPresented: $showingImportAlert, presenting: importError) { _ in
        Button("确定", role: .cancel) {}
    } message: { error in
        Text(error.localizedDescription)
    }
}

// 添加导入错误类型
enum ImportError: Error, LocalizedError {
    case invalidURL
    case fileAccessDenied

    var errorDescription: String? {
        switch self {
        case .invalidURL:
            return "输入的 URL 无效。"
        case .fileAccessDenied:
            return "无法访问所选文件。"
        }
    }
}
*/