//  AddBookView.swift
/// 添加书籍视图
/// 核心功能:
//  添加书籍视图，负责书籍信息录入、本地文件导入及远程URL添加，支持多种电子书格式的导入与校验。
//  主要功能：
//  - 书名、作者等基本信息输入
//  - 本地文件选择与类型校验
//  - 远程电子书URL添加
//  - 导航栏操作与导入流程提示
/// 
/// 主要UI组件:
/// - 基本信息表单(书名/作者输入框)
/// - 添加方式选择(本地文件导入按钮/远程URL输入框)
/// - 导航栏操作按钮(取消/完成)

import SwiftUI

struct AddBookView: View {
    @EnvironmentObject var coordinator: ReaderCoordinator // 修改：使用 @EnvironmentObject 并确保 ReaderCoordinator 符合 ObservableObject
    @Environment(\.presentationMode) var presentationMode
    
    @State private var bookTitle = ""
    @State private var author = ""
    @State private var bookURL = ""
    @State private var isImporting = false
    @State private var selectedFiles: [URL] = [] // 修改：从单个文件改为文件数组
    @State private var showAlert = false
    @State private var alertMessage = ""
    @State private var selectedTab = 0 // 0: 本地书籍, 1: 网络书籍
    // Remove cover image related state variables
    // @State private var coverImage: UIImage?
    // @State private var coverImageURL = ""
    // @State private var isImportingCover = false
    
    // 支持的文件类型列表
    private let supportedFileExtensions = [".txt", ".pdf", ".epub", ".html", ".htm"] // 移除了 .mobi
    
    // 验证文件类型是否支持
    private func isFileTypeSupported(_ url: URL) -> Bool {
        let fileExtension = url.pathExtension.lowercased()
        return supportedFileExtensions.contains("."+fileExtension)
    }
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // 标签页切换
                Picker("添加方式", selection: $selectedTab) {
                    Text("本地书籍").tag(0)
                    Text("网络书籍").tag(1)
                }
                .pickerStyle(SegmentedPickerStyle())
                .padding()
                
                TabView(selection: $selectedTab) {
                    // 本地书籍标签页
                    localBookForm
                        .tag(0)
                    
                    // 网络书籍标签页
                    networkBookForm
                        .tag(1)
                }
                .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
            }
            .navigationTitle("添加书籍")
            .alert(isPresented: $showAlert) {
                Alert(title: Text("提示"),
                      message: Text(alertMessage),
                      dismissButton: .default(Text("确定")))
            }
            .navigationBarItems(
                leading: Button("取消") {
                    presentationMode.wrappedValue.dismiss()
                },
                trailing: Button("完成") {
                    if selectedTab == 0 { // 本地书籍
                        if !selectedFiles.isEmpty { // 检查文件数组是否为空
                            var duplicateCount = 0
                            var unsupportedCount = 0
                            var tasks: [Task<Void, Error>] = []

                            for url in selectedFiles {
                                if isFileTypeSupported(url) {
                                    let title = url.deletingPathExtension().lastPathComponent
                                    let bookType = url.pathExtension.lowercased()
                                    if !coordinator.isBookDuplicate(url: url, title: title, bookType: bookType) {
                                        let task = Task {
                                            try await coordinator.addBook(url: url, title: title)
                                        }
                                        tasks.append(task)
                                    } else {
                                        duplicateCount += 1 // 同步更新重复计数
                                    }
                                } else {
                                    unsupportedCount += 1 // 同步更新不支持计数
                                }
                            }

                            // 创建父Task等待所有书籍添加任务完成
                            Task {
                                var addedCount = 0
                                var failedCount = 0 // 新增：记录失败计数
                                for task in tasks {
                                    do {
                                        try await task.value // 等待任务完成
                                        addedCount += 1 // 任务成功，增加成功计数
                                    } catch {
                                        failedCount += 1 // 任务失败，增加失败计数
                                        print("Failed to add book: \(error)")
                                    }
                                }

                                // 所有任务处理完毕
                                var messageParts: [String] = []
                                if addedCount > 0 {
                                    messageParts.append("成功导入 \(addedCount) 本书籍。")
                                }
                                if duplicateCount > 0 {
                                    messageParts.append("\(duplicateCount) 本书籍已存在。")
                                }
                                if unsupportedCount > 0 {
                                    messageParts.append("\(unsupportedCount) 个文件格式不支持。")
                                }
                                if failedCount > 0 {
                                    messageParts.append("\(failedCount) 本书籍导入失败。") // 新增：失败提示
                                }

                                if messageParts.isEmpty {
                                    if !selectedFiles.isEmpty {
                                        alertMessage = "未导入任何书籍。请检查文件有效性或稍后重试。"
                                    } else {
                                        alertMessage = "请选择要导入的书籍文件！"
                                    }
                                } else {
                                    alertMessage = messageParts.joined(separator: " ")
                                }
                                
                                showAlert = true
                                
                                // 只有成功添加了书籍才关闭视图
                                if addedCount > 0 {
                                    // 延迟关闭，让用户可以看到提示
                                    DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
                                        presentationMode.wrappedValue.dismiss()
                                    }
                                }
                            }
                        } else { // selectedFiles.isEmpty
                            alertMessage = "请选择要导入的书籍文件！"
                            showAlert = true
                        }
                    } else { // 网络书籍
                        if !bookURL.isEmpty {
                            if let url = URL(string: bookURL) {
                                // 对于网络书籍，bookType 通常在下载后确定，或者默认为 txt/html
                                // isBookDuplicate 检查时，如果 bookType 未知，可以不传或传一个通用类型
                                // 假设网络书籍先尝试作为通用类型检查，具体类型在下载后确定
                                let bookType = url.pathExtension.isEmpty ? "html" : url.pathExtension.lowercased() // 尝试从URL获取，否则默认为html
                                if !coordinator.isBookDuplicate(url: url, title: bookTitle, bookType: bookType) { // 传递 bookType
                                    Task {
                                        do {
                                            // addNetworkBook 内部会处理下载和 Book 对象的创建，包括 bookType
                                            try await coordinator.addNetworkBook(url: url, title: bookTitle, author: author)
                                            // 成功添加后，可以给一个成功提示，或者直接关闭
                                            alertMessage = "网络书籍添加成功！"
                                            showAlert = true
                                            DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) { // 延迟关闭让用户看到提示
                                                presentationMode.wrappedValue.dismiss()
                                            }
                                        } catch {
                                            print("Failed to add network book: \(error)")
                                            alertMessage = "添加网络书籍失败：\(error.localizedDescription)"
                                            showAlert = true
                                        }
                                    }
                                } else {
                                    alertMessage = "书架已存在相同书籍，请先删除书架书籍"
                                    showAlert = true
                                }
                            } else {
                                alertMessage = "请输入有效的URL地址！"
                                showAlert = true
                            }
                        } else {
                            alertMessage = "请输入书籍URL地址！"
                            showAlert = true
                        }
                    }
                }
            )
            .fileImporter(
                isPresented: $isImporting,
                allowedContentTypes: [.data],
                allowsMultipleSelection: true // 修改：允许多选
            ) { result in
                do {
                    let fileURLs = try result.get() // 修改：获取所有选中的URL
                    var unsupportedFilesFound = false
                    selectedFiles.removeAll() // 清空之前的选择
                    bookTitle = "" // 清空书名，因为是批量导入
                    author = "" // 清空作者
                    
                    for url in fileURLs {
                        if isFileTypeSupported(url) {
                            selectedFiles.append(url) // 添加支持的文件
                        } else {
                            unsupportedFilesFound = true
                        }
                    }
                    
                    if unsupportedFilesFound {
                        alertMessage = "部分文件格式不支持（TXT、PDF、EPUB、HTML…），已自动忽略。"
                        showAlert = true
                    }
                    
                    // 如果只选择了一个文件，可以自动填充书名（可选）
                    if selectedFiles.count == 1 {
                        bookTitle = selectedFiles.first!.deletingPathExtension().lastPathComponent
                    }
                    
                } catch {
                    print("文件选择错误: ", error.localizedDescription)
                    alertMessage = "文件选择失败，请重试。"
                    showAlert = true
                }
            }
        }
    }
    
    // MARK: - 子视图
    
    // 本地书籍表单
    private var localBookForm: some View {
        Form {
            Section(header: Text("本地文件")) {
                Button(action: { isImporting = true }) {
                    Label("导入本地文件", systemImage: "folder")
                }
            }
           Section(header: Text("基本信息")) {
                // 批量导入时，书名和作者通常不在此处单独设置，除非用户只选了一个文件
                if selectedFiles.count <= 1 {
                    TextField("书名", text: $bookTitle)
                    TextField("作者", text: $author)
                } else {
                    Text("书名和作者将根据文件名自动填充")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                    
                // Remove cover image selection UI
                // var relativePath: String? = nil // 注释掉未使用的变量

                /*
                if coverImage != nil {
                   Image(uiImage: coverImage!)
                            .resizable()
                            .scaledToFit()
                            .frame(height: 150)
                }
                    
                Button(action: { isImportingCover = true }) {
                    Label("选择封面图片", systemImage: "photo")
                }
                    
                TextField("封面图片URL", text: $coverImageURL)
                        .keyboardType(.URL)
                        .textContentType(.URL)
                        .autocapitalization(.none)
                        .disableAutocorrection(true)
                 */
                }
                
                // 显示已选文件数量
                if !selectedFiles.isEmpty {
                    Text("已选择 \(selectedFiles.count) 个文件")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
    }
    
    // 网络书籍表单
    private var networkBookForm: some View {
        Form {            
            Section(header: Text("远程地址")) {
                TextField("远程书籍URL", text: $bookURL)
                    .keyboardType(.URL)
                    .textContentType(.URL)
                    .autocapitalization(.none)
                    .disableAutocorrection(true)
            }
            Section(header: Text("基本信息")) {
                TextField("书名", text: $bookTitle)
                TextField("作者", text: $author)
                
                // Remove cover image selection UI
                /*
                if coverImage != nil {
                    Image(uiImage: coverImage!)
                        .resizable()
                        .scaledToFit()
                        .frame(height: 150)
                }
                
                Button(action: { isImportingCover = true }) {
                    Label("选择封面图片", systemImage: "photo")
                }
                
                TextField("封面图片URL", text: $coverImageURL)
                    .keyboardType(.URL)
                    .textContentType(.URL)
                    .autocapitalization(.none)
                    .disableAutocorrection(true)
                */
            }
        }
    }
    
    // MARK: - 辅助方法
    
    // 根据文件类型获取默认封面图片
    private func getDefaultCoverImage(for fileExtension: String?) -> UIImage? {
        guard let ext = fileExtension?.lowercased() else { return nil }
        
        switch ext {
        case "pdf":
            return UIImage(systemName: "doc.richtext")
        case "epub":
            return UIImage(systemName: "book")
        case "txt":
            return UIImage(systemName: "doc.text")
        case "html", "htm":
            return UIImage(systemName: "globe")
        default:
            return UIImage(systemName: "book")
        }
    }
    
    // 注意：以下 isBookDuplicate 和 addBook 方法已被移除，
    // 因为它们的功能已由 ReaderCoordinator 或直接在视图逻辑中处理。
    // 保留此注释作为代码演进的记录。
    
    // 添加网络书籍
    private func addNetworkBook() {
        guard let url = URL(string: bookURL) else { return }
        
        // 显示加载指示器或进度条（可选）
        
        // 使用BookStorage下载网络书籍到应用私有目录
        // 为避免阻塞UI，使用异步方式下载
        let fileName = url.lastPathComponent.isEmpty ? "(UUID().uuidString).txt" : url.lastPathComponent
        
        Task {
            do {
                let (fileURL, relativePath) = try await BookStorage.shared().downloadNetworkBook(from: url, fileName: fileName)
                // 在主线程更新UI
                DispatchQueue.main.async {
                    let newBook = Book(
                        title: self.bookTitle.isEmpty ? fileURL.deletingPathExtension().lastPathComponent : self.bookTitle,
                        author: self.author.isEmpty ? "未知作者" : self.author,
                        url: fileURL,
                        sourceId: UUID(), // 为网络书籍生成新的 sourceId
                        bookType: fileURL.pathExtension,
                        filePath: fileURL.path // 保存完整路径
                        // relativePath 参数已在 Book 模型中移除或修改，此处不再传递
                    )
                    
                    // 将 newBook 插入到 modelContext 并保存
                    do {
                        self.coordinator.modelContainer.mainContext.insert(newBook)
                        try self.coordinator.modelContainer.mainContext.save()
                        print("网络书籍信息已成功保存到 SwiftData: \(newBook.title)")
                        self.coordinator.objectWillChange.send() // 通知UI更新
                        self.coordinator.loadInitialData() // 重新加载数据以更新书架
                        self.presentationMode.wrappedValue.dismiss() // 关闭视图
                    } catch {
                        print("保存网络书籍信息到 SwiftData 失败: \(error.localizedDescription)")
                        self.alertMessage = "保存网络书籍失败: \(error.localizedDescription)"
                        self.showAlert = true
                    }
                }
            } catch {
                // 下载失败处理
                print("下载网络书籍失败或未能获取文件路径: \(error)")
                // 可以在主线程向用户显示错误提示
                DispatchQueue.main.async {
                    self.alertMessage = "下载网络书籍失败: \(error.localizedDescription)"
                    self.showAlert = true
                }
            }
        }
    }
    
    // 检查书籍是否已存在
    private func bookExists(url: URL, title: String) -> Bool {
        // 检查路径是否重复
        let pathDuplicate = coordinator.books.contains { $0.filePath == url.path }
        
        // 检查URL是否重复 (对于网络书籍)
        let urlDuplicate = coordinator.books.contains { $0.url == url }
        
        // 检查书名和格式是否重复
        let titleAndFormatDuplicate = coordinator.books.contains { book in
            let sameTitle = book.title.trimmingCharacters(in: .whitespacesAndNewlines) == title.trimmingCharacters(in: .whitespacesAndNewlines)
            let sameFormat = book.bookType?.lowercased() == url.pathExtension.lowercased()
            return sameTitle && sameFormat
        }
        
        return pathDuplicate || urlDuplicate || titleAndFormatDuplicate
    }
    

}

import SwiftData

struct AddBookView_Previews: PreviewProvider {
    static var previews: some View {
        // 创建一个临时的内存中 ModelContainer 用于预览
        let config = ModelConfiguration(isStoredInMemoryOnly: true)
        let container = try! ModelContainer(for: Book.self, configurations: config)
        AddBookView().environmentObject(ReaderCoordinator(modelContainer: container))
    }
}
