//
//  LoginView.swift
//  IOSReader
//
//  登录视图
//  功能:
//  - 提供用户登录界面
//  - 支持用户名/邮箱登录
//  - 密码输入和验证
//  - 记住登录状态选项
//  - 跳转到注册页面
//  - 密码重置功能
//

import SwiftUI
import SwiftData

struct LoginView: View {
    @EnvironmentObject var accountManager: AccountManager
    @Environment(\.dismiss) private var dismiss
    
    // 表单状态
    @State private var identifier = ""
    @State private var password = ""
    @State private var rememberMe = false
    @State private var showPassword = false
    
    // UI状态
    @State private var isLoading = false
    @State private var showingAlert = false
    @State private var alertMessage = ""
    @State private var showingRegister = false
    @State private var showingPasswordReset = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // 应用Logo和标题
                    headerSection
                    
                    // 登录表单
                    loginFormSection
                    
                    // 登录按钮
                    loginButtonSection
                    
                    // 其他选项
                    optionsSection
                    
                    Spacer(minLength: 50)
                }
                .padding(.horizontal, 24)
                .padding(.top, 40)
            }
            .navigationBarHidden(true)
            .background(
                LinearGradient(
                    colors: [Color.blue.opacity(0.1), Color.purple.opacity(0.1)],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
            )
        }
        .sheet(isPresented: $showingRegister) {
            RegisterView()
        }
        .sheet(isPresented: $showingPasswordReset) {
            PasswordResetView()
        }
        .alert("登录提示", isPresented: $showingAlert) {
            Button("确定", role: .cancel) {}
        } message: {
            Text(alertMessage)
        }
        .onChange(of: accountManager.isLoggedIn) { _, isLoggedIn in
            if isLoggedIn {
                dismiss()
            }
        }
    }
    
    // MARK: - 视图组件
    
    private var headerSection: some View {
        VStack(spacing: 16) {
            // 应用图标
            Image(systemName: "book.circle.fill")
                .font(.system(size: 80))
                .foregroundStyle(
                    LinearGradient(
                        colors: [.blue, .purple],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .symbolEffect(.bounce, options: .speed(0.5))
            
            VStack(spacing: 8) {
                Text("欢迎回来")
                    .font(.largeTitle.bold())
                    .foregroundColor(.primary)
                
                Text("登录您的阅读账号")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
        }
    }
    
    private var loginFormSection: some View {
        VStack(spacing: 16) {
            // 用户名/邮箱输入
            VStack(alignment: .leading, spacing: 8) {
                Text("用户名或邮箱")
                    .font(.subheadline.weight(.medium))
                    .foregroundColor(.secondary)
                
                HStack {
                    Image(systemName: "person.circle")
                        .foregroundColor(.secondary)
                        .frame(width: 20)
                    
                    TextField("请输入用户名或邮箱", text: $identifier)
                        .textFieldStyle(PlainTextFieldStyle())
                        .autocapitalization(.none)
                        .disableAutocorrection(true)
                        .keyboardType(.emailAddress)
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 12)
                .background(Color(.systemGray6))
                .cornerRadius(12)
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.blue.opacity(0.3), lineWidth: 1)
                )
            }
            
            // 密码输入
            VStack(alignment: .leading, spacing: 8) {
                Text("密码")
                    .font(.subheadline.weight(.medium))
                    .foregroundColor(.secondary)
                
                HStack {
                    Image(systemName: "lock.circle")
                        .foregroundColor(.secondary)
                        .frame(width: 20)
                    
                    Group {
                        if showPassword {
                            TextField("请输入密码", text: $password)
                        } else {
                            SecureField("请输入密码", text: $password)
                        }
                    }
                    .textFieldStyle(PlainTextFieldStyle())
                    .autocapitalization(.none)
                    .disableAutocorrection(true)
                    
                    Button(action: {
                        showPassword.toggle()
                    }) {
                        Image(systemName: showPassword ? "eye.slash" : "eye")
                            .foregroundColor(.secondary)
                    }
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 12)
                .background(Color(.systemGray6))
                .cornerRadius(12)
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.blue.opacity(0.3), lineWidth: 1)
                )
            }
            
            // 记住登录状态
            HStack {
                Button(action: {
                    rememberMe.toggle()
                }) {
                    HStack(spacing: 8) {
                        Image(systemName: rememberMe ? "checkmark.square.fill" : "square")
                            .foregroundColor(rememberMe ? .blue : .secondary)
                        
                        Text("记住登录状态")
                            .font(.subheadline)
                            .foregroundColor(.primary)
                    }
                }
                
                Spacer()
                
                Button("忘记密码？") {
                    showingPasswordReset = true
                }
                .font(.subheadline)
                .foregroundColor(.blue)
            }
        }
        .padding(.horizontal, 4)
    }
    
    private var loginButtonSection: some View {
        Button(action: performLogin) {
            HStack {
                if isLoading {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        .scaleEffect(0.8)
                } else {
                    Image(systemName: "arrow.right.circle.fill")
                        .font(.title3)
                }
                
                Text(isLoading ? "登录中..." : "登录")
                    .font(.headline)
                    .fontWeight(.semibold)
            }
            .foregroundColor(.white)
            .frame(maxWidth: .infinity)
            .frame(height: 50)
            .background(
                LinearGradient(
                    colors: [.blue, .purple],
                    startPoint: .leading,
                    endPoint: .trailing
                )
            )
            .cornerRadius(12)
            .shadow(color: .blue.opacity(0.3), radius: 8, x: 0, y: 4)
        }
        .disabled(isLoading || identifier.isEmpty || password.isEmpty)
        .opacity(identifier.isEmpty || password.isEmpty ? 0.6 : 1.0)
    }
    
    private var optionsSection: some View {
        VStack(spacing: 16) {
            // 分割线
            HStack {
                Rectangle()
                    .frame(height: 1)
                    .foregroundColor(.secondary.opacity(0.3))
                
                Text("或")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .padding(.horizontal, 16)
                
                Rectangle()
                    .frame(height: 1)
                    .foregroundColor(.secondary.opacity(0.3))
            }
            
            // 注册按钮
            Button(action: {
                showingRegister = true
            }) {
                HStack {
                    Image(systemName: "person.badge.plus")
                        .font(.title3)
                    
                    Text("创建新账号")
                        .font(.headline)
                        .fontWeight(.medium)
                }
                .foregroundColor(.blue)
                .frame(maxWidth: .infinity)
                .frame(height: 50)
                .background(Color.blue.opacity(0.1))
                .cornerRadius(12)
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.blue.opacity(0.3), lineWidth: 1)
                )
            }
            
            // 游客模式
            Button(action: {
                dismiss()
            }) {
                Text("暂不登录，继续使用")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .underline()
            }
        }
    }
    
    // MARK: - 方法
    
    private func performLogin() {
        guard !identifier.isEmpty, !password.isEmpty else {
            showAlert(message: "请输入用户名和密码")
            return
        }
        
        isLoading = true
        
        Task {
            do {
                let result = try await accountManager.login(
                    identifier: identifier,
                    password: password,
                    rememberMe: rememberMe
                )
                
                await MainActor.run {
                    isLoading = false
                    
                    switch result {
                    case .success(let user):
                        print("登录成功: \(user.nickname)")
                        // 登录成功后会通过onChange自动关闭视图
                    case .failure(let error):
                        showAlert(message: error.localizedDescription)
                    }
                }
            } catch {
                await MainActor.run {
                    isLoading = false
                    showAlert(message: error.localizedDescription)
                }
            }
        }
    }
    
    private func showAlert(message: String) {
        alertMessage = message
        showingAlert = true
    }
}

// MARK: - 预览

#Preview {
    Group {
        if let container = try? ModelContainer(for: User.self, Book.self, BookSource.self, Bookmark.self, ReadingProgress.self) {
            LoginView()
                .environmentObject(ReaderCoordinator(modelContainer: container))
        } else {
            Text("预览错误")
        }
    }
}