// WebServiceView.swift
// Web服务功能视图文件，负责管理应用的内嵌浏览器编辑功能
// 核心功能：
// 1. Monaco代码编辑器集成
// 2. 实时语法校验
// 3. Web服务配置管理
// 主要交互逻辑：
// - 通过ReaderCoordinator协调视图与数据层的交互
// - 提供Web服务配置的编辑与保存功能

import SwiftUI
import SwiftData // 确保 SwiftData 已导入

struct WebServiceView: View {
    @EnvironmentObject private var coordinator: ReaderCoordinator
    
    var body: some View {
        Text("Web服务功能")
            .navigationTitle("Web服务")
    }
}

// MARK: - 预览

#Preview {
    if let container = try? ModelContainer(for: User.self, Book.self, BookSource.self, Bookmark.self, ReadingProgress.self) {
        WebServiceView()
            .modelContainer(container)
            .environmentObject(ReaderCoordinator(modelContainer: container))
    } else {
        Text("预览错误")
    }
}