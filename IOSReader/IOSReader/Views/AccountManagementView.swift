//
//  AccountManagementView.swift
//  IOSReader
//
//  账号管理视图
//  功能:
//  - 显示用户账号信息
//  - 提供账号设置和管理功能
//  - 支持头像上传、资料编辑
//  - 账号安全设置
//  - 登出功能
//

import SwiftUI
import SwiftData
import PhotosUI

struct AccountManagementView: View {
    @EnvironmentObject var accountManager: AccountManager
    @Environment(\.dismiss) private var dismiss
    
    // 编辑状态
    @State private var isEditing = false
    @State private var editedNickname = ""
    @State private var editedBio = ""
    
    // UI状态
    @State private var showingImagePicker = false
    @State private var showingPasswordChange = false
    @State private var showingDeleteAccount = false
    @State private var showingLogoutAlert = false
    @State private var showingAlert = false
    @State private var alertMessage = ""
    @State private var isLoading = false
    
    // 头像选择
    @State private var selectedPhoto: PhotosPickerItem?
    @State private var avatarImage: UIImage?
    
    var currentUser: User? {
        accountManager.currentUser
    }
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // 用户头像和基本信息
                    profileHeaderSection
                    
                    // 账号信息卡片
                    accountInfoSection
                    
                    // 账号设置
                    accountSettingsSection
                    
                    // 安全设置
                    securitySection
                    
                    // 危险操作
                    dangerZoneSection
                    
                    Spacer(minLength: 50)
                }
                .padding(.horizontal, 20)
                .padding(.top, 20)
            }
            .navigationTitle("账号管理")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    if isEditing {
                        Button("保存") {
                            saveProfile()
                        }
                        .disabled(isLoading)
                    } else {
                        Button("编辑") {
                            startEditing()
                        }
                    }
                }
                
                ToolbarItem(placement: .navigationBarLeading) {
                    if isEditing {
                        Button("取消") {
                            cancelEditing()
                        }
                    } else {
                        Button("完成") {
                            dismiss()
                        }
                    }
                }
            }
        }
        .photosPicker(isPresented: $showingImagePicker, selection: $selectedPhoto, matching: .images)
        .onChange(of: selectedPhoto) { _, newValue in
            loadSelectedPhoto()
        }
        .sheet(isPresented: $showingPasswordChange) {
            ChangePasswordView()
        }
        .alert("确认登出", isPresented: $showingLogoutAlert) {
            Button("取消", role: .cancel) {}
            Button("登出", role: .destructive) {
                performLogout()
            }
        } message: {
            Text("确定要登出当前账号吗？")
        }
        .alert("确认删除账号", isPresented: $showingDeleteAccount) {
            Button("取消", role: .cancel) {}
            Button("删除", role: .destructive) {
                deleteAccount()
            }
        } message: {
            Text("删除账号后将无法恢复，确定要继续吗？")
        }
        .alert("提示", isPresented: $showingAlert) {
            Button("确定", role: .cancel) {}
        } message: {
            Text(alertMessage)
        }
    }
    
    // MARK: - 视图组件
    
    private var profileHeaderSection: some View {
        VStack(spacing: 20) {
            // 头像
            Button(action: {
                if isEditing {
                    showingImagePicker = true
                }
            }) {
                ZStack {
                    if let avatarImage = avatarImage {
                        Image(uiImage: avatarImage)
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                            .frame(width: 100, height: 100)
                            .clipShape(Circle())
                    } else {
                        Circle()
                            .fill(
                                LinearGradient(
                                    colors: [.blue, .purple],
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                )
                            )
                            .frame(width: 100, height: 100)
                            .overlay(
                                Text(currentUser?.nickname.prefix(1).uppercased() ?? "U")
                                    .font(.largeTitle.bold())
                                    .foregroundColor(.white)
                            )
                    }
                    
                    if isEditing {
                        Circle()
                            .fill(Color.black.opacity(0.3))
                            .frame(width: 100, height: 100)
                            .overlay(
                                Image(systemName: "camera.fill")
                                    .font(.title2)
                                    .foregroundColor(.white)
                            )
                    }
                }
            }
            .disabled(!isEditing)
            
            // 用户信息
            VStack(spacing: 8) {
                if isEditing {
                    TextField("昵称", text: $editedNickname)
                        .font(.title2.bold())
                        .multilineTextAlignment(.center)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                } else {
                    Text(currentUser?.nickname ?? "未知用户")
                        .font(.title2.bold())
                        .foregroundColor(.primary)
                }
                
                HStack(spacing: 8) {
                    Image(systemName: "crown.fill")
                        .foregroundColor(.orange)
                        .font(.caption)
                    
                    Text(currentUser?.level.description ?? "普通用户")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                
                if let user = currentUser {
                    Text("注册于 \(formatDate(user.createdAt))")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            // 个人简介
            if isEditing {
                VStack(alignment: .leading, spacing: 8) {
                    Text("个人简介")
                        .font(.subheadline.weight(.medium))
                        .foregroundColor(.secondary)
                    
                    TextField("介绍一下自己吧...", text: $editedBio, axis: .vertical)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                        .lineLimit(3...6)
                }
            } else if let bio = currentUser?.bio, !bio.isEmpty {
                Text(bio)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 20)
            }
        }
        .padding(.vertical, 20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemGray6))
        )
    }
    
    private var accountInfoSection: some View {
        VStack(spacing: 16) {
            HStack {
                Text("账号信息")
                    .font(.headline)
                    .foregroundColor(.primary)
                
                Spacer()
            }
            
            VStack(spacing: 12) {
                infoRow(icon: "person.circle", title: "用户名", value: currentUser?.username ?? "")
                infoRow(icon: "envelope.circle", title: "邮箱", value: currentUser?.email ?? "")
                infoRow(icon: "checkmark.shield", title: "账号状态", value: currentUser?.status.description ?? "")
                
                if let user = currentUser {
                    infoRow(icon: "clock.circle", title: "最后登录", value: formatDate(user.lastLoginAt))
                }
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 2)
        )
    }
    
    private var accountSettingsSection: some View {
        VStack(spacing: 16) {
            HStack {
                Text("账号设置")
                    .font(.headline)
                    .foregroundColor(.primary)
                
                Spacer()
            }
            
            VStack(spacing: 0) {
                settingRow(
                    icon: "bell.circle",
                    title: "通知设置",
                    subtitle: "管理推送通知",
                    action: {
                        // TODO: 打开通知设置
                    }
                )
                
                Divider().padding(.leading, 50)
                
                settingRow(
                    icon: "moon.circle",
                    title: "主题设置",
                    subtitle: "深色模式和主题",
                    action: {
                        // TODO: 打开主题设置
                    }
                )
                
                Divider().padding(.leading, 50)
                
                settingRow(
                    icon: "icloud.circle",
                    title: "数据同步",
                    subtitle: "云端备份和同步",
                    action: {
                        // TODO: 打开数据同步设置
                    }
                )
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 2)
        )
    }
    
    private var securitySection: some View {
        VStack(spacing: 16) {
            HStack {
                Text("安全设置")
                    .font(.headline)
                    .foregroundColor(.primary)
                
                Spacer()
            }
            
            VStack(spacing: 0) {
                settingRow(
                    icon: "key.circle",
                    title: "修改密码",
                    subtitle: "更改登录密码",
                    action: {
                        showingPasswordChange = true
                    }
                )
                
                Divider().padding(.leading, 50)
                
                settingRow(
                    icon: "faceid",
                    title: "生物识别",
                    subtitle: "Face ID / Touch ID",
                    action: {
                        // TODO: 打开生物识别设置
                    }
                )
                
                Divider().padding(.leading, 50)
                
                settingRow(
                    icon: "shield.circle",
                    title: "隐私设置",
                    subtitle: "数据隐私和权限",
                    action: {
                        // TODO: 打开隐私设置
                    }
                )
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 2)
        )
    }
    
    private var dangerZoneSection: some View {
        VStack(spacing: 16) {
            HStack {
                Text("危险操作")
                    .font(.headline)
                    .foregroundColor(.red)
                
                Spacer()
            }
            
            VStack(spacing: 0) {
                settingRow(
                    icon: "arrow.right.square",
                    title: "登出账号",
                    subtitle: "退出当前登录",
                    titleColor: .orange,
                    action: {
                        showingLogoutAlert = true
                    }
                )
                
                Divider().padding(.leading, 50)
                
                settingRow(
                    icon: "trash.circle",
                    title: "删除账号",
                    subtitle: "永久删除账号和数据",
                    titleColor: .red,
                    action: {
                        showingDeleteAccount = true
                    }
                )
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 2)
        )
    }
    
    // MARK: - 辅助视图
    
    private func infoRow(icon: String, title: String, value: String) -> some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .foregroundColor(.blue)
                .frame(width: 24, height: 24)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                
                Text(value)
                    .font(.subheadline.weight(.medium))
                    .foregroundColor(.primary)
            }
            
            Spacer()
        }
        .padding(.vertical, 4)
    }
    
    private func settingRow(
        icon: String,
        title: String,
        subtitle: String,
        titleColor: Color = .primary,
        action: @escaping () -> Void
    ) -> some View {
        Button(action: action) {
            HStack(spacing: 12) {
                Image(systemName: icon)
                    .foregroundColor(.blue)
                    .frame(width: 24, height: 24)
                
                VStack(alignment: .leading, spacing: 2) {
                    Text(title)
                        .font(.subheadline.weight(.medium))
                        .foregroundColor(titleColor)
                    
                    Text(subtitle)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                Image(systemName: "chevron.right")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding(.vertical, 8)
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    // MARK: - 方法
    
    private func startEditing() {
        isEditing = true
        editedNickname = currentUser?.nickname ?? ""
        editedBio = currentUser?.bio ?? ""
    }
    
    private func cancelEditing() {
        isEditing = false
        editedNickname = ""
        editedBio = ""
        avatarImage = nil
        selectedPhoto = nil
    }
    
    private func saveProfile() {
        guard let user = currentUser else { return }
        
        isLoading = true
        
        Task {
            do {
                try await accountManager.updateProfile(
                    nickname: editedNickname.isEmpty ? user.nickname : editedNickname
                )
                
                await MainActor.run {
                    isLoading = false
                    isEditing = false
                    showAlert(message: "资料更新成功")
                }
            } catch {
                await MainActor.run {
                    isLoading = false
                    showAlert(message: "更新失败: \(error.localizedDescription)")
                }
            }
        }
    }
    
    private func loadSelectedPhoto() {
        guard let selectedPhoto = selectedPhoto else { return }
        
        Task {
            if let data = try? await selectedPhoto.loadTransferable(type: Data.self),
               let image = UIImage(data: data) {
                await MainActor.run {
                    avatarImage = image
                }
            }
        }
    }
    
    private func performLogout() {
        Task {
            await accountManager.logout()
            dismiss()
        }
    }
    
    private func deleteAccount() {
        // TODO: 实现账号删除逻辑
        showAlert(message: "账号删除功能暂未实现")
    }
    
    private func showAlert(message: String) {
        alertMessage = message
        showingAlert = true
    }
    
    private func formatDate(_ date: Date?) -> String {
        guard let date = date else { return "未登录" }
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy年MM月dd日 HH:mm"
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter.string(from: date)
    }
}

// MARK: - 修改密码视图

struct ChangePasswordView: View {
    @EnvironmentObject var accountManager: AccountManager
    @Environment(\.dismiss) private var dismiss
    
    @State private var currentPassword = ""
    @State private var newPassword = ""
    @State private var confirmPassword = ""
    @State private var showCurrentPassword = false
    @State private var showNewPassword = false
    @State private var showConfirmPassword = false
    @State private var isLoading = false
    @State private var showingAlert = false
    @State private var alertMessage = ""
    
    var isFormValid: Bool {
        !currentPassword.isEmpty &&
        newPassword.count >= 8 &&
        newPassword == confirmPassword
    }
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // 说明文字
                    VStack(spacing: 8) {
                        Text("修改密码")
                            .font(.title2.bold())
                        
                        Text("为了您的账号安全，请输入当前密码以验证身份")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                    }
                    .padding(.top, 20)
                    
                    // 密码输入表单
                    VStack(spacing: 20) {
                        passwordField(
                            title: "当前密码",
                            placeholder: "请输入当前密码",
                            text: $currentPassword,
                            showPassword: $showCurrentPassword
                        )
                        
                        passwordField(
                            title: "新密码",
                            placeholder: "请输入新密码（至少8位）",
                            text: $newPassword,
                            showPassword: $showNewPassword
                        )
                        
                        passwordField(
                            title: "确认新密码",
                            placeholder: "请再次输入新密码",
                            text: $confirmPassword,
                            showPassword: $showConfirmPassword
                        )
                    }
                    
                    // 修改按钮
                    Button(action: changePassword) {
                        HStack {
                            if isLoading {
                                ProgressView()
                                    .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                    .scaleEffect(0.8)
                            } else {
                                Image(systemName: "key.fill")
                                    .font(.title3)
                            }
                            
                            Text(isLoading ? "修改中..." : "修改密码")
                                .font(.headline)
                                .fontWeight(.semibold)
                        }
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .frame(height: 50)
                        .background(Color.blue)
                        .cornerRadius(12)
                    }
                    .disabled(!isFormValid || isLoading)
                    .opacity(isFormValid ? 1.0 : 0.6)
                    
                    Spacer()
                }
                .padding(.horizontal, 24)
            }
            .navigationTitle("修改密码")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden(true)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        dismiss()
                    }
                }
            }
        }
        .alert("提示", isPresented: $showingAlert) {
            Button("确定", role: .cancel) {
                if alertMessage.contains("成功") {
                    dismiss()
                }
            }
        } message: {
            Text(alertMessage)
        }
    }
    
    private func passwordField(
        title: String,
        placeholder: String,
        text: Binding<String>,
        showPassword: Binding<Bool>
    ) -> some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(title)
                .font(.subheadline.weight(.medium))
                .foregroundColor(.secondary)
            
            HStack {
                Image(systemName: "lock.circle")
                    .foregroundColor(.secondary)
                    .frame(width: 20)
                
                Group {
                    if showPassword.wrappedValue {
                        TextField(placeholder, text: text)
                    } else {
                        SecureField(placeholder, text: text)
                    }
                }
                .textFieldStyle(PlainTextFieldStyle())
                .autocapitalization(.none)
                .disableAutocorrection(true)
                
                Button(action: {
                    showPassword.wrappedValue.toggle()
                }) {
                    Image(systemName: showPassword.wrappedValue ? "eye.slash" : "eye")
                        .foregroundColor(.secondary)
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(Color(.systemGray6))
            .cornerRadius(12)
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(Color.blue.opacity(0.3), lineWidth: 1)
            )
        }
    }
    
    private func changePassword() {
        isLoading = true
        
        Task {
            do {
                try await accountManager.changePassword(
                    oldPassword: currentPassword,
                    newPassword: newPassword
                )
                
                await MainActor.run {
                    isLoading = false
                    showAlert(message: "密码修改成功")
                }
            } catch {
                await MainActor.run {
                    isLoading = false
                    showAlert(message: "修改失败: \(error.localizedDescription)")
                }
            }
        }
    }
    
    private func showAlert(message: String) {
        alertMessage = message
        showingAlert = true
    }
    
    private func formatDate(_ date: Date?) -> String {
        guard let date = date else { return "未登录" }
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy年MM月dd日 HH:mm"
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter.string(from: date)
    }
}

// MARK: - 预览

#Preview {
    Group {
        if let container = try? ModelContainer(for: User.self, Book.self, BookSource.self, Bookmark.self, ReadingProgress.self) {
            AccountManagementView()
                .environmentObject(ReaderCoordinator(modelContainer: container))
        } else {
            Text("预览错误")
        }
    }
}
