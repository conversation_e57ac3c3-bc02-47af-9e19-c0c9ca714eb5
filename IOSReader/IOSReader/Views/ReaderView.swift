//
/// 阅读器视图 - 核心阅读界面
/// 核心功能:
/// 1. 显示当前页面内容
/// 2. 提供页面导航控制
/// 3. 显示阅读进度
/// 4. 支持手势翻页
/// 
/// 主要UI组件:
/// - 内容显示区域
/// - 页面导航按钮
/// - 阅读进度指示器
/// - 设置按钮
import SwiftUI
import SwiftData
import AVFoundation

// 使用 UIViewRepresentable 包装 UITextView 显示富文本
struct AttributedTextView: UIViewControllerRepresentable {
    typealias UIViewControllerType = UIViewController
    let attributedText: NSAttributedString
    
    func makeUIViewController(context: Context) -> UIViewControllerType {
        let viewController = UIViewController()
        viewController.view.backgroundColor = .clear
        
        let textView = UITextView()
        // 基本配置
        textView.backgroundColor = .clear
        textView.isEditable = false
        textView.isSelectable = true
        textView.isScrollEnabled = false
        textView.showsVerticalScrollIndicator = false
        textView.showsHorizontalScrollIndicator = false
        
        // 文本容器配置
        textView.textContainer.lineBreakMode = .byWordWrapping
        textView.textContainer.maximumNumberOfLines = 0
        textView.textContainer.lineFragmentPadding = 0
        
        // 内边距配置
        textView.textContainerInset = UIEdgeInsets(top: 10, left: 10, bottom: 10, right: 10)
        
        // 自动布局配置
        textView.translatesAutoresizingMaskIntoConstraints = false
        
        let containerView = UIView()
        containerView.translatesAutoresizingMaskIntoConstraints = false
        containerView.backgroundColor = .clear
        viewController.view.addSubview(containerView)
        
        NSLayoutConstraint.activate([
            containerView.topAnchor.constraint(equalTo: viewController.view.topAnchor),
            containerView.bottomAnchor.constraint(equalTo: viewController.view.bottomAnchor),
            containerView.leadingAnchor.constraint(equalTo: viewController.view.leadingAnchor),
            containerView.trailingAnchor.constraint(equalTo: viewController.view.trailingAnchor)
        ])
        
        containerView.addSubview(textView)
        
        NSLayoutConstraint.activate([
            textView.topAnchor.constraint(equalTo: containerView.topAnchor),
            textView.bottomAnchor.constraint(equalTo: containerView.bottomAnchor),
            textView.leadingAnchor.constraint(equalTo: containerView.leadingAnchor),
            textView.trailingAnchor.constraint(equalTo: containerView.trailingAnchor)
        ])
        
        textView.attributedText = attributedText
        
        return viewController
    }
    
    func updateUIViewController(_ uiViewController: UIViewController, context: Context) {
        guard let containerView = uiViewController.view.subviews.first,
              let textView = containerView.subviews.first(where: { $0 is UITextView }) as? UITextView else {
            return
        }
        textView.attributedText = attributedText
    }
    
    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }
    
    class Coordinator: NSObject {
        var parent: AttributedTextView
        init(_ parent: AttributedTextView) {
            self.parent = parent
        }
    }
} // End of AttributedTextView

struct ReaderView: View {
    // MARK: - 属性
    @ObservedObject var coordinator: ReaderCoordinator
    @Environment(\.dismiss) private var dismiss
    @State private var showSettings = false
    @Binding var showToolbars: Bool // 改为Binding，从父视图传入
    @State private var isListening: Bool = false
    @State private var showBookmarks: Bool = false
    @State private var showChapters: Bool = false
    @State private var showDownloadOptions: Bool = false // 假设底部有下载按钮, 暂时移除，因为参考图片中没有
    @State private var isNightMode: Bool = false // 由 coordinator.readingMode 控制
    @State private var brightness: Double = UIScreen.main.brightness // 用于亮度调节
    private let readerToolbarHeight: CGFloat = 44
    //@Environment(\.safeAreaInsets) private var safeAreaInsets

    // MARK: - 私有属性
    private var backgroundColor: Color {
        coordinator.readingMode == .day ? Color(UIColor.systemGray6) : Color(UIColor.darkGray)
    }
    
    private var textColor: Color {
        coordinator.readingMode == .day ? Color.black : Color(UIColor.lightGray)
    }
    
    @State private var dragOffset: CGSize = .zero

    // MARK: - 功能方法
    private func toggleListening() {
        isListening.toggle()
        // TODO: 实现更完善的听书功能，连接到 Coordinator
        print("Toggle Listening: \(isListening)")
    }
    
    private func toggleNightMode() {
        isNightMode.toggle()
        coordinator.readingMode = isNightMode ? .night : .day
        print("Toggle Night Mode: \(coordinator.readingMode)")
    }
    
    private func addBookmark() async {
        await coordinator.addBookmarkAtCurrentPage()
        print("Add Bookmark Tapped")
        // 不自动显示书签列表，只添加书签
    }

    // MARK: - Top Toolbar
    @ViewBuilder
    private var topToolbar: some View {
        VStack(spacing: 0) {
            // 实际工具栏内容
            HStack {
                Button {
                    dismiss()
                } label: {
                    Image(systemName: "chevron.left")
                        .foregroundColor(textColor)
                        .font(.title2)
                }
                .padding(.leading, 16)
                
                Spacer()
                
                Text(coordinator.readerViewModel.currentBook?.title ?? "")
                    .font(.headline)
                    .lineLimit(1)
                    .truncationMode(.tail)
                    .foregroundColor(textColor)
                
                Spacer()
                
                Button {
                    toggleListening()
                } label: {
                    Image(systemName: isListening ? "speaker.wave.2.fill" : "speaker.wave.2")
                        .foregroundColor(textColor)
                        .font(.title2)
                }
                .padding(.trailing, 8)
                
                Button {
                    Task {
                        await addBookmark()
                    }
                } label: {
                    Image(systemName: "bookmark")
                        .foregroundColor(textColor)
                        .font(.title2)
                }
                .padding(.trailing, 16)
            }
            .background(backgroundColor.opacity(0.8))
            .frame(height: 44)
        }
        .transition(.move(edge: .top).combined(with: .opacity))
    }

    // MARK: - Bottom Toolbar
    @ViewBuilder
    private var bottomToolbar: some View {
        VStack(spacing: 0) {
            HStack {
                Button {
                    Task {
                        await coordinator.readerViewModel.previousChapter()
                    }
                } label: {
                    Text("上一章")
                        .foregroundColor(textColor)
                        .font(.system(size: 14))
                }
                .disabled(coordinator.readerViewModel.currentChapterIndex <= 0)
                
                Spacer()
                Text("\(coordinator.readerViewModel.currentChapterIndex + 1)/\(coordinator.readerViewModel.chapters.count) \(coordinator.readerViewModel.currentChapterTitle)")
                    .font(.caption)
                    .foregroundColor(textColor)
                    .lineLimit(1)
                    .truncationMode(.middle)
                Spacer()
                
                Button {
                    Task {
                        await coordinator.readerViewModel.nextChapter()
                    }
                } label: {
                    Text("下一章")
                        .foregroundColor(textColor)
                        .font(.system(size: 14))
                }
                .disabled(coordinator.readerViewModel.currentChapterIndex >= coordinator.readerViewModel.chapters.count - 1)
            }
            .padding(.horizontal)
            .frame(height: 44)
            
            HStack {
                ToolbarButton(icon: "list.bullet", text: "目录") {
                    showChapters = true
                }
                .foregroundColor(textColor)
                .frame(maxWidth: .infinity)
                
                ToolbarButton(icon: "sun.max", text: "亮度") { 
                    brightness = brightness > 0.5 ? 0.2 : 0.8
                    UIScreen.main.brightness = CGFloat(brightness)
                }
                .foregroundColor(textColor)
                .frame(maxWidth: .infinity)
                
                ToolbarButton(icon: coordinator.readingMode == .day ? "moon.fill" : "sun.max.fill", text: "深色") {
                    toggleNightMode()
                }
                .foregroundColor(textColor)
                .frame(maxWidth: .infinity)
                
                ToolbarButton(icon: "textformat.size", text: "设置") {
                    showSettings = true
                }
                .foregroundColor(textColor)
                .frame(maxWidth: .infinity)
            }
            .padding(.horizontal)
            .frame(height: 44)
        }
        .background(backgroundColor.opacity(0.8))
        .frame(height: 88) // 两行工具栏的高度
        .transition(.move(edge: .bottom).combined(with: .opacity))
    }
    
    // MARK: - Body
    var body: some View {
        let _ = print("ReaderView body: 渲染 ReaderView")
        ZStack {
            backgroundColor
                .ignoresSafeArea()
            
            GeometryReader { geometry in
                VStack(spacing: 0) {
                    contentArea(geometry: geometry)
                        .frame(maxWidth: .infinity, maxHeight: .infinity)
                        .contentShape(Rectangle())
                        .onTapGesture {
                            withAnimation(.easeInOut(duration: 0.2)) {
                                showToolbars.toggle()
                            }
                        }
                        .gesture(
                            DragGesture()
                                .onChanged { value in 
                                    dragOffset = value.translation 
                                }
                                .onEnded { value in
                                    let horizontalMovement = value.translation.width
                                    let verticalMovement = value.translation.height
                                    
                                    // 确保是水平滑动且滑动距离足够
                                    if abs(horizontalMovement) > abs(verticalMovement) && abs(horizontalMovement) > 80 {
                                        Task {
                                            if horizontalMovement < 0 {
                                                // 向左滑动，下一页
                                                await coordinator.readerViewModel.nextPage()
                                            } else {
                                                // 向右滑动，上一页
                                                await coordinator.readerViewModel.previousPage()
                                            }
                                        }
                                    }
                                    
                                    // 重置拖拽偏移
                                    dragOffset = .zero
                                }
                        )
                        .onAppear {
                            coordinator.readerViewModel.updateReaderViewSize(geometry.size)
                        }
                        .onChange(of: geometry.size) { oldSize, newSize in // Swift 5.5+ syntax
                            coordinator.readerViewModel.updateReaderViewSize(newSize)
                        }
                }
                .overlay(
                    VStack {
                        if showToolbars { 
                            topToolbar
                                .zIndex(1)
                        }
                        Spacer()
                        if showToolbars { 
                            bottomToolbar
                                .zIndex(1)
                        }
                    }
                    .animation(.easeInOut(duration: 0.2), value: showToolbars)
                )
            }
        }
        .onAppear {
            isNightMode = coordinator.readingMode == .night
        }
        .sheet(isPresented: $showSettings) {
            ReaderSettingsView(settings: $coordinator.readerViewModel.settings)
        }
        .sheet(isPresented: $showBookmarks) {
            SharedBookmarkManagementView(coordinator: coordinator)
        }
        .sheet(isPresented: $showChapters) {
            SharedChapterListView(coordinator: coordinator) { chapterIndex in
                Task {
                    // 跳转到指定章节
                    await coordinator.readerViewModel.jumpToChapter(chapterIndex)
                }
                showChapters = false
            }
        }
    } // End of body

    // MARK: - 子视图构建
    @ViewBuilder
    func contentArea(geometry: GeometryProxy) -> some View {
        let _ = print("ReaderView contentArea: 准备渲染 ReaderContentView")
        
        // 计算内容区域的安全边距
        let topPadding: CGFloat = showToolbars ? 88 + max(0, geometry.safeAreaInsets.top) : 20
        let bottomPadding: CGFloat = showToolbars ? 88 + max(0, geometry.safeAreaInsets.bottom) : 20
        
        ReaderContentView(coordinator: coordinator, textColor: textColor)
            .padding(.top, topPadding)
            .padding(.bottom, bottomPadding)
            .padding(.horizontal, 16)
    }
} // End of ReaderView

// MARK: - 预览
#Preview {
    Group {
        if let container = try? ModelContainer(for: User.self, Book.self, BookSource.self, Bookmark.self, ReadingProgress.self) {
            ReaderView(coordinator: ReaderCoordinator(modelContainer: container), showToolbars: .constant(true))
        } else {
            Text("预览错误")
        }
    }
}

// Removed duplicate SharedChapterListView and SharedDownloadOptionsView
// These are defined in SharedReaderComponents.swift


// 注意：原始的 ReaderView.swift 包含 SharedChapterListView 和 SharedDownloadOptionsView 的本地定义，这些定义已被移除。
// .sheet 修饰符现在应该使用 SharedReaderComponents.swift 中的版本。
// 以下代码假定 SharedChapterListView 和 SharedDownloadOptionsView
// 存在于作用域内（很可能从 SharedReaderComponents.swift 导入）。
// 如果 SharedReaderComponents.swift 没有定义它们，这些调用将失败。

// 假设 SharedChapterListView 存在的示例：
// .sheet(isPresented: $showChapters) {
//     SharedChapterListView(coordinator: coordinator)
// }

// 假设 SharedDownloadOptionsView 存在的示例：
// .sheet(isPresented: $showDownloadOptions) {
//     SharedDownloadOptionsView(coordinator: coordinator)
// }
// 上面的 .sheet 修饰符现在正确引用了共享视图。
// 移除了先前关于潜在问题的解释性注释。
