// 阅读记录功能视图
// 主要功能：
// 1. 阅读时间轴展示 - 按时间顺序显示用户的阅读历史
// 2. 阅读速度统计 - 计算并展示用户的平均阅读速度
// 3. 阅读进度追踪 - 记录和显示每本书的阅读进度

import SwiftUI
import SwiftData

struct ReadingRecordView: View {
    @EnvironmentObject private var coordinator: ReaderCoordinator
    @State private var fileContent: String = ""
    @State private var isFilePickerPresented = false
    
    var body: some View {
        VStack {
            if !fileContent.isEmpty {
                ScrollView {
                    Text(fileContent)
                        .padding()
                }
            } else {
                Text("请选择要查看的文件")
            }
        }
        .navigationTitle("阅读记录")
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                Button(action: { isFilePickerPresented = true }) {
                    Image(systemName: "doc.text")
                }
            }
        }
        .fileImporter(
            isPresented: $isFilePickerPresented,
            allowedContentTypes: [.plainText],
            allowsMultipleSelection: false
        ) { result in
            switch result {
            case .success(let urls):
                guard urls.first != nil else { return }
                // TODO: 实现 coordinator.readFileContent 方法并在此处调用
                fileContent = "" // 暂时设置为空字符串，直到 coordinator.readFileContent 明确
            case .failure(let error):
                print("文件选择错误: \(error.localizedDescription)")
                fileContent = "文件选择失败"
            }
        }
    }
}

#Preview {
    Group {
        if let container = try? ModelContainer(for: User.self, Book.self, BookSource.self, Bookmark.self, ReadingProgress.self) {
            NavigationStack {
                ReadingRecordView()
            }
            .modelContainer(container)
            .environmentObject(ReaderCoordinator(modelContainer: container))
        } else {
            Text("预览错误")
        }
    }
}
