// BackupView.swift
// 备份与恢复功能视图
//
// 功能说明:
// 1. WebDAV配置 - 支持连接WebDAV服务器进行云端备份
// 2. 本地备份管理 - 管理设备本地备份文件的创建、恢复与删除
// 3. 增量备份策略 - 采用增量备份机制优化备份效率
//
// 主要交互:
// - 提供备份创建、恢复、删除等操作界面
// - 显示备份历史记录和状态
// - 支持本地与云端备份切换

import SwiftUI
import SwiftData

/// 备份状态
enum BackupStatus: String {
    case idle = "空闲"
    case inProgress = "进行中"
    case success = "成功"
    case failure = "失败"
}

/// 恢复状态
enum RestoreStatus: String {
    case idle = "空闲"
    case inProgress = "进行中"
    case success = "成功"
    case failure = "失败"
}

struct BackupView: View {
    // MARK: - 数据绑定
    @EnvironmentObject private var coordinator: ReaderCoordinator
    @State private var selectedBackupLocation: BackupLocation = .local
    @State private var selectedItems: Set<BackupItem> = []
    @State private var backupStatus: BackupStatus = .idle
    @State private var restoreStatus: RestoreStatus = .idle
    @State private var showDocumentPicker = false
    @State private var selectedBackupFile: URL?
    
    // MARK: - 文件选择器
    private let fileManager = FileManager.default
    @State private var isFilePickerPresented = false
    @State private var selectedFileURL: URL?
    
    // MARK: - 初始化
    // 使用 @EnvironmentObject 后，不再需要自定义初始化器
    // init(coordinator: ReaderCoordinator) {
    //     // 如果需要从外部初始化，可以考虑这种方式，但通常 @EnvironmentObject 用于从父视图注入
    //     // _coordinator = StateObject(wrappedValue: coordinator)
    // }
    
    // MARK: - 主界面
    var body: some View {
        Form {
            backupLocationSection
            backupContentSection
            actionButtonsSection
        }
        .navigationTitle("备份与恢复")
        .navigationBarTitleDisplayMode(.inline) // 添加这一行来设置导航栏标题样式
        .fileImporter(
            isPresented: $isFilePickerPresented,
            allowedContentTypes: [.data],
            allowsMultipleSelection: false
        ) { result in
            switch result {
            case .success(let urls):
                guard let url = urls.first else { return }
                selectedFileURL = url
            case .failure(let error):
                print("文件选择错误: \(error.localizedDescription)")
            }
        }
    }
    
    // MARK: - 子视图组件 (移出 body)
    private var backupLocationSection: some View {
        Section(header: Text("备份位置")) {
            Picker("选择备份位置", selection: $selectedBackupLocation) {
                ForEach(BackupLocation.allCases, id: \.self) { location in
                    Text(location.rawValue)
                }
            }
            .pickerStyle(SegmentedPickerStyle())
        }
    }
    
    private var backupContentSection: some View {
        Section(header: Text("选择备份内容")) {
            ForEach(BackupItem.allCases) { item in
                MultipleSelectionRow(
                    title: item.rawValue,
                    isSelected: selectedItems.contains(item)
                ) {
                    Task { @MainActor in
                        selectedItems.toggle(item)
                    }
                }
            }
        }
    }
        
    // 将这些计算属性移到 BackupView 结构体的主体中，而不是嵌套在其他计算属性或 body 中
    var actionButtonsSection: some View { // 移除 private
        Section {
            backupActionButton
            restoreActionButton
            // statusIndicators // 暂时注释，因为其定义已被注释
        }
    }
    
    var backupActionButton: some View { // 移除 private
        Button {
            Task {
                do {
                    try await startBackup()
                } catch {
                    print("备份失败: \(error.localizedDescription)")
                }
            }
        } label: {
            Text("开始备份")
                .frame(maxWidth: .infinity)
        }
        .disabled(selectedItems.isEmpty || backupStatus == .inProgress)
    }
    
    var restoreActionButton: some View { // 移除 private
        Group {
            if selectedBackupLocation == .local {
                Button("选择备份文件") {
                    isFilePickerPresented = true
                }
            }
            
            Button {
                Task {
                    do {
                        try await startRestore()
                    } catch {
                        print("恢复失败: \(error.localizedDescription)")
                    }
                }
            } label: {
                Text("恢复备份")
                    .frame(maxWidth: .infinity)
            }
            .disabled(restoreStatus == .inProgress || (selectedBackupLocation == .local && selectedFileURL == nil))
        }
    }
    
    // var statusIndicators: some View { // 移除 private
    //     Group {
    //         // BackupStatusView(status: $backupStatus) // 暂时注释，因为找不到定义
    //         // RestoreStatusView(status: $restoreStatus) // 暂时注释，因为找不到定义
    //     }
    // }
    
    // MARK: - 业务逻辑 (移到 BackupView 结构体的主体中)
    @MainActor
    func startBackup() async throws { // 移除 private
        backupStatus = .inProgress
        coordinator.objectWillChange.send()
        
        do {
            try await coordinator.startBackup(to: selectedBackupLocation)
            await handleBackupResult(status: $backupStatus, success: true)
        } catch {
            await handleBackupResult(status: $backupStatus, success: false)
            throw error
        }
    }

    @MainActor
    func startRestore() async throws { // 移除 private
        // 如果是本地恢复但没有选择文件，则不执行恢复操作
        if selectedBackupLocation == .local && selectedFileURL == nil {
            print("本地恢复需要选择备份文件")
            return
        }
        
        restoreStatus = .inProgress
        coordinator.objectWillChange.send()
        
        do {
            try await coordinator.startRestore(from: selectedBackupLocation)
            await handleRestoreResult(status: $restoreStatus, success: true)
        } catch {
            await handleRestoreResult(status: $restoreStatus, success: false)
            throw error
        }
    }

    @MainActor
    func handleBackupResult(status: Binding<BackupStatus>, success: Bool) async { // 移除 private
        backupStatus = success ? .success : .failure
        try? await Task.sleep(nanoseconds: 2_000_000_000) // 2秒后重置状态
        backupStatus = .idle
    }

    @MainActor
    func handleRestoreResult(status: Binding<RestoreStatus>, success: Bool) async { // 移除 private
        restoreStatus = success ? .success : .failure
        try? await Task.sleep(nanoseconds: 2_000_000_000) // 2秒后重置状态
        restoreStatus = .idle
    }
    

} // BackupView 结构体结束
    
// MARK: - 辅助类型 (移到文件作用域)
extension BackupView {
    enum BackupItem: String, CaseIterable, Identifiable {
        case bookSources = "书源"
        case bookshelf = "书架"
        case readingProgress = "阅读进度"
        case theme = "主题设置"
        case readingSettings = "阅读设置"
        case accountSettings = "账号设置"
        
        var id: String { rawValue }
    }
}

// MARK: - 预览

#Preview {
    let config = ModelConfiguration(isStoredInMemoryOnly: true)
    let previewContainer = try! ModelContainer(
        for: Book.self, BookSource.self, Item.self, Bookmark.self, ReadingProgress.self, // 添加了 ReadingProgress
        configurations: config
    )
    let coordinator = ReaderCoordinator(modelContainer: previewContainer)
    
    // 使用 NavigationStack 包裹 BackupView 以提供导航上下文
    // 并确保 coordinator 被正确注入
    BackupView()
        .modelContainer(previewContainer)
        .environmentObject(coordinator)
}

// MARK: - 辅助扩展 (移到文件作用域)
extension Set {
    mutating func toggle(_ element: Element) {
        if contains(element) {
            remove(element)
        } else {
            insert(element)
        }
    }
}