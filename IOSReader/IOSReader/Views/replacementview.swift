// ReplacementView.swift
// 替换净化规则管理视图
// 功能:
// 1. 替换规则管理 - 提供文本内容替换规则的增删改查功能
// 2. 正则表达式支持 - 支持使用正则表达式进行复杂文本匹配和替换
// 3. 规则优先级设置 - 允许用户设置多条规则的执行顺序

import SwiftUI
import SwiftData // 添加 SwiftData 导入

struct ReplacementView: View {
    @EnvironmentObject private var coordinator: ReaderCoordinator
    
    var body: some View {
        Text("替换净化功能")
            .navigationTitle("替换净化")
    }
}

struct ReplacementView_Previews: PreviewProvider {
    static var previews: some View {
        // 创建一个临时的 ModelContainer 供预览使用
        let schema = Schema([
            User.self,
            Book.self,
            BookSource.self,
            Bookmark.self,
            ReadingProgress.self // 确保包含所有需要的模型
        ])
        let configuration = ModelConfiguration(schema: schema, isStoredInMemoryOnly: true)
        let container = try! ModelContainer(for: schema, configurations: [configuration])

        // 使用正确的初始化器
        let coordinator = ReaderCoordinator(modelContainer: container)
        // 明确返回视图
        return ReplacementView()
            .modelContainer(container) // 注入容器
            .environmentObject(coordinator) // 提供 ReaderCoordinator 环境对象
    }
}