import SwiftUI
import AVFoundation
import PDFKit
import SwiftData

// MARK: - TabBar State
@MainActor
private class TabBarState: ObservableObject, @unchecked Sendable {
    static let shared = TabBarState()
    @Published var isHidden = false
    private init() {}
}

/// 统一阅读视图
/// 整合了PDF和TXT阅读功能，使用策略模式处理不同文件格式
/// 核心功能:
/// 1. 自动识别文件类型并使用相应的渲染策略
/// 2. 共享UI组件和用户交互
/// 3. 统一的设置和书签管理
struct UnifiedReaderView: View {
    // MARK: - Properties
    let filePath: String
    
    // MARK: - State Management & Environment
    @State private var tabSelection: Int = 0 // State for TabView selection
    @State private var fileType: FileType = .unknown
    @State private var toolBarVisible = true // 工具栏显示状态
    @State private var bookTitle: String = "未知文件"
    @State private var isListening: Bool = false
    @State private var showBookmarks: Bool = false
    @State private var isNightMode: Bool = false
    @State private var showSettings: Bool = false
    @State private var showChapters: Bool = false // 章节列表状态
    @State private var showError: Bool = false // 错误提示状态
    @State private var errorMessage: String = "未知错误" // 错误信息
    @State private var isInitialized: Bool = false // 标记是否已初始化
    @State private var viewSize: CGSize = .zero // 视图尺寸
    @State private var isLayoutComplete: Bool = false // 布局完成标记
    
    // 文本朗读相关属性
    @State private var speechSynthesizer: AVSpeechSynthesizer? // 用于保存语音合成器实例
    
    // Environment variables
    @Environment(\.modelContext) private var modelContext // For accessing ModelContainer indirectly
    @Environment(\.presentationMode) var presentationMode
    @Environment(\.colorScheme) private var colorScheme
    @Environment(\.dismiss) private var dismiss
    
    // ViewModels and Coordinators
    @StateObject private var txtViewModel: ReaderViewModel
    // txtReaderCoordinator will be initialized in the init method using the txtViewModel instance
    @StateObject private var txtReaderCoordinator: ReaderCoordinator
    @EnvironmentObject var pdfCoordinator: ReaderCoordinator // For PDF and potentially shared state
    
    // MARK: - Initialization
    // 移除了不带 ModelContainer 的 init(filePath: String)，强制使用下面的初始化方法
    
    // Add a new initializer that accepts ModelContainer
    init(filePath: String, modelContainer: ModelContainer) {
        self.filePath = filePath
        self._txtViewModel = StateObject(wrappedValue: ReaderViewModel(modelContainer: modelContainer))
        
        let url = URL(fileURLWithPath: filePath)
        _bookTitle = State(initialValue: url.deletingPathExtension().lastPathComponent)
        
        let detectedType = FileType.detectType(from: filePath)
        _fileType = State(initialValue: detectedType)
        
        if detectedType == .unknown {
            _errorMessage = State(initialValue: "不支持的文件格式: \(url.pathExtension)")
            _showError = State(initialValue: true)
        }
        
        // Initialize txtReaderCoordinator after txtViewModel is definitely initialized
        // And ensure it uses the same modelContainer
        // ReaderCoordinator's init only takes modelContainer according to ReaderCoordinator.swift
        self._txtReaderCoordinator = StateObject(wrappedValue: ReaderCoordinator(modelContainer: modelContainer))
        // The assignment of viewModel to coordinator will be done in .onAppear
        // self._txtReaderCoordinator.wrappedValue.readerViewModel = _txtViewModel.wrappedValue
        
        _speechSynthesizer = State(initialValue: AVSpeechSynthesizer())
        print("UnifiedReaderView init: filePath = \(filePath), fileType = \(detectedType)")
    }
    
    // MARK: - Computed Properties
    private var backgroundColor: Color {
        if fileType == FileType.txt || fileType == FileType.epub {
            return isNightMode ? Color(red: 0.1, green: 0.1, blue: 0.1) : Color.white
        } else {
            return isNightMode ? Color.black : Color(.systemGray6)
        }
    }
    
    private var textColor: Color {
        if fileType == FileType.txt || fileType == FileType.epub {
            return isNightMode ? Color(red: 0.9, green: 0.9, blue: 0.9) : Color.black
        } else {
            return isNightMode ? Color(.systemGray) : Color.black
        }
    }
    
    // 监听夜间模式变化
    private func updateReaderColors() {
        if fileType == .txt || fileType == .epub {
            if isNightMode {
                txtViewModel.settings.backgroundColor = Color(red: 0.1, green: 0.1, blue: 0.1)
                txtViewModel.settings.textColor = Color(red: 0.9, green: 0.9, blue: 0.9)
            } else {
                txtViewModel.settings.backgroundColor = .white
                txtViewModel.settings.textColor = .black
            }
        }
    }
    
    private var progressText: String {
        switch fileType {
        case .pdf:
            guard let doc = pdfCoordinator.document, doc.pageCount > 0 else { return "0/0 (0%)" }
            let currentPage = tabSelection + 1
            let totalPages = doc.pageCount
            let percentage = totalPages > 0 ? Double(currentPage) / Double(totalPages) * 100 : 0
            return String(format: "%d/%d (%.0f%%)", currentPage, totalPages, percentage)
        case .txt, .epub:
            let currentPage = txtViewModel.currentPage + 1
            let totalPages = txtViewModel.totalPages
            let percentage = totalPages > 0 ? Double(currentPage) / Double(totalPages) * 100 : 0
            let chapterInfo = txtViewModel.currentChapterTitle.isEmpty ? "" : " - \(txtViewModel.currentChapterTitle)"
            return String(format: "%d/%d (%.0f%%) %@", currentPage, totalPages, percentage, chapterInfo)
        case .html:
            return "HTML (不支持)"
        case .unknown:
            return "未知格式"
        }
    }
    
    // MARK: - UI Components
    private var bottomToolbar: some View {
        VStack(spacing: 0) {
            HStack {
                Button {
                    showChapters = true
                } label: {
                    Image(systemName: "list.bullet")
                        .font(.title2)
                }
                .disabled(fileType == .pdf)
                
                Spacer()
                
                Text(progressText)
                    .font(.caption)
                    .lineLimit(1)
                    .truncationMode(.middle)
                
                Spacer()
                
                Button {
                    showSettings = true
                } label: {
                    Image(systemName: "gearshape")
                        .font(.title2)
                }
            }
            .padding(.horizontal, 16)
            .frame(height: 50)
            .background(backgroundColor.opacity(0.8))
            
            // 底部安全区域占位
            GeometryReader { geometry in
                Rectangle()
                    .fill(backgroundColor.opacity(0.8))
                    .frame(height: geometry.safeAreaInsets.bottom)
            }
            .frame(height: 0)
        }
        .transition(.move(edge: .bottom).combined(with: .opacity))
    }
    
    private var errorDisplayView: some View {
        VStack {
            Image(systemName: "exclamationmark.triangle.fill")
                .resizable()
                .scaledToFit()
                .frame(width: 50, height: 50)
                .foregroundColor(.orange)
            Text("加载错误")
                .font(.title)
                .padding(.bottom, 5)
            Text(errorMessage)
                .foregroundColor(.gray)
                .multilineTextAlignment(.center)
                .padding(.horizontal)
        }
    }
    
    private var contentDisplayView: some View {
        Group {
            switch fileType {
            case .pdf:
                TabView(selection: $tabSelection) {
                    pdfPagesView()
                }
                .tabViewStyle(.page(indexDisplayMode: .never))
                // onChange for tabSelection specific to PDF can remain if pdfCoordinator uses it,
                // or be handled within pdfPagesView / pdfCoordinator.
                // For now, keeping it simple. If pdfCoordinator.currentPage needs to sync with tabSelection,
                // that logic should be encapsulated or clearly defined for PDF context.
            case .txt, .epub:
                textPagesView() // This will now return a ReaderView
            case .unknown:
                Text("不支持的文件格式")
            case .html:
                Text("HTML 格式暂不支持")
                    .foregroundColor(.gray)
            }
        }
    }
    
    // MARK: - Helper Views
    private func pdfPagesView() -> some View {
        Group {
            if let doc = pdfCoordinator.document, doc.pageCount > 0 {
                ForEach(0..<doc.pageCount, id: \.self) { index in
                    PDFKitPageView(document: doc, pageNumber: index)
                        .tag(index)
                        .onTapGesture {
                            withAnimation {
                                toolBarVisible.toggle()
                            }
                        }
                }
            } else {
                let _ = print("UnifiedReaderView textPagesView（PDF）: Content not available or empty. txtViewModel.pageContent.isEmpty = \(txtViewModel.pageContent.isEmpty), isInitialized = \(isInitialized)")
                ProgressView("加载 PDF 中...")
                    .tag(0)
            }
        }
        .id(UUID())
    }
    
    @ViewBuilder
    private func textPagesView() -> some View {
        // 调试日志，观察 ViewModel 状态变化
        if txtViewModel.isLoading { // ViewModel 是否正在加载
            AnyView(ProgressView("加载内容中... \(Int(txtViewModel.loadingProgress * 100))%"))
        } else if let errorMessage = txtViewModel.errorMessage { // 是否有加载错误, 尝试使用 wrappedValue 访问
            AnyView(VStack {
                Text("加载失败: \(errorMessage)")
            })
        } else if !txtViewModel.pageContent.isEmpty { // 内容是否已成功加载并分页
            ReaderView(coordinator: txtReaderCoordinator, showToolbars: $toolBarVisible)
                .id(("\(filePath)") + "-txtReader") // 使用文件路径作为ID的一部分，确保书籍切换时视图刷新
                .onAppear {
                    print("ReaderView (for TXT/EPUB) appeared in UnifiedReaderView. filePath: \(filePath)")
                    // 可以在此或 ReaderView 内部调用 viewModel.updateViewSize 如果需要基于几何尺寸重新分页
                }
        } else { // 其他情况：非加载中、无错误、但内容为空 (例如，书籍本身为空或初始化时尚未加载)
            // 如果 isInitialized 为 false，表示 initializeReader 还未执行或未完成
            if !isInitialized {
                AnyView(ProgressView("正在准备阅读器..."))
            } else {
                // 如果已经初始化但内容仍为空，可能表示书籍确实无内容或分页未产生页面
                AnyView(Text("内容为空或正在加载。")
                    .foregroundColor(.gray))
            }
        }
    }
    
    // 新增：提取出的 ZStack 内容
    @ViewBuilder
    private var readerContentView: some View {
        ZStack {
            backgroundColor
                .ignoresSafeArea()
            
            if showError {
                errorDisplayView
            } else {
                // 调试日志，确认 contentDisplayView 何时被渲染
                let _ = print("UnifiedReaderView readerContentView: Rendering contentDisplayView. FileType: \(fileType), isInitialized: \(isInitialized), txtViewModel.isLoading: \(txtViewModel.isLoading)")
                contentDisplayView // contentDisplayView 内部的视图（ReaderView 或 PDFKitPageView）应处理自己的点击手势（如果需要）
                    .onTapGesture { // UnifiedReaderView 级别的点击手势，用于切换其自身的工具栏
                        withAnimation(.easeInOut(duration: 0.2)) {
                            toolBarVisible.toggle()
                        }
                    } // 尝试启用此处的点击手势
                    .padding(.top, fileType == .pdf ? 0 : 20) // 为TXT/EPUB格式添加顶部边距
                    .padding(.bottom, fileType == .pdf ? 0 : 20) // 为TXT/EPUB格式添加底部边距
                    .onChange(of: viewSize) { _, newSize in
                        if fileType == .txt || fileType == .epub {
                            print("UnifiedReaderView readerContentView: viewSize changed to \(newSize), updating txtViewModel.")
                            txtViewModel.updateReaderViewSize(newSize)
                        }
                    }
                    .onAppear {
                        isLayoutComplete = true
                        print("UnifiedReaderView readerContentView: Appeared. viewSize: \(viewSize)")
                        if fileType == .txt || fileType == .epub,
                           viewSize != .zero {
                            txtViewModel.updateReaderViewSize(viewSize)
                        }
                        // 设置初始颜色
                        updateReaderColors()
                    }
                    .onChange(of: isNightMode) { oldValue, newValue in
                        print("UnifiedReaderView: Night mode changed from \(oldValue) to \(newValue)")
                        updateReaderColors()
                    }
            }
            
            // 底部工具栏容器 - 只在非TXT/EPUB格式时显示，因为TXT/EPUB使用ReaderView的工具栏
            if fileType != .txt && fileType != .epub {
                VStack {
                    Spacer()
                    if toolBarVisible {
                        bottomToolbar
                    }
                }
                .ignoresSafeArea(.keyboard)
            }
        }
    }
    
    // MARK: - Main View
    var body: some View {
        // 监听夜间模式变化
        let _ = Self._printChanges()
        let _ = print("UnifiedReaderView body: isNightMode = \(isNightMode)")

        // NavigationView 已移除，以解决双导航栏问题
        GeometryReader { geometry in
            readerContentView
                .onAppear {
                    // 确保初始尺寸被设置和传递
                    if viewSize == .zero, geometry.size != .zero {
                        viewSize = geometry.size
                        if fileType == .txt || fileType == .epub {
                            txtViewModel.updateReaderViewSize(geometry.size)
                        }
                    }
                }
                .onChange(of: geometry.size) { _, newSize in
                    if viewSize != newSize {
                        viewSize = newSize
                        // 当视图尺寸变化时，也通知ViewModel
                        if fileType == .txt || fileType == .epub {
                            txtViewModel.updateReaderViewSize(newSize)
                        }
                    }
                }
                // 添加一个全局的点击手势来控制 UnifiedReaderView 的工具栏显隐
                // 这个手势会覆盖 contentDisplayView 内部视图的点击手势，需要小心处理
                // 或者，让 contentDisplayView 内部的视图通过某种方式通知 UnifiedReaderView
                // .onTapGesture { // 点击内容区域切换工具栏
                //     withAnimation {
                //         toolBarVisible.toggle()
                //     }
                // } // 暂时注释掉，优先使用 contentDisplayView 上的手势
        }
        // 根据文件类型决定是否显示工具栏
        .toolbar(toolBarVisible ? .visible : .hidden, for: .navigationBar)
        .toolbar { // 定义顶部导航栏内容
            // 工具栏内容
            ToolbarItem(placement: .navigationBarLeading) {
                Button {
                    speechSynthesizer?.stopSpeaking(at: .immediate)
                    dismiss()
                } label: {
                    Image(systemName: "chevron.left")
                        .font(.title2)
                }
            }
            
            ToolbarItem(placement: .navigationBarTrailing) {
                // 语音控制按钮（仅TXT/EPUB格式）
                if fileType == .txt || fileType == .epub {
                    Button {
                        toggleListening()
                    } label: {
                        Image(systemName: isListening ? "speaker.wave.3.fill" : "speaker.wave.2")
                            .font(.title2)
                    }
                }
                
                Button {
                    addOrRemoveBookmark()
                } label: {
                    Image(systemName: "bookmark")
                        .font(.title2)
                }
                .disabled(fileType == .pdf)
            }
        }
        .navigationBarTitleDisplayMode(.inline)
        //}
        // .navigationViewStyle(StackNavigationViewStyle()) 已移除
        .onAppear {
            self.txtReaderCoordinator.readerViewModel = self.txtViewModel
            initializeReader()
            // 为PDF格式初始化文档
            if fileType == .pdf {
                Task {
                    do {
                        let success = try await pdfCoordinator.startReading(from: filePath)
                        if !success {
                            errorMessage = "PDF加载失败"
                            showError = true
                        }
                    } catch {
                        print("PDF加载失败: \(error)")
                        errorMessage = "PDF加载失败: \(error.localizedDescription)"
                        showError = true
                    }
                }
            }
        }
            .sheet(isPresented: $showSettings) {
                if fileType == FileType.txt || fileType == FileType.epub {
                    ReaderSettingsView(settings: $txtViewModel.settings)
                } else {
                    Text("PDF 设置暂不可用")
                }
            }
            .sheet(isPresented: $showBookmarks) {
                if fileType == FileType.txt || fileType == FileType.epub {
                    SharedBookmarkManagementView(vm: txtViewModel)
                } else if fileType == .pdf {
                    BookmarkView().environmentObject(pdfCoordinator)
                } else {
                    Text("书签暂不可用")
                }
            }
            .sheet(isPresented: $showChapters) {
                if fileType == FileType.txt || fileType == FileType.epub {
                    SharedChapterListView(vm: txtViewModel) { index in
                        Task {
                            await txtViewModel.jumpToChapter(index)
                        }
                        showChapters = false
                    }
                } else {
                    Text("PDF 章节暂不可用")
                }
            }
            .onDisappear {
                speechSynthesizer?.stopSpeaking(at: .immediate)
                speechSynthesizer = nil
                Task { await txtViewModel.cancelLoading() }
            }
        }
    //}
        
    // MARK: - Methods
    func cancelLoading() async {
        await txtViewModel.cancelLoading()
    }
    
    private func initializeReader() {
        isInitialized = true
        if fileType == FileType.txt || fileType == FileType.epub {
            // Task 已在外部调用 initializeReader 的地方处理，此处不需要重复 Task
            // 确保 loadBook 是 async throws
            // try? await txtViewModel.loadBook(from: filePath)
            // 改为直接调用，因为 initializeReader 已经是 Task
            Task {
                do {
                    try await txtViewModel.loadBook(from: filePath)
                } catch {
                    // TODO：处理错误，例如显示一个提示
                    print("加载书籍失败: \(error)")
                }
            }
        }
    }
    
    private func toggleListening() {
        if isListening {
            speechSynthesizer?.stopSpeaking(at: .immediate)
            isListening = false
        } else {
            if let content = txtViewModel.pageContent[safe: txtViewModel.currentPage]?.string {
                let utterance = AVSpeechUtterance(string: content)
                utterance.voice = AVSpeechSynthesisVoice(language: "zh-CN")
                speechSynthesizer?.speak(utterance)
                isListening = true
            }
        }
    }
    
    private func addOrRemoveBookmark() {
        // 检查文件类型是否为支持书签的类型
        let isSupportedType = fileType == FileType.txt || fileType == FileType.epub
        
        if isSupportedType {
            let currentPage = txtViewModel.currentPage
            
            // 检查当前页是否已有书签
            let hasBookmark = txtViewModel.bookmarks.contains { bookmark in
                return bookmark.pageIndex == currentPage
            }
            
            if hasBookmark {
                // 如果已有书签，则删除
                if let bookmarkToRemove = txtViewModel.bookmarks.first(where: { $0.pageIndex == currentPage && $0.chapterIndex == txtViewModel.currentChapterIndex }) {
                    Task {
                        do {
                            try await txtViewModel.deleteBookmark(bookmarkToRemove)
                        } catch {
                            print("删除书签失败: \(error)")
                        }
                    }
                }
            } else {
                // 如果没有书签，则添加
                Task { await txtViewModel.addBookmark() }
            }
        }
    }
} // struct UnifiedReaderView 的结束大括号
