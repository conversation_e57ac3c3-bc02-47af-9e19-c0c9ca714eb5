import SwiftUI

struct MultipleSelectionRow: View {
    var title: String
    var isSelected: Bool
    var action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack {
                Image(systemName: isSelected ? "checkmark.square" : "square")
                    .foregroundColor(isSelected ? .blue : .gray)
                Text(title)
                Spacer()
            }
        }
        .foregroundColor(.primary)
    }
}

struct MultipleSelectionRow_Previews: PreviewProvider {
    static var previews: some View {
        Group {
            MultipleSelectionRow(
                title: "测试选项",
                isSelected: true,
                action: {}
            )
            MultipleSelectionRow(
                title: "测试选项",
                isSelected: false,
                action: {}
            )
        }
        .previewLayout(.sizeThatFits)
    }
}