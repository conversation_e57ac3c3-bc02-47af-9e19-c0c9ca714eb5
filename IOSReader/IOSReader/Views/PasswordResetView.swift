//
//  PasswordResetView.swift
//  IOSReader
//
//  密码重置视图
//  功能:
//  - 提供密码重置界面
//  - 邮箱验证和密码重置
//  - 分步骤引导用户完成重置
//

import SwiftUI
import SwiftData

struct PasswordResetView: View {
    @StateObject private var accountManager: AccountManager
    @Environment(\.dismiss) private var dismiss
    
    init() {
        let container = try! ModelContainer(for: User.self)
        self._accountManager = StateObject(wrappedValue: AccountManager(modelContainer: container))
    }
    
    // 表单状态
    @State private var email = ""
    @State private var verificationCode = ""
    @State private var newPassword = ""
    @State private var confirmPassword = ""
    @State private var showPassword = false
    @State private var showConfirmPassword = false
    
    // UI状态
    @State private var currentStep: ResetStep = .email
    @State private var isLoading = false
    @State private var showingAlert = false
    @State private var alertMessage = ""
    @State private var countdown = 0
    @State private var timer: Timer?
    
    enum ResetStep {
        case email
        case verification
        case newPassword
        case success
    }
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // 头部
                    headerSection
                    
                    // 进度指示器
                    progressIndicator
                    
                    // 内容区域
                    contentSection
                    
                    // 按钮区域
                    buttonSection
                    
                    Spacer(minLength: 50)
                }
                .padding(.horizontal, 24)
                .padding(.top, 20)
            }
            .navigationTitle("重置密码")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden(true)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        dismiss()
                    }
                }
            }
        }
        .alert("提示", isPresented: $showingAlert) {
            Button("确定", role: .cancel) {}
        } message: {
            Text(alertMessage)
        }
        .onDisappear {
            timer?.invalidate()
        }
    }
    
    // MARK: - 视图组件
    
    private var headerSection: some View {
        VStack(spacing: 16) {
            Image(systemName: stepIcon)
                .font(.system(size: 60))
                .foregroundStyle(
                    LinearGradient(
                        colors: [.orange, .red],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
            
            VStack(spacing: 8) {
                Text(stepTitle)
                    .font(.title2.bold())
                    .foregroundColor(.primary)
                
                Text(stepSubtitle)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
        }
    }
    
    private var progressIndicator: some View {
        HStack(spacing: 8) {
            ForEach(1...4, id: \.self) { step in
                Circle()
                    .fill(step <= currentStepNumber ? Color.orange : Color.gray.opacity(0.3))
                    .frame(width: 12, height: 12)
                
                if step < 4 {
                    Rectangle()
                        .fill(step < currentStepNumber ? Color.orange : Color.gray.opacity(0.3))
                        .frame(height: 2)
                        .frame(maxWidth: .infinity)
                }
            }
        }
        .padding(.horizontal, 40)
    }
    
    private var contentSection: some View {
        VStack(spacing: 20) {
            switch currentStep {
            case .email:
                emailInputSection
            case .verification:
                verificationSection
            case .newPassword:
                newPasswordSection
            case .success:
                successSection
            }
        }
    }
    
    private var emailInputSection: some View {
        VStack(spacing: 16) {
            VStack(alignment: .leading, spacing: 8) {
                Text("邮箱地址")
                    .font(.subheadline.weight(.medium))
                    .foregroundColor(.secondary)
                
                HStack {
                    Image(systemName: "envelope.circle")
                        .foregroundColor(.secondary)
                        .frame(width: 20)
                    
                    TextField("请输入注册时使用的邮箱", text: $email)
                        .textFieldStyle(PlainTextFieldStyle())
                        .autocapitalization(.none)
                        .disableAutocorrection(true)
                        .keyboardType(.emailAddress)
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 12)
                .background(Color(.systemGray6))
                .cornerRadius(12)
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.orange.opacity(0.3), lineWidth: 1)
                )
            }
            
            Text("我们将向您的邮箱发送验证码，请确保邮箱地址正确。")
                .font(.caption)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal, 16)
        }
    }
    
    private var verificationSection: some View {
        VStack(spacing: 16) {
            VStack(alignment: .leading, spacing: 8) {
                Text("验证码")
                    .font(.subheadline.weight(.medium))
                    .foregroundColor(.secondary)
                
                HStack {
                    Image(systemName: "key.horizontal")
                        .foregroundColor(.secondary)
                        .frame(width: 20)
                    
                    TextField("请输入6位验证码", text: $verificationCode)
                        .textFieldStyle(PlainTextFieldStyle())
                        .keyboardType(.numberPad)
                        .onChange(of: verificationCode) { _, newValue in
                            // 限制输入长度
                            if newValue.count > 6 {
                                verificationCode = String(newValue.prefix(6))
                            }
                        }
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 12)
                .background(Color(.systemGray6))
                .cornerRadius(12)
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.orange.opacity(0.3), lineWidth: 1)
                )
            }
            
            HStack {
                Text("验证码已发送至 \(email)")
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                Spacer()
                
                if countdown > 0 {
                    Text("\(countdown)s后重发")
                        .font(.caption)
                        .foregroundColor(.secondary)
                } else {
                    Button("重新发送") {
                        sendVerificationCode()
                    }
                    .font(.caption)
                    .foregroundColor(.orange)
                }
            }
            .padding(.horizontal, 4)
        }
    }
    
    private var newPasswordSection: some View {
        VStack(spacing: 16) {
            // 新密码输入
            VStack(alignment: .leading, spacing: 8) {
                Text("新密码")
                    .font(.subheadline.weight(.medium))
                    .foregroundColor(.secondary)
                
                HStack {
                    Image(systemName: "lock.circle")
                        .foregroundColor(.secondary)
                        .frame(width: 20)
                    
                    Group {
                        if showPassword {
                            TextField("请输入新密码（至少8位）", text: $newPassword)
                        } else {
                            SecureField("请输入新密码（至少8位）", text: $newPassword)
                        }
                    }
                    .textFieldStyle(PlainTextFieldStyle())
                    .autocapitalization(.none)
                    .disableAutocorrection(true)
                    
                    Button(action: {
                        showPassword.toggle()
                    }) {
                        Image(systemName: showPassword ? "eye.slash" : "eye")
                            .foregroundColor(.secondary)
                    }
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 12)
                .background(Color(.systemGray6))
                .cornerRadius(12)
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.orange.opacity(0.3), lineWidth: 1)
                )
            }
            
            // 确认密码输入
            VStack(alignment: .leading, spacing: 8) {
                Text("确认新密码")
                    .font(.subheadline.weight(.medium))
                    .foregroundColor(.secondary)
                
                HStack {
                    Image(systemName: "lock.circle")
                        .foregroundColor(.secondary)
                        .frame(width: 20)
                    
                    Group {
                        if showConfirmPassword {
                            TextField("请再次输入新密码", text: $confirmPassword)
                        } else {
                            SecureField("请再次输入新密码", text: $confirmPassword)
                        }
                    }
                    .textFieldStyle(PlainTextFieldStyle())
                    .autocapitalization(.none)
                    .disableAutocorrection(true)
                    
                    Button(action: {
                        showConfirmPassword.toggle()
                    }) {
                        Image(systemName: showConfirmPassword ? "eye.slash" : "eye")
                            .foregroundColor(.secondary)
                    }
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 12)
                .background(Color(.systemGray6))
                .cornerRadius(12)
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.orange.opacity(0.3), lineWidth: 1)
                )
            }
            
            // 密码要求提示
            VStack(alignment: .leading, spacing: 4) {
                Text("密码要求:")
                    .font(.caption.weight(.medium))
                    .foregroundColor(.secondary)
                
                HStack {
                    Image(systemName: newPassword.count >= 8 ? "checkmark.circle.fill" : "circle")
                        .foregroundColor(newPassword.count >= 8 ? .green : .secondary)
                        .font(.caption)
                    
                    Text("至少8个字符")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                HStack {
                    Image(systemName: newPassword == confirmPassword && !confirmPassword.isEmpty ? "checkmark.circle.fill" : "circle")
                        .foregroundColor(newPassword == confirmPassword && !confirmPassword.isEmpty ? .green : .secondary)
                        .font(.caption)
                    
                    Text("两次输入密码一致")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            .frame(maxWidth: .infinity, alignment: .leading)
            .padding(.horizontal, 4)
        }
    }
    
    private var successSection: some View {
        VStack(spacing: 20) {
            Image(systemName: "checkmark.circle.fill")
                .font(.system(size: 80))
                .foregroundColor(.green)
                .symbolEffect(.bounce, options: .speed(0.5))
            
            VStack(spacing: 8) {
                Text("密码重置成功")
                    .font(.title2.bold())
                    .foregroundColor(.primary)
                
                Text("您的密码已成功重置，请使用新密码登录。")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
        }
        .padding(.vertical, 40)
    }
    
    private var buttonSection: some View {
        VStack(spacing: 16) {
            switch currentStep {
            case .email:
                Button(action: sendVerificationCode) {
                    HStack {
                        if isLoading {
                            ProgressView()
                                .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                .scaleEffect(0.8)
                        } else {
                            Image(systemName: "paperplane.fill")
                                .font(.title3)
                        }
                        
                        Text(isLoading ? "发送中..." : "发送验证码")
                            .font(.headline)
                            .fontWeight(.semibold)
                    }
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .frame(height: 50)
                    .background(Color.orange)
                    .cornerRadius(12)
                }
                .disabled(isLoading || email.isEmpty || !isValidEmail(email))
                .opacity(email.isEmpty || !isValidEmail(email) ? 0.6 : 1.0)
                
            case .verification:
                Button(action: verifyCode) {
                    HStack {
                        if isLoading {
                            ProgressView()
                                .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                .scaleEffect(0.8)
                        } else {
                            Image(systemName: "checkmark.circle.fill")
                                .font(.title3)
                        }
                        
                        Text(isLoading ? "验证中..." : "验证")
                            .font(.headline)
                            .fontWeight(.semibold)
                    }
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .frame(height: 50)
                    .background(Color.orange)
                    .cornerRadius(12)
                }
                .disabled(isLoading || verificationCode.count != 6)
                .opacity(verificationCode.count != 6 ? 0.6 : 1.0)
                
            case .newPassword:
                Button(action: resetPassword) {
                    HStack {
                        if isLoading {
                            ProgressView()
                                .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                .scaleEffect(0.8)
                        } else {
                            Image(systemName: "key.fill")
                                .font(.title3)
                        }
                        
                        Text(isLoading ? "重置中..." : "重置密码")
                            .font(.headline)
                            .fontWeight(.semibold)
                    }
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .frame(height: 50)
                    .background(
                        LinearGradient(
                            colors: [.orange, .red],
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .cornerRadius(12)
                    .shadow(color: .orange.opacity(0.3), radius: 8, x: 0, y: 4)
                }
                .disabled(isLoading || !isPasswordValid)
                .opacity(isPasswordValid ? 1.0 : 0.6)
                
            case .success:
                Button(action: {
                    dismiss()
                }) {
                    HStack {
                        Image(systemName: "arrow.right.circle.fill")
                            .font(.title3)
                        
                        Text("返回登录")
                            .font(.headline)
                            .fontWeight(.semibold)
                    }
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .frame(height: 50)
                    .background(Color.green)
                    .cornerRadius(12)
                }
            }
        }
    }
    
    // MARK: - 计算属性
    
    private var stepIcon: String {
        switch currentStep {
        case .email:
            return "envelope.circle"
        case .verification:
            return "key.horizontal"
        case .newPassword:
            return "lock.rotation"
        case .success:
            return "checkmark.circle.fill"
        }
    }
    
    private var stepTitle: String {
        switch currentStep {
        case .email:
            return "输入邮箱"
        case .verification:
            return "验证身份"
        case .newPassword:
            return "设置新密码"
        case .success:
            return "重置成功"
        }
    }
    
    private var stepSubtitle: String {
        switch currentStep {
        case .email:
            return "请输入您注册时使用的邮箱地址"
        case .verification:
            return "请输入发送到您邮箱的验证码"
        case .newPassword:
            return "请设置一个安全的新密码"
        case .success:
            return "您的密码已成功重置"
        }
    }
    
    private var currentStepNumber: Int {
        switch currentStep {
        case .email: return 1
        case .verification: return 2
        case .newPassword: return 3
        case .success: return 4
        }
    }
    
    private var isPasswordValid: Bool {
        newPassword.count >= 8 && newPassword == confirmPassword && !confirmPassword.isEmpty
    }
    
    // MARK: - 方法
    
    private func isValidEmail(_ email: String) -> Bool {
        let emailRegex = "^[A-Z0-9a-z._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$"
        let emailPredicate = NSPredicate(format: "SELF MATCHES %@", emailRegex)
        return emailPredicate.evaluate(with: email)
    }
    
    private func sendVerificationCode() {
        guard isValidEmail(email) else {
            showAlert(message: "请输入有效的邮箱地址")
            return
        }
        
        isLoading = true
        
        // TODO: 实现发送验证码逻辑
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) {
            isLoading = false
            currentStep = .verification
            startCountdown()
            showAlert(message: "验证码已发送到您的邮箱")
        }
    }
    
    private func verifyCode() {
        guard verificationCode.count == 6 else {
            showAlert(message: "请输入6位验证码")
            return
        }
        
        isLoading = true
        
        // TODO: 实现验证码验证逻辑
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            isLoading = false
            // 模拟验证成功
            if verificationCode == "123456" {
                currentStep = .newPassword
            } else {
                showAlert(message: "验证码错误，请重新输入")
            }
        }
    }
    
    private func resetPassword() {
        guard isPasswordValid else {
            showAlert(message: "请检查密码输入")
            return
        }
        
        isLoading = true
        
        Task {
            do {
                // TODO: 实现密码重置逻辑
                try await accountManager.resetPassword(email: email)
                
                await MainActor.run {
                    isLoading = false
                    currentStep = .success
                }
            } catch {
                await MainActor.run {
                    isLoading = false
                    showAlert(message: error.localizedDescription)
                }
            }
        }
    }
    
    @MainActor
    private func startCountdown() {
        countdown = 60
        timer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { _ in
            Task { @MainActor in
                if self.countdown > 0 {
                    self.countdown -= 1
                } else {
                    self.timer?.invalidate()
                    self.timer = nil
                }
            }
        }
    }
    
    private func showAlert(message: String) {
        alertMessage = message
        showingAlert = true
    }
}

// MARK: - 预览

#Preview {
    Group {
        if let container = try? ModelContainer(for: User.self, Book.self, BookSource.self, Bookmark.self, ReadingProgress.self) {
            PasswordResetView()
                .environmentObject(ReaderCoordinator(modelContainer: container))
        } else {
            Text("预览错误")
        }
    }
}