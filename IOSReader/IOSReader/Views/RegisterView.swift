//
//  RegisterView.swift
//  IOSReader
//
//  注册视图
//  功能:
//  - 提供用户注册界面
//  - 用户名、邮箱、密码输入和验证
//  - 密码强度检查
//  - 服务条款和隐私政策确认
//  - 注册成功后自动登录
//

import SwiftUI
import SwiftData

struct RegisterView: View {
    @EnvironmentObject var accountManager: AccountManager
    @Environment(\.dismiss) private var dismiss
    
    // 表单状态
    @State private var username = ""
    @State private var email = ""
    @State private var password = ""
    @State private var confirmPassword = ""
    @State private var nickname = ""
    @State private var showPassword = false
    @State private var showConfirmPassword = false
    @State private var agreeToTerms = false
    
    // UI状态
    @State private var isLoading = false
    @State private var showingAlert = false
    @State private var alertMessage = ""
    @State private var currentStep = 1
    
    // 验证状态
    @State private var usernameValidation: ValidationResult = .none
    @State private var emailValidation: ValidationResult = .none
    @State private var passwordValidation: ValidationResult = .none
    @State private var confirmPasswordValidation: ValidationResult = .none
    
    enum ValidationResult {
        case none
        case valid
        case invalid(String)
        
        var isValid: Bool {
            if case .valid = self { return true }
            return false
        }
        
        var errorMessage: String? {
            if case .invalid(let message) = self { return message }
            return nil
        }
    }
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // 头部
                    headerSection
                    
                    // 进度指示器
                    progressIndicator
                    
                    // 注册表单
                    if currentStep == 1 {
                        basicInfoSection
                    } else {
                        passwordSection
                    }
                    
                    // 按钮区域
                    buttonSection
                    
                    Spacer(minLength: 50)
                }
                .padding(.horizontal, 24)
                .padding(.top, 20)
            }
            .navigationTitle("创建账号")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden(true)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        dismiss()
                    }
                }
            }
        }
        .alert("注册提示", isPresented: $showingAlert) {
            Button("确定", role: .cancel) {}
        } message: {
            Text(alertMessage)
        }
        .onChange(of: username) { _, newValue in
            validateUsername(newValue)
        }
        .onChange(of: email) { _, newValue in
            validateEmail(newValue)
        }
        .onChange(of: password) { _, newValue in
            validatePassword(newValue)
        }
        .onChange(of: confirmPassword) { _, newValue in
            validateConfirmPassword(newValue)
        }
        .onChange(of: accountManager.isLoggedIn) { _, isLoggedIn in
            if isLoggedIn {
                dismiss()
            }
        }
    }
    
    // MARK: - 视图组件
    
    private var headerSection: some View {
        VStack(spacing: 16) {
            Image(systemName: "person.badge.plus")
                .font(.system(size: 60))
                .foregroundStyle(
                    LinearGradient(
                        colors: [.green, .blue],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
            
            VStack(spacing: 8) {
                Text("加入阅读社区")
                    .font(.title2.bold())
                    .foregroundColor(.primary)
                
                Text("创建您的专属阅读账号")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
        }
    }
    
    private var progressIndicator: some View {
        HStack(spacing: 16) {
            ForEach(1...2, id: \.self) { step in
                HStack {
                    Circle()
                        .fill(step <= currentStep ? Color.blue : Color.gray.opacity(0.3))
                        .frame(width: 24, height: 24)
                        .overlay(
                            Text("\(step)")
                                .font(.caption.bold())
                                .foregroundColor(step <= currentStep ? .white : .gray)
                        )
                    
                    if step < 2 {
                        Rectangle()
                            .fill(step < currentStep ? Color.blue : Color.gray.opacity(0.3))
                            .frame(height: 2)
                            .frame(maxWidth: .infinity)
                    }
                }
            }
        }
        .padding(.horizontal, 40)
    }
    
    private var basicInfoSection: some View {
        VStack(spacing: 20) {
            Text("基本信息")
                .font(.headline)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            // 用户名输入
            inputField(
                title: "用户名",
                placeholder: "请输入用户名（3-20个字符）",
                text: $username,
                icon: "person.circle",
                validation: usernameValidation
            )
            
            // 邮箱输入
            inputField(
                title: "邮箱地址",
                placeholder: "请输入邮箱地址",
                text: $email,
                icon: "envelope.circle",
                keyboardType: .emailAddress,
                validation: emailValidation
            )
            
            // 昵称输入（可选）
            inputField(
                title: "昵称（可选）",
                placeholder: "请输入显示昵称",
                text: $nickname,
                icon: "star.circle"
            )
        }
    }
    
    private var passwordSection: some View {
        VStack(spacing: 20) {
            Text("设置密码")
                .font(.headline)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            // 密码输入
            passwordField(
                title: "密码",
                placeholder: "请输入密码（至少8位）",
                text: $password,
                showPassword: $showPassword,
                validation: passwordValidation
            )
            
            // 确认密码
            passwordField(
                title: "确认密码",
                placeholder: "请再次输入密码",
                text: $confirmPassword,
                showPassword: $showConfirmPassword,
                validation: confirmPasswordValidation
            )
            
            // 密码强度指示器
            if !password.isEmpty {
                passwordStrengthIndicator
            }
            
            // 服务条款
            termsSection
        }
    }
    
    private var passwordStrengthIndicator: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("密码强度")
                .font(.caption)
                .foregroundColor(.secondary)
            
            let strength = getPasswordStrength(password)
            
            HStack(spacing: 4) {
                ForEach(0..<4, id: \.self) { index in
                    Rectangle()
                        .fill(index < strength.level ? strength.color : Color.gray.opacity(0.3))
                        .frame(height: 4)
                        .cornerRadius(2)
                }
            }
            
            Text(strength.description)
                .font(.caption)
                .foregroundColor(strength.color)
        }
        .padding(.horizontal, 4)
    }
    
    private var termsSection: some View {
        VStack(spacing: 12) {
            Button(action: {
                agreeToTerms.toggle()
            }) {
                HStack(alignment: .top, spacing: 12) {
                    Image(systemName: agreeToTerms ? "checkmark.square.fill" : "square")
                        .foregroundColor(agreeToTerms ? .blue : .secondary)
                        .font(.title3)
                    
                    VStack(alignment: .leading, spacing: 4) {
                        Text("我已阅读并同意")
                            .font(.subheadline)
                            .foregroundColor(.primary)
                        
                        HStack(spacing: 4) {
                            Button("《服务条款》") {
                                // TODO: 显示服务条款
                            }
                            .font(.subheadline)
                            .foregroundColor(.blue)
                            
                            Text("和")
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                            
                            Button("《隐私政策》") {
                                // TODO: 显示隐私政策
                            }
                            .font(.subheadline)
                            .foregroundColor(.blue)
                        }
                    }
                    
                    Spacer()
                }
            }
            .buttonStyle(PlainButtonStyle())
        }
        .padding(.horizontal, 4)
    }
    
    private var buttonSection: some View {
        VStack(spacing: 16) {
            if currentStep == 1 {
                // 下一步按钮
                Button(action: {
                    withAnimation(.easeInOut(duration: 0.3)) {
                        currentStep = 2
                    }
                }) {
                    HStack {
                        Text("下一步")
                            .font(.headline)
                            .fontWeight(.semibold)
                        
                        Image(systemName: "arrow.right")
                            .font(.title3)
                    }
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .frame(height: 50)
                    .background(Color.blue)
                    .cornerRadius(12)
                }
                .disabled(!isStep1Valid)
                .opacity(isStep1Valid ? 1.0 : 0.6)
            } else {
                // 注册按钮
                Button(action: performRegister) {
                    HStack {
                        if isLoading {
                            ProgressView()
                                .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                .scaleEffect(0.8)
                        } else {
                            Image(systemName: "checkmark.circle.fill")
                                .font(.title3)
                        }
                        
                        Text(isLoading ? "注册中..." : "完成注册")
                            .font(.headline)
                            .fontWeight(.semibold)
                    }
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .frame(height: 50)
                    .background(
                        LinearGradient(
                            colors: [.green, .blue],
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .cornerRadius(12)
                    .shadow(color: .green.opacity(0.3), radius: 8, x: 0, y: 4)
                }
                .disabled(!isStep2Valid || isLoading)
                .opacity(isStep2Valid ? 1.0 : 0.6)
                
                // 返回上一步
                Button("返回上一步") {
                    withAnimation(.easeInOut(duration: 0.3)) {
                        currentStep = 1
                    }
                }
                .font(.subheadline)
                .foregroundColor(.blue)
            }
        }
    }
    
    // MARK: - 辅助方法
    
    private func inputField(
        title: String,
        placeholder: String,
        text: Binding<String>,
        icon: String,
        keyboardType: UIKeyboardType = .default,
        validation: ValidationResult = .none
    ) -> some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(title)
                .font(.subheadline.weight(.medium))
                .foregroundColor(.secondary)
            
            HStack {
                Image(systemName: icon)
                    .foregroundColor(.secondary)
                    .frame(width: 20)
                
                TextField(placeholder, text: text)
                    .textFieldStyle(PlainTextFieldStyle())
                    .autocapitalization(.none)
                    .disableAutocorrection(true)
                    .keyboardType(keyboardType)
                
                if validation.isValid {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.green)
                } else if validation.errorMessage != nil {
                    Image(systemName: "exclamationmark.circle.fill")
                        .foregroundColor(.red)
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(Color(.systemGray6))
            .cornerRadius(12)
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(
                        validation.isValid ? Color.green.opacity(0.5) :
                        validation.errorMessage != nil ? Color.red.opacity(0.5) :
                        Color.blue.opacity(0.3),
                        lineWidth: 1
                    )
            )
            
            if let errorMessage = validation.errorMessage {
                Text(errorMessage)
                    .font(.caption)
                    .foregroundColor(.red)
                    .padding(.horizontal, 4)
            }
        }
    }
    
    private func passwordField(
        title: String,
        placeholder: String,
        text: Binding<String>,
        showPassword: Binding<Bool>,
        validation: ValidationResult = .none
    ) -> some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(title)
                .font(.subheadline.weight(.medium))
                .foregroundColor(.secondary)
            
            HStack {
                Image(systemName: "lock.circle")
                    .foregroundColor(.secondary)
                    .frame(width: 20)
                
                Group {
                    if showPassword.wrappedValue {
                        TextField(placeholder, text: text)
                    } else {
                        SecureField(placeholder, text: text)
                    }
                }
                .textFieldStyle(PlainTextFieldStyle())
                .autocapitalization(.none)
                .disableAutocorrection(true)
                
                Button(action: {
                    showPassword.wrappedValue.toggle()
                }) {
                    Image(systemName: showPassword.wrappedValue ? "eye.slash" : "eye")
                        .foregroundColor(.secondary)
                }
                
                if validation.isValid {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.green)
                } else if validation.errorMessage != nil {
                    Image(systemName: "exclamationmark.circle.fill")
                        .foregroundColor(.red)
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(Color(.systemGray6))
            .cornerRadius(12)
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(
                        validation.isValid ? Color.green.opacity(0.5) :
                        validation.errorMessage != nil ? Color.red.opacity(0.5) :
                        Color.blue.opacity(0.3),
                        lineWidth: 1
                    )
            )
            
            if let errorMessage = validation.errorMessage {
                Text(errorMessage)
                    .font(.caption)
                    .foregroundColor(.red)
                    .padding(.horizontal, 4)
            }
        }
    }
    
    // MARK: - 验证逻辑
    
    private var isStep1Valid: Bool {
        usernameValidation.isValid && emailValidation.isValid
    }
    
    private var isStep2Valid: Bool {
        passwordValidation.isValid && confirmPasswordValidation.isValid && agreeToTerms
    }
    
    private func validateUsername(_ username: String) {
        if username.isEmpty {
            usernameValidation = .none
        } else if username.count < 3 {
            usernameValidation = .invalid("用户名至少需要3个字符")
        } else if username.count > 20 {
            usernameValidation = .invalid("用户名不能超过20个字符")
        } else if !username.allSatisfy({ $0.isLetter || $0.isNumber || $0 == "_" }) {
            usernameValidation = .invalid("用户名只能包含字母、数字和下划线")
        } else {
            usernameValidation = .valid
        }
    }
    
    private func validateEmail(_ email: String) {
        if email.isEmpty {
            emailValidation = .none
        } else if !email.contains("@") || !email.contains(".") {
            emailValidation = .invalid("请输入有效的邮箱地址")
        } else {
            let emailRegex = "^[A-Z0-9a-z._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$"
            let emailPredicate = NSPredicate(format: "SELF MATCHES %@", emailRegex)
            if emailPredicate.evaluate(with: email) {
                emailValidation = .valid
            } else {
                emailValidation = .invalid("邮箱格式不正确")
            }
        }
    }
    
    private func validatePassword(_ password: String) {
        if password.isEmpty {
            passwordValidation = .none
        } else if password.count < 8 {
            passwordValidation = .invalid("密码至少需要8个字符")
        } else if password.count > 50 {
            passwordValidation = .invalid("密码不能超过50个字符")
        } else {
            passwordValidation = .valid
        }
    }
    
    private func validateConfirmPassword(_ confirmPassword: String) {
        if confirmPassword.isEmpty {
            confirmPasswordValidation = .none
        } else if confirmPassword != password {
            confirmPasswordValidation = .invalid("两次输入的密码不一致")
        } else {
            confirmPasswordValidation = .valid
        }
    }
    
    private func getPasswordStrength(_ password: String) -> (level: Int, color: Color, description: String) {
        var score = 0
        
        if password.count >= 8 { score += 1 }
        if password.count >= 12 { score += 1 }
        if password.rangeOfCharacter(from: .lowercaseLetters) != nil { score += 1 }
        if password.rangeOfCharacter(from: .uppercaseLetters) != nil { score += 1 }
        if password.rangeOfCharacter(from: .decimalDigits) != nil { score += 1 }
        if password.rangeOfCharacter(from: CharacterSet(charactersIn: "!@#$%^&*()_+-=[]{}|;:,.<>?")) != nil { score += 1 }
        
        switch score {
        case 0...2:
            return (1, .red, "弱")
        case 3...4:
            return (2, .orange, "中等")
        case 5:
            return (3, .yellow, "强")
        default:
            return (4, .green, "很强")
        }
    }
    
    // MARK: - 注册逻辑
    
    private func performRegister() {
        guard isStep2Valid else {
            showAlert(message: "请完善所有必填信息")
            return
        }
        
        isLoading = true
        
        Task {
            do {
                let result = try await accountManager.register(
                    username: username,
                    email: email,
                    password: password,
                    nickname: nickname.isEmpty ? username : nickname
                )
                
                await MainActor.run {
                    isLoading = false
                    
                    switch result {
                    case .success(let user):
                        print("注册成功: \(user.nickname)")
                        // 注册成功后会通过onChange自动关闭视图
                    case .failure(let error):
                        showAlert(message: error.localizedDescription)
                    }
                }
            } catch {
                await MainActor.run {
                    isLoading = false
                    showAlert(message: error.localizedDescription)
                }
            }
        }
    }
    
    private func showAlert(message: String) {
        alertMessage = message
        showingAlert = true
    }
}

// MARK: - 预览

#Preview {
    Group {
        if let container = try? ModelContainer(for: User.self, Book.self, BookSource.self, Bookmark.self, ReadingProgress.self) {
            RegisterView()
                .environmentObject(ReaderCoordinator(modelContainer: container))
        } else {
            Text("预览错误")
        }
    }
}