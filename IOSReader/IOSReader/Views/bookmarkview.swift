// BookmarkView.swift
// 书签管理视图，负责展示和管理用户的阅读书签
// 主要功能：
// 1. 书签列表展示
// 2. 书签添加/删除
// 3. 书签分类管理
// 主要UI组件：
// - 书签列表视图
// - 书签添加/删除按钮
// - 书签分类选择器

import SwiftUI
import SwiftData // 导入 SwiftData

struct BookmarkView: View {
    @EnvironmentObject var coordinator: ReaderCoordinator
    @State private var showingAddBookmarkSheet = false
    @State private var newBookmarkDescription = ""
    
    var body: some View {
        VStack {
            if let currentBook = coordinator.currentBook {
                let bookId = currentBook.id.uuidString
                let bookmarks = coordinator.bookmarks[bookId] ?? []
                
                if bookmarks.isEmpty {
                    emptyBookmarksView
                } else {
                    List {
                        ForEach(bookmarks) { bookmark in
                            bookmarkRow(bookmark: bookmark)
                        }
                        .onDelete { indexSet in
                            // 修改：直接调用 coordinator 的 deleteBookmark 方法
                            for index in indexSet {
                                let bookmarkToDelete = bookmarks[index]
                                coordinator.deleteBookmark(bookmarkToDelete)
                            }
                        }
                    }
                }
            } else {
                noBookSelectedView
            }
        }
        .navigationTitle("书签")
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                Button(action: { showingAddBookmarkSheet = true }) {
                    Image(systemName: "plus")
                }
                .disabled(coordinator.currentBook == nil)
            }
        }
        .sheet(isPresented: $showingAddBookmarkSheet) {
            addBookmarkView
        }
    }
    //    // MARK: - 辅助视图
    
    // 空书签视图
    private var emptyBookmarksView: some View {
        VStack(spacing: 16) {
            Image(systemName: "bookmark.slash")
                .font(.system(size: 40))
                .foregroundColor(.accentColor)
                .symbolEffect(.bounce)
            Text("暂无书签")
                .font(.title2.bold())
                .padding(.top, 8)
            Text("阅读时添加书签，方便下次继续阅读")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal)
            
            Button("添加书签") {
                showingAddBookmarkSheet = true
            }
            .buttonStyle(.borderedProminent)
            .padding(.top)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .padding()
    }
    
    // 无书籍选择视图
    private var noBookSelectedView: some View {
        VStack(spacing: 16) {
            Image(systemName: "book.closed")
                .font(.system(size: 40))
                .foregroundColor(.gray)
            Text("未选择书籍")
                .font(.title2.bold())
                .padding(.top, 8)
            Text("请先选择一本书籍，然后添加书签")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .padding()
    }
    
    // 书签行视图
    private func bookmarkRow(bookmark: Bookmark) -> some View {
        VStack(alignment: .leading, spacing: 4) {
            HStack {
                // 修正：使用 displayText() 方法获取书签显示文本
                Text(bookmark.displayText())
                    .font(.headline)
                    .lineLimit(1)
                Spacer()
                Text("第 \(bookmark.pageIndex + 1) 页")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            // createdAt 是非可选的 Date，可以直接使用
            Text(formatDate(bookmark.createdAt))
                .font(.caption)
                .foregroundColor(.secondary)
            
        }
        .padding(.vertical, 4)
        .contentShape(Rectangle())
        .onTapGesture {
            coordinator.jumpToBookmark(bookmark)
        }
    }
    
    // 格式化日期
    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        return formatter.string(from: date)
    }
    
    // 添加书签视图
    private var addBookmarkView: some View {
        NavigationView {
            Form {
                Section(header: Text("书签信息")) {
                    if let book = coordinator.currentBook {
                        Text("书籍: \(book.title)") // 修正：确认插值
                        Text("当前位置: 第 \(coordinator.currentChapterIndex + 1) 章 第 \(coordinator.currentPage + 1) 页") // 修正：调整空格并重新确认插值
                    }
                    
                    TextField("书签描述（可选）", text: $newBookmarkDescription)
                }
                
                Section {
                    Button("添加书签") {
                        addBookmark()
                        showingAddBookmarkSheet = false
                    }
                    .frame(maxWidth: .infinity)
                    .buttonStyle(.borderedProminent)
                }
            }
            .navigationTitle("添加书签")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("取消") {
                        showingAddBookmarkSheet = false
                    }
                }
            }
        }
    }
    
    // MARK: - 辅助方法
    
    // 添加书签
    private func addBookmark() {
        guard let book = coordinator.currentBook else { return }
        
        // 调用 coordinator 的添加书签方法
        Task {
            await coordinator.addBookmarkAtCurrentPage(description: newBookmarkDescription)
            newBookmarkDescription = ""
        }
        
        // 清空描述字段
        // newBookmarkDescription = "" // 已在 Task 中处理
    }
    
    // 删除书签
    private func deleteBookmarks(at indexSet: IndexSet, bookId: String, bookmarks: [Bookmark]) {
        for index in indexSet {
            if index < bookmarks.count {
                coordinator.deleteBookmark(bookmarks[index])
            }
        }
    }
}


// 创建一个独立的预览提供者结构体，避免循环引用
struct BookmarkView_Previews: PreviewProvider {
    static var previews: some View {
        // 使用Group避免控制流语句问题
        Group {
            let schema = Schema([
                Book.self,
                Bookmark.self,
                Item.self, // 添加 Item
                BookSource.self, // 添加 BookSource
                ReadingProgress.self
            ])
            let configuration = ModelConfiguration(schema: schema, isStoredInMemoryOnly: true)
            let container = try! ModelContainer(for: schema, configurations: [configuration])
            let coordinator = ReaderCoordinator(modelContainer: container)
            
            BookmarkView().environmentObject(coordinator)
                .modelContainer(container)
        }
    }
}