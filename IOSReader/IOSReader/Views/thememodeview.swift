// ThemeModeView.swift
// 主题模式设置视图，主要功能包括:
// 1. 系统主题跟随
// 2. 浅色/深色模式切换
// 3. 自动切换时间设置
// 通过ReaderCoordinator协调主题状态管理

import SwiftUI
import SwiftData

struct ThemeModeView: View {
    @EnvironmentObject var coordinator: ReaderCoordinator
    
    var body: some View {
        Text("主题模式功能")
            .navigationTitle("主题模式")
    }
}

// MARK: - 预览

#Preview {
    Group {
        if let container = try? ModelContainer(for: User.self, Book.self, BookSource.self, Bookmark.self, ReadingProgress.self) {
            ThemeModeView()
                .modelContainer(container)
                .environmentObject(ReaderCoordinator(modelContainer: container))
        } else {
            Text("预览错误")
        }
    }
}
