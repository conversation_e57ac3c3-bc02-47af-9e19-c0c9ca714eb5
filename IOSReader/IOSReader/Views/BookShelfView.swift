import SwiftUI
import SwiftData

/// 书架主视图
/// 核心功能:
/// 1. 书籍列表展示 - 以网格布局展示用户已添加的所有书籍
/// 2. 书籍搜索功能 - 提供搜索框可按书名筛选书籍
/// 3. 添加新书籍 - 支持从本地、远程或网址添加新书籍
/// 4. 显示当前时间 - 顶部状态栏显示实时时间
/// 
/// 主要UI组件:
/// - 顶部搜索框
/// - 书籍网格布局(3列自适应)
/// - 空状态提示(当无书籍时)
/// - 添加书籍按钮(固定位置)
/// - 书籍详情卡片(封面+标题)
/// 
/// 主要业务逻辑:
/// - 书籍搜索 - 过滤书籍列表
/// - 书籍添加 - 支持多种来源
/// - 书籍排序 - 按添加时间或阅读进度
/// - 书籍管理 - 编辑、删除书籍
/// - 阅读进度 - 实时更新
/// - 点击书籍封面，打开书籍（历史已阅读，跳转到历史进度，未阅读跳转到第一页）
/// 状态管理:
/// - 使用@State和@ObservedObject管理视图状态
/// - 书籍数据通过@ObservedObject传递给子视图
///
/// 性能优化:
/// - 异步加载书籍封面
/// - 分页加载书籍列表
/// - 缓存已加载的书籍封面
///
/// 注意事项:

// MARK: - 共享工具方法

/// 计算网格项宽度的工具方法
/// - Parameters:
///   - spacing: 项目间距，默认为10
///   - columns: 列数，默认为3
///   - horizontalPadding: 水平边距，默认为5
///   - safeSpacing: 安全间距，默认为10
/// - Returns: 计算得到的网格项宽度
@MainActor
func calculateItemWidth(
    spacing: CGFloat = 10,
    columns: CGFloat = 3,
    horizontalPadding: CGFloat = 5,
    safeSpacing: CGFloat = 10
) -> CGFloat {
    let screenWidth = UIScreen.main.bounds.width
    return (screenWidth - horizontalPadding * 2 - spacing * (columns - 1) - safeSpacing) / columns
}

/// 打开书籍的统一处理方法
/// - Parameters:
///   - book: 要打开的书籍
///   - coordinator: 阅读器协调器


/// 书籍封面视图组件
/// - Parameters:
///   - book: 书籍对象
///   - itemWidth: 封面宽度
/// - Returns: 封面视图
struct BookCoverView: View {
    let book: Book
    let itemWidth: CGFloat
    @Environment(\.colorScheme) private var colorScheme
    
    var body: some View {
        ZStack(alignment: .bottom) {
            AsyncImage(url: book.coverUrl) { phase in // 使用 phase 来处理不同的加载状态
                if let image = phase.image {
                    image // 显示加载的图片
                        .resizable()
                        .aspectRatio(3/4, contentMode: .fill)
                        .frame(width: itemWidth * 0.85, height: itemWidth * 1.2)
                        .clipped()
                        .shadow(color: colorScheme == .dark ? .black.opacity(0.3) : .gray.opacity(0.2), radius: 8, x: 0, y: 4)
                } else if phase.error != nil {
                    // 加载错误时显示默认封面
                    defaultCoverView
                } else {
                    // 加载中或URL为空时显示默认封面
                    defaultCoverView
                }
            }
            
            VStack(spacing: 4) {
                Text(book.title)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.black) // 保持文字颜色为黑色
                    .lineLimit(1)
                    .frame(maxWidth: .infinity)
                    .padding(.horizontal, 8)
                
                Text(book.bookType?.uppercased() ?? "未知")
                    .font(.system(size: 10, weight: .bold))
                    .padding(.horizontal, 6)
                    .padding(.vertical, 3)
                    .background(Color.accentColor)
                    .foregroundColor(.white)
                    .clipShape(Capsule())
            }
            .frame(width: itemWidth * 0.85)
            .padding(.bottom, 8)
        }
        .clipShape(RoundedRectangle(cornerRadius: 12))
        .contentShape(Rectangle())
    }

    // 提取的默认封面视图
    @ViewBuilder
    private var defaultCoverView: some View {
        Group {
            if let bookType = book.bookType?.lowercased() {
                if bookType == "txt" {
                    Image("cover_txt")
                        .resizable()
                        .aspectRatio(3/4, contentMode: .fit)
                } else if bookType == "epub" {
                    Image("cover_epub")
                        .resizable()
                        .aspectRatio(3/4, contentMode: .fit)
                } else if bookType == "pdf" {
                    Image("cover_PDF")
                        .resizable()
                        .aspectRatio(3/4, contentMode: .fit)
                } else if bookType == "html" {
                    Image("cover_HTML")
                        .resizable()
                        .aspectRatio(3/4, contentMode: .fit)
                } else {
                    Image(systemName: "book.closed")
                        .resizable()
                        .aspectRatio(3/4, contentMode: .fit)
                        .padding(.vertical, 10)
                        .foregroundColor(.gray)
                }
            } else {
                Image(systemName: "book.closed")
                    .resizable()
                    .aspectRatio(3/4, contentMode: .fit)
                    .padding(.vertical, 10)
                    .foregroundColor(.gray)
            }
        }
        .frame(width: itemWidth * 0.85, height: itemWidth * 1.2)
        .background(colorScheme == .dark ? Color.gray.opacity(0.15) : Color.gray.opacity(0.1))
        .clipShape(RoundedRectangle(cornerRadius: 12))
        .shadow(color: colorScheme == .dark ? .black.opacity(0.3) : .gray.opacity(0.2), radius: 8, x: 0, y: 4)
    }
}


struct BookShelfView: View {
    @ObservedObject var coordinator: ReaderCoordinator
    @State private var selectedBookID: UUID? = nil // 统一使用 UUID 作为书籍标识符类型
    @Environment(\.colorScheme) private var colorScheme
    
    // 状态管理
    @State private var searchText = ""
    @State private var showAddBookOptions = false
    @State private var isGridLayout = true
    @State private var showFilter = false
    @State private var visibleBooks: [Book] = []
    @State private var showingDeleteAlert = false
    @State private var bookToDelete: Book? = nil
    // Removed onBookSelected
    
    // 共享样式
    private let styles = SharedViewStyles.self
    
    // 使用纯 Swift 图片缓存
    private let imageCache = ImageCache()
    
    // 获取当前时间
    @State private var currentTime = Date()
    let timer = Timer.publish(every: 1, on: .main, in: .common).autoconnect()
    
    // 计算网格项宽度 - 优化布局参数确保3列显示
    private var itemWidth: CGFloat {
        calculateItemWidth()
    }
    
    // MARK: - 性能优化
    @MainActor
    private func loadVisibleBooks() async {
        let filteredBooks = coordinator.books.filter { book in
            searchText.isEmpty || book.title.localizedCaseInsensitiveContains(searchText)
        }
        
        // 分批加载封面
        let batchSize = 10
        for i in stride(from: 0, to: filteredBooks.count, by: batchSize) {
            let end = min(i + batchSize, filteredBooks.count)
            let batch = Array(filteredBooks[i..<end])
            
            await MainActor.run {
                self.visibleBooks.append(contentsOf: batch)
            }
            
            // 预加载封面
            await withTaskGroup(of: Void.self) { group in
                for book in batch {
                    let bookId = book.id
                    let bookFilePath = book.filePath
                    let bookCoverImageData = book.coverImageData
                    group.addTask { @MainActor @Sendable in
                        await self.loadBookCoverById(bookId: bookId, filePath: bookFilePath, coverImageData: bookCoverImageData)
                    }
                }
            }
        }
    }
    
    @MainActor
    private func loadBookCover(for book: Book) async {
        guard let filePath = book.filePath else { return }
        guard let coverImageData = book.coverImageData else { return }
        await loadBookCoverById(bookId: book.id, filePath: filePath, coverImageData: coverImageData)
    }
    
    @MainActor
    private func loadBookCoverById(bookId: UUID, filePath: String?, coverImageData: Data?) async {
        guard let filePath = filePath else { return }
        guard let coverImageData = coverImageData else { return }
        
        let cacheKey = filePath
        if await imageCache.image(forKey: cacheKey) == nil {
            #if canImport(UIKit)
            if let uiImage = UIImage(data: coverImageData) {
                await imageCache.setImage(Image(uiImage: uiImage), forKey: cacheKey)
            }
            #elseif canImport(AppKit)
            if let nsImage = NSImage(data: coverImageData) {
                await imageCache.setImage(Image(nsImage: nsImage), forKey: cacheKey)
            }
            #endif
        }
    }
    
    // MARK: - 初始化
    init(coordinator: ReaderCoordinator) { // Removed onBookSelected parameter
        self.coordinator = coordinator
        // ImageCache has internal maxCacheSize, no need to set countLimit
        // imageCache.countLimit = 100 // Remove this line
    }
    
    // MARK: - 视图构建
    var body: some View {
        NavigationStack {
            VStack(spacing: 0) {
                // 导航栏
                styles.NavigationBarStyle.container(
                    HStack {
                        styles.TextStyle.title("书架")
                            .padding(.leading)
                        Spacer()
                        HStack(spacing: 16) {
                            Button(action: { showFilter.toggle() }) {
                                styles.IconStyle.icon("line.3.horizontal.decrease.circle")
                            }
                            Button(action: { isGridLayout.toggle() }) {
                                styles.IconStyle.icon(isGridLayout ? "square.grid.2x2" : "list.bullet")
                            }
                            Button(action: { showAddBookOptions.toggle() }) {
                                styles.IconStyle.icon("plus.circle.fill")
                                    .foregroundColor(.accentColor)
                            }
                        }
                        .padding(.trailing)
                    }
                )
                    .padding(.bottom, 8)
                    .background(Color.clear)
                // Removed explicit background modifier
                styles.SearchBarStyle.searchBar($searchText)
                    .padding(.horizontal)
                    .padding(.bottom, 8)
                if coordinator.books.isEmpty {
                    VStack(spacing: 20) {
                        styles.IconStyle.icon("books.vertical")
                            .font(.system(size: 60))
                            .foregroundColor(.gray)
                        styles.TextStyle.title("书架空空如也")
                            .foregroundColor(.gray)
                        Button(action: { showAddBookOptions = true }) {
                            Text("添加书籍")
                                .font(.headline)
                                .foregroundColor(.white)
                                .padding(.horizontal, 20)
                                .padding(.vertical, 10)
                                .background(Color.accentColor)
                                .clipShape(RoundedRectangle(cornerRadius: 10))
                        }
                    }
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                } else {
                    if isGridLayout {
                        BooksGridView(coordinator: coordinator,
                                    showAddBookOptions: $showAddBookOptions,
                                    onBookSelected: { bookID in // Closure now provides ID
                            if let book = coordinator.books.first(where: { $0.id == bookID }) {
                                coordinator.selectedBook.send(book) // Send the selected book
                            }
                            self.selectedBookID = bookID // 确保更新 selectedBookID 以触发导航
                        })
                            .onChange(of: coordinator.showAlert) { _, newValue in
                                if newValue {
                                    print("显示错误提示: \(coordinator.alertTitle) - \(coordinator.alertMessage)")
                                }
                            }
                    } else {
                        BooksListView(coordinator: coordinator, onBookSelected: { bookID in // Closure now provides ID
                            if let book = coordinator.books.first(where: { $0.id == bookID }) {
                                coordinator.selectedBook.send(book) // Send the selected book
                            }
                            self.selectedBookID = bookID // 确保更新 selectedBookID 以触发导航
                        })
                            .onChange(of: coordinator.showAlert) { _, newValue in
                                if newValue {
                                    print("显示错误提示: \(coordinator.alertTitle) - \(coordinator.alertMessage)")
                                }
                            }
                    }
                }
            }
            .navigationBarHidden(true)
            .sheet(isPresented: $showAddBookOptions) {
                AddBookView().environmentObject(coordinator)
            }
            .sheet(isPresented: $showFilter) {
                FilterView(coordinator: coordinator)
            }
            .alert(coordinator.alertTitle, isPresented: $coordinator.showAlert) {
                Button("确定", role: .cancel) {}
            } message: {
                Text(coordinator.alertMessage)
            }
        }
        // Moved navigationDestination outside VStack, attached to NavigationStack
        // Use .navigationDestination(item:) for optional ID
        .navigationDestination(item: $selectedBookID) { bookID in
            // Find the book using the provided bookID
            if let bookToRead = coordinator.books.first(where: { $0.id == bookID }) {
                 // The ReaderView should handle loading the book itself based on the coordinator state
                 // We just need to ensure the coordinator knows which book is selected.
                 // The coordinator's startReading should ideally be called *before* navigation,
                 // perhaps triggered by the change in selectedBookID if needed,
                 // or handled within ReaderView's onAppear/task.
                 // For now, let's assume ReaderView handles it.
                 ReaderView(coordinator: coordinator, showToolbars: .constant(true)) // Pass the coordinator
                    .ignoresSafeArea()
                     .onAppear {
                         // 确保在视图出现时，如果 coordinator 的当前书籍与期望的书籍不一致，则尝试加载
                         // 这一步是为了确保即使 selectedBook.send 发生得稍晚或 ReaderView 初始化时状态未完全同步，也能正确加载
                         if coordinator.readerViewModel.currentBook?.id != bookToRead.id {
                             Task {
                                 if let path = bookToRead.filePath {
                                     do {
                                         print("ReaderView onAppear: Attempting to load book: \(bookToRead.title) from path: \(path)")
                                         _ = try await coordinator.startReading(from: path)
                                         print("ReaderView onAppear: Successfully started reading \(bookToRead.title)")
                                     } catch {
                                         print("Error starting reading in ReaderView onAppear: \(error)")
                                         // 可以在这里更新 coordinator 的状态以显示错误信息
                                         await MainActor.run {
                                             coordinator.alertTitle = "加载失败"
                                             coordinator.alertMessage = "加载书籍时发生错误: \(error.localizedDescription)"
                                             coordinator.showAlert = true
                                         }
                                         //coordinator.showAlert(title: "加载失败", message: "无法加载书籍内容：\(error.localizedDescription)")
                                     }
                                 } else {
                                     print("ReaderView onAppear: Book path is nil for \(bookToRead.title)")
                                     //coordinator.showAlert(title: "加载失败", message: "书籍文件路径无效。")
                                     await MainActor.run {
                                         coordinator.alertTitle = "加载失败"
                                         coordinator.alertMessage = "书籍文件路径无效。"
                                         coordinator.showAlert = true
                                     }
                                 }
                             }
                         } else {
                             print("ReaderView onAppear: Current book in coordinator already matches \(bookToRead.title). No need to reload.")
                         }
                     }
                } else {
                    // 处理找不到书籍的情况，例如显示错误视图或返回
                    Text("无法加载书籍")
                }
            }
        }
        // Removed onChange(of: selectedBook) as navigation is now driven by selectedBookID and .navigationDestination(item:)
    }

// 核心功能栏
private struct CoreFunctionBar: View {
    @Binding var showAddBookOptions: Bool
    @Binding var showFilter: Bool
    @Binding var isGridLayout: Bool
    
    var body: some View {
        HStack {
            Text("书名/作者/网址")
                .font(.headline)
            
            Spacer()
            
            HStack(spacing: 16) {
                Button(action: { showFilter.toggle() }) {
                    Image(systemName: "line.3.horizontal.decrease.circle")
                }
                
                Button(action: { isGridLayout.toggle() }) {
                    Image(systemName: isGridLayout ? "square.grid.2x2" : "list.bullet")
                }
                
                Button(action: { showAddBookOptions.toggle() }) {
                    Image(systemName: "plus.circle.fill")
                        .foregroundColor(.blue)
                }
            }
            .font(.system(size: 20))
        }
        .padding(.horizontal)
        .padding(.vertical, 8)
    }
}

/// 列表视图组件
private struct BooksListView: View {
    @ObservedObject var coordinator: ReaderCoordinator
    var onBookSelected: ((UUID) -> Void)? = nil // 统一使用 UUID
    
    init(coordinator: ReaderCoordinator, onBookSelected: ((UUID) -> Void)? = nil) { // 统一使用 UUID
        self.coordinator = coordinator
        self.onBookSelected = onBookSelected
    }
    
    var body: some View {
        List {
            ForEach(coordinator.books) { book in
                BookListCell(book: book, coordinator: coordinator)
                    .onTapGesture {
                        if let onBookSelected = onBookSelected {
                            // 使用回调通知ContentView切换到阅读视图
                            onBookSelected(book.id) // Pass ID
                        }
                        // Removed the else block and openBook call
                    }
            }
        }
    }
}

// 添加BookListCell组件定义
private struct BookListCell: View {
    let book: Book
    @ObservedObject var coordinator: ReaderCoordinator
    
    // 计算网格项宽度
    private var itemWidth: CGFloat {
        let screenWidth = UIScreen.main.bounds.width
        let spacing: CGFloat = 30 // 项目间距
        let columns: CGFloat = 3 // 3列布局
        let horizontalPadding: CGFloat = 5 // 水平边距
        let safespacing: CGFloat = 40 // 安全间距
        return (screenWidth - horizontalPadding*2-spacing * (columns - 1)-safespacing) / columns
    }
    
    var body: some View {
        HStack {
            bookCoverImage(for: book)
                .frame(width: 50, height: 70)
                .cornerRadius(4)
            
            VStack(alignment: .leading, spacing: 4) {
                ScrollView(.horizontal, showsIndicators: false) {
                    Text(book.title)
                        .font(.headline)
                        .lineLimit(1)
                }
                Text(book.author)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                // 修正：直接访问 coordinator 的属性/方法
                if let progress = coordinator.getReadingProgress(bookID: book.id.uuidString) {
                    // 修正：直接访问 coordinator 的属性/方法
                    // 假设 getTotalPages 方法存在且返回 Int 或 String
                    Text("\(progress.pageNumber)/\(coordinator.getTotalPages(bookID: book.id.uuidString))页")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            Spacer()
            
            if let lastReadDate = book.lastReadDate {
                Text(dateFormatter.string(from: lastReadDate))
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            if let lastReadDate = book.lastReadDate {
                Text(dateFormatter.string(from: lastReadDate))
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .padding(.vertical, 4)
    }
    
    private var dateFormatter: DateFormatter {
        let formatter = DateFormatter()
        formatter.dateStyle = .short
        return formatter
    }
    
    private func bookCoverImage(for book: Book) -> some View {
        BookCoverView(book: book, itemWidth: itemWidth)
    }
}

// 筛选视图
private struct FilterView: View {
    @ObservedObject var coordinator: ReaderCoordinator
    @Environment(\.presentationMode) var presentationMode
    
    // 图片缓存
    // 使用纯 Swift 图片缓存
    private let imageCache = ImageCache()
    
    // MARK: - 初始化
    init(coordinator: ReaderCoordinator) {
        self.coordinator = coordinator
        // ImageCache 在其实现内部管理大小限制，无需在此处配置 countLimit
    }
    
    // MARK: - 视图构建
    var body: some View {
        NavigationView {
            List {
                Section(header: Text("文件类型")) {
                    Text("全部")
                    Text("TXT")
                    Text("EPUB")
                    // Text("MOBI") // MOBI 已移除
                    Text("HTML")
                }
                
                Section(header: Text("阅读状态")) {
                    Text("全部")
                    Text("未读")
                    Text("在读")
                    Text("已读")
                }
                
                Section(header: Text("更新时间")) {
                    Text("全部")
                    Text("24小时内")
                    Text("本周")
                }
            }
            .listStyle(GroupedListStyle())
            .navigationTitle("筛选")
            .navigationBarItems(trailing: Button("完成") {
                presentationMode.wrappedValue.dismiss()
            })
        }
    }
}

private struct SearchTextField: View {
    @Binding var searchText: String
    
    var body: some View {
        TextField("全部", text: $searchText)
            .textFieldStyle(RoundedBorderTextFieldStyle())
    }
}

private struct EmptyBookshelfView: View {
    @Binding var showAddBookOptions: Bool
    
    var body: some View {
        VStack(spacing: 20) {
            Spacer()
            Image(systemName: "books.vertical")
                .font(.system(size: 60))
                .foregroundColor(.gray)
            Text("书架空空如也")
                .font(.title2)
                .foregroundColor(.gray)
            Button(action: { showAddBookOptions = true }) {
                Text("添加书籍")
                    .font(.headline)
                    .foregroundColor(.white)
                    .padding(.horizontal, 30)
                    .padding(.vertical, 12)
                    .background(Color.blue)
                    .cornerRadius(8)
            }
            Spacer()
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color(.systemBackground))
    }
}

private struct BooksGridView: View {
    @ObservedObject var coordinator: ReaderCoordinator
    @Binding var showAddBookOptions: Bool
    @State private var bookToDelete: Book? = nil
    @State private var showingDeleteAlert = false
    var onBookSelected: ((UUID) -> Void)? = nil // 统一使用 UUID
    @Environment(\.colorScheme) private var colorScheme
    private let styles = SharedViewStyles.self
    
    init(coordinator: ReaderCoordinator, showAddBookOptions: Binding<Bool>, onBookSelected: ((UUID) -> Void)? = nil) { // 统一使用 UUID
        self.coordinator = coordinator
        self._showAddBookOptions = showAddBookOptions
        self.onBookSelected = onBookSelected
    }
    
    // 计算网格项宽度
    private var itemWidth: CGFloat {
        let screenWidth = UIScreen.main.bounds.width
        let spacing: CGFloat = 10 // 减小项目间距
        let columns: CGFloat = 3 // 3列布局
        let horizontalPadding: CGFloat = 5 // 减小水平边距
        let safespacing:CGFloat = 10 // 减小安全间距
        return (screenWidth - horizontalPadding * 2 - spacing * (columns - 1) - safespacing) / columns
    }

    // 明确定义为3列固定宽度的网格
    private var gridColumns: [GridItem] {
        [GridItem(.fixed(itemWidth), spacing: 8),
         GridItem(.fixed(itemWidth), spacing: 8),
         GridItem(.fixed(itemWidth), spacing: 8)]
    }

    var body: some View {
        ScrollView {
            LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: styles.ListStyle.itemSpacing), count: 3), spacing: styles.ListStyle.itemSpacing * 2) {
                ForEach(coordinator.books, id: \.id) { book in
                    BookGridItem(book: book, itemWidth: calculateItemWidth(), coordinator: coordinator, onBookSelected: onBookSelected)
                        .background(styles.ListStyle.itemBackground(colorScheme: colorScheme))
                        .padding(styles.ListStyle.itemPadding)
                        .contextMenu {
                            Button(role: .destructive) {
                                bookToDelete = book
                                showingDeleteAlert = true
                            } label: {
                                Label("删除", systemImage: "trash")
                                    .foregroundColor(.red)
                            }
                        }
                }
                .alert("确认删除", isPresented: $showingDeleteAlert, presenting: bookToDelete) { book in
                    Button("删除", role: .destructive) {
                        if let index = coordinator.books.firstIndex(where: { $0.id == book.id }) {
                            coordinator.books.remove(at: index)
                        }
                    }
                    Button("取消", role: .cancel) {}
                } message: { book in
                    Text("确定要删除《\(book.title)》吗？此操作无法撤销。")
                }
                
                AddBookButton(action: { showAddBookOptions = true })
                    .frame(width: itemWidth*0.8, height: itemWidth * 1.067)
                    .fixedSize(horizontal: true, vertical: true) // 确保尺寸固定
            }
            .padding(.horizontal, 4)
            .padding(.vertical, 4)
        }
    }
}

private struct BookGridItem: View {
    let book: Book
    let itemWidth: CGFloat
    @ObservedObject var coordinator: ReaderCoordinator
    @State private var showReader = false
    var onBookSelected: ((UUID) -> Void)? = nil // 统一使用 UUID
    
    var body: some View {
        Button {
            print("=== 书籍封面点击事件触发 ===")
            print("书籍ID: \(book.id.uuidString)")
            print("书名: \(book.title)")
            
            if book.filePath == nil {
                print("❌ 文件路径不存在 | 书籍ID: \(book.id.uuidString)")
                coordinator.alertTitle = "文件不存在"
                coordinator.alertMessage = "无法找到书籍文件，可能已被删除或移动"
                coordinator.showAlert = true
                return
            }
            
            print("✅ 有效文件路径: \(book.filePath!)")
            // 获取阅读进度，如果存在则使用，否则默认为0
            // 修正：直接访问 coordinator 的属性/方法
            let progress = coordinator.getReadingProgress(bookID: book.id.uuidString)
            _ = progress?.pageNumber ?? 0 // Extract page number, default to 0
            _ = progress?.chapterIndex ?? 0 // Extract chapter index, default to 0
            // Ensure updateCurrentPage is called correctly if needed here, or remove if redundant
            // coordinator.updateCurrentPage(bookId: book.id.uuidString, chapterIndex: chapterIndex, pageNumber: pageNumber) // This might be called later in the Task
            
            // 如果有回调函数，直接调用回调函数通知ContentView切换到阅读视图
            if let onBookSelected = onBookSelected {
                onBookSelected(book.id) // Pass ID
                return
            }
            
            // 否则使用原来的方法打开书籍
            Task {
                print("⌛ 开始异步加载书籍")
                do {
                    // The startReading call seems correct based on definition.
                    // Assuming the previous errors were spurious or related to other issues.
                    // Use book.filePath! as filePath is not defined in this scope and book.filePath was checked for nil earlier.
                    let success = try await coordinator.startReading(from: book.filePath!)
                    if success {
                        await MainActor.run {
                            coordinator.objectWillChange.send()
                            // 使用 onBookSelected 回调触发导航，而不是设置 showReader
                            if let onBookSelected = onBookSelected {
                                onBookSelected(book.id)
                            } else {
                                // 如果没有提供回调，则通过 coordinator 发送选中的书籍
                                coordinator.selectedBook.send(book)
                            }
                        }
                    } else {
                        print("⛔ 阅读启动失败 | 原因: 文件损坏/格式不支持")
                        await MainActor.run {
                            coordinator.alertTitle = "加载失败"
                            coordinator.alertMessage = "无法加载书籍，文件可能已损坏或格式不支持"
                            coordinator.showAlert = true
                        }
                    }
                } catch {
                    print("⛔ 阅读启动失败 | 错误: \(error.localizedDescription)")
                    await MainActor.run {
                        coordinator.alertTitle = "加载失败"
                        coordinator.alertMessage = "加载书籍时发生错误: \(error.localizedDescription)"
                        coordinator.showAlert = true
                    }
                }
            }
        } label: {
            BookCoverView(book: book, itemWidth: itemWidth)
                .font(.caption) //应用在 Text 上
                .frame(width: itemWidth*0.8, height: itemWidth * 1.067)
                .fixedSize(horizontal: true, vertical: true) // 确保宽度和高度都固定
        }
        .onAppear {
            // 检查并加载阅读历史记录
            // Removed the .onAppear logic that incorrectly tried to call updateCurrentPage.
            // Reading progress should be loaded when the book is opened, not necessarily on shelf view appear.
            // If needed, progress loading logic belongs in the openBook function or within the ReaderView itself.
        }
        .onDisappear {
            // Saving progress on disappear might be too frequent or unnecessary here.
            // Progress is typically saved when exiting the reader view or periodically during reading.
            // Consider moving save logic to ReaderCoordinator or UnifiedReaderView's onDisappear.
            // Task {
            //     if let currentProgress = coordinator.getReadingProgress(bookID: book.id.uuidString) {
            //         coordinator.saveReadingProgress(bookID: book.id.uuidString, chapterIndex: currentProgress.chapterIndex, pageNumber: coordinator.currentPage)
            //     }
            // }
            // Moving book to first position logic would go here if implemented.
        }
        .onDisappear {
            Task.init(priority: nil) {
                // 保存阅读进度 - 明确指定参数以解决方法歧义
                coordinator.readingProgress[book.id.uuidString] = (chapterIndex: 0, pageNumber: coordinator.currentPage)
                // 将书籍移动到书架第一位（如果需要实现此功能，请添加moveBookToFirstPosition方法）
                // 移除对不存在的wrappedValue属性的引用
                if let index = coordinator.books.firstIndex(where: { $0.id == book.id }) {
                    let movedBook = coordinator.books.remove(at: index)
                    coordinator.books.insert(movedBook, at: 0)
                }
            }
        }
        .buttonStyle(PlainButtonStyle())
        .onLongPressGesture {
            // 显示删除确认对话框
            coordinator.alertTitle = "删除确认"
            coordinator.alertMessage = "确定要删除《\(book.title)》吗？"
            coordinator.showAlert = true
            
            // 删除确认后的操作
            if let index = coordinator.books.firstIndex(where: { $0.id == book.id }) {
                coordinator.books.remove(at: index)
                // 删除相关的文件和记录
                if book.filePath != nil {
                    try? FileManager.default.removeItem(atPath: #filePath)
                }
                // 清除阅读进度
                UserDefaults.standard.removeObject(forKey: "readingProgress_" + book.id.uuidString)
            }
        }
        .contextMenu {
            Button("删除", role: .destructive) {
                // 显示删除确认对话框
                coordinator.alertTitle = "删除确认"
                coordinator.alertMessage = "确定要删除《\(book.title)》吗？"
                coordinator.showAlert = true
                
                // 删除确认后的操作
                if let index = coordinator.books.firstIndex(where: { $0.id == book.id }) {
                    coordinator.books.remove(at: index)
                    // 删除相关的文件和记录
                    if book.filePath != nil {
                        try? FileManager.default.removeItem(atPath: #filePath)
                    }
                    // 清除阅读进度
                    UserDefaults.standard.removeObject(forKey: "readingProgress_" + book.id.uuidString)
                    // 通知视图更新
                    coordinator.objectWillChange.send()
                }
            }
        }
        // 移除了 navigationDestination 修饰符，使用 NavigationStack 中的 navigationDestination(item:) 代替
    }
}

private struct BookCoverDetailView: View {
    let book: Book
    let itemWidth: CGFloat
    
    var body: some View {
        VStack(spacing: 2) {
            bookCoverImage
            
            Text(book.title)
                .font(.system(size: 12))
                .lineLimit(1)
                .truncationMode(.tail)
                .frame(width: itemWidth)
        }
    }
    
    private var bookCoverImage: some View {
        ZStack(alignment: .bottomTrailing) {
            // book.coverImage is already a SwiftUI Image
            if let coverImage = book.coverImage {
                coverImage
                    .resizable()
                    .aspectRatio(3/4, contentMode: .fill)
                    .frame(width: itemWidth * 0.8, height: itemWidth * 1.067) // 缩小封面尺寸
                    .clipShape(RoundedRectangle(cornerRadius: 8))
                    .shadow(color: .gray.opacity(0.4), radius: 2, x: 0, y: 2)
            } else {
                // 根据文件类型显示默认封面
                if let bookType = book.bookType?.lowercased() {
                    if bookType == "txt" {
                        // 使用Image("cover_txt")加载图片资源
                        Image("cover_txt")
                            .resizable()
                            .aspectRatio(3/4, contentMode: .fit)
                            .frame(width: itemWidth * 0.8, height: itemWidth * 1.067) // 缩小封面尺寸
                            .clipShape(RoundedRectangle(cornerRadius: 8))
                            .shadow(color: .gray.opacity(0.3), radius: 2, x: 0, y: 2)
                    } else if bookType == "epub" {
                        // 使用Image("cover_epub")加载图片资源
                        Image("cover_epub")
                            .resizable()
                            .aspectRatio(3/4, contentMode: .fit)
                            .frame(width: itemWidth * 0.6, height: itemWidth * 0.8) // 缩小封面尺寸
                            .clipShape(RoundedRectangle(cornerRadius: 8))
                            .shadow(color: .gray.opacity(0.4), radius: 2, x: 0, y: 2)
                    } else if bookType == "pdf" {
                        // 使用Image("cover_PDF")加载图片资源
                        Image("cover_PDF")
                            .resizable()
                            .aspectRatio(3/4, contentMode: .fit)
                            .frame(width: itemWidth * 0.6, height: itemWidth * 0.8) // 缩小封面尺寸
                            .clipShape(RoundedRectangle(cornerRadius: 8))
                            .shadow(color: .gray.opacity(0.4), radius: 2, x: 0, y: 2)
                    } else if bookType == "html" {
                        // 使用Image("cover_HTML")加载图片资源
                        Image("cover_HTML")
                            .resizable()
                            .aspectRatio(3/4, contentMode: .fit)
                            .frame(width: itemWidth * 0.6, height: itemWidth * 0.8) // 缩小封面尺寸
                            .clipShape(RoundedRectangle(cornerRadius: 8))
                            .shadow(color: .gray.opacity(0.4), radius: 2, x: 0, y: 2)
                    } else {
                        // 其他类型的书籍使用通用封面
                        Image(systemName: "book.closed")
                            .resizable()
                            .aspectRatio(contentMode: .fit)
                            .frame(width: itemWidth * 0.6, height: itemWidth * 0.8)
                            .padding(.vertical, 10)
                            .frame(width: itemWidth * 0.8, height: itemWidth * 1.067) // 缩小封面尺寸
                            .background(Color.gray.opacity(0.2))
                            .clipShape(RoundedRectangle(cornerRadius: 8))
                            .shadow(color: .gray.opacity(0.4), radius: 2, x: 0, y: 2)
                            .foregroundColor(.gray)
                    }
                } else {
                    // 当bookType为nil时显示默认封面
                    Image(systemName: "book.closed")
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .frame(width: itemWidth * 0.6, height: itemWidth * 0.8)
                        .padding(.vertical, 10)
                        .frame(width: itemWidth * 0.8, height: itemWidth * 1.067) // 缩小封面尺寸
                        .background(Color.gray.opacity(0.2))
                        .clipShape(RoundedRectangle(cornerRadius: 8))
                        .shadow(color: .gray.opacity(0.4), radius: 2, x: 0, y: 2)
                        .foregroundColor(.gray)
                }
            }
            
            if let bookType = book.bookType {
                Text(bookType.uppercased())
                    .font(.system(size: 8, weight: .bold))
                    .padding(3)
                    .background(Color.black.opacity(0.7))
                    .foregroundColor(.red)
                    .cornerRadius(3)
                    .padding(3)
            }
        }
    }
}

private struct AddBookButton: View {
    let action: () -> Void
    
    // 获取与其他组件一致的宽度
    private var itemWidth: CGFloat {
        let screenWidth = UIScreen.main.bounds.width
        let spacing: CGFloat = 30 // 进一步减小项目间距
        let columns: CGFloat = 3 // 3列布局
        let horizontalPadding: CGFloat = 5 // 减小水平边距
        let safespacing:CGFloat = 40
        return (screenWidth - horizontalPadding * 2 - spacing * (columns - 1)-safespacing) / columns
    }
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 2) {
                Image(systemName: "plus")
                    .font(.system(size: 20))
                    .frame(width: itemWidth * 0.8, height: itemWidth * 1.067)
                    .background(Color.gray.opacity(0.2))
                    .cornerRadius(6)
                
                Text("添加")
                    .font(.system(size: 10))
                    .frame(width: itemWidth)
            }
        }
    }
}

// MARK: - 视图修饰符
private extension Image {
    func bookCoverStyle() -> some View {
        self
            .resizable()
            .aspectRatio(contentMode: .fit)
            .frame(width: 60, height: 80)
            .background(Color.gray.opacity(0.2))
            .cornerRadius(8)
    }
    
    func addIconStyle() -> some View {
        self
            .font(.system(size: 24))
            .frame(width: 60, height: 80)
            .background(Color.gray.opacity(0.2))
            .cornerRadius(8)
    }
}

private extension Text {
    func bookTitleStyle() -> some View {
        self
            .font(.caption)
            .lineLimit(1)
    }
    
    func addButtonTextStyle() -> some View {
        self
            .font(.caption)
    }
}
#Preview {
    // 简化预览结构
    Text("书架预览")
}
