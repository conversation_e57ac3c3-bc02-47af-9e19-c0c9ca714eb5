import SwiftUI
import PDFKit

/// 显示PDF文档单页的SwiftUI视图
struct PDFKitPageView: UIViewRepresentable {
    let document: PDFDocument
    let pageNumber: Int

    func makeUIView(context: Context) -> PDFView {
        let pdfView = PDFView()
        pdfView.displayMode = .singlePage
        pdfView.displaysPageBreaks = false
        pdfView.displayDirection = .horizontal // 匹配TabView滑动方向
        pdfView.autoScales = true
        pdfView.backgroundColor = .clear // 允许UnifiedReaderView设置背景色
        return pdfView
    }

    func updateUIView(_ pdfView: PDFView, context: Context) {
        // 确保文档已设置
        pdfView.document = document
        
        // 跳转到指定页面，检查边界
        if let page = document.page(at: pageNumber) {
            // 检查当前页面是否已是目标页面以避免不必要的更新
            if pdfView.currentPage != page {
                pdfView.go(to: page)
                // 可选：导航后根据需要调整缩放比例
                // pdfView.scaleFactor = pdfView.scaleFactorForSizeToFit
            }
        } else {
            // 必要时处理无效页码
            print("Warning: Attempted to navigate to invalid page number \(pageNumber) in PDFKitPageView.")
        }
        
        // 确保背景透明以支持主题更改
        pdfView.backgroundColor = .clear
    }
}