import SwiftUI
import SwiftData
#if canImport(UIKit)
import UIKit
#endif

// MARK: - 主视图
struct BookStoreView: View {
    // @StateObject private var bookListVM: BookListViewModel // TODO: 实现BookListViewModel
    @State private var showingSourceManager = false
    // 将 coordinator 声明为 @ObservedObject 或 @EnvironmentObject
    // 如果 ReaderCoordinator 是 ObservableObject 并且在父视图中通过 .environmentObject() 提供
    // 则可以使用 @EnvironmentObject var coordinator: ReaderCoordinator
    // 否则，如果只是传递，可以使用 @ObservedObject var coordinator: ReaderCoordinator
    // 这里我们假设 ReaderCoordinator 是 ObservableObject
    @ObservedObject var coordinator: ReaderCoordinator
    @State private var selectedCategoryForView: String? = "重磅推荐" // 默认选中第一个分类，与截图一致
    
    init(coordinator: ReaderCoordinator) {
        self.coordinator = coordinator
        // TODO: BookListViewModel 初始化时需要 sourceManager，确保 coordinator.sourceManager 可用
        // 移除了 initialCategory 参数，因为 BookSource 没有 categories 成员
        // _bookListVM = StateObject(wrappedValue: BookListViewModel(sourceManager: coordinator.sourceManager))
        // 初始化时，如果 bookListVM 的 selectedCategory 为 nil，则设置为默认值
        // 这确保了 ViewModel 和 View 的初始状态一致
        // _bookListVM.wrappedValue.selectedCategory = _bookListVM.wrappedValue.selectedCategory ?? "重磅推荐"
    }
    
    // 在 body 中或者 onAppear 中设置 bookListVM.selectedCategory
    // 以确保 ViewModel 知道初始选中的分类
    // 这个操作最好在 ViewModel 初始化时或者 View onAppear 时进行一次
    // .onAppear {
    //     if bookListVM.selectedCategory == nil {
    //         bookListVM.selectedCategory = selectedCategoryForView
    //     }
    // }

    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // 实现新的搜索栏
                NewSearchBar()
                
                // 实现新的分类选择器
                NewCategoryPickerView()
                
                // 书籍列表
                BookListContentView()
            }
            .sheet(isPresented: $showingSourceManager) {
                // TODO: 实现书源管理视图
                Text("书源管理")
                    .navigationTitle("书源管理")
            }
            .navigationBarTitleDisplayMode(.inline) //保持inline以自定义标题视图
            .toolbar {
                ToolbarItem(placement: .principal) { // 使用 ToolbarItem 来居中标题
                    Text(coordinator.currentBookSource?.name ?? "书城") // 显示当前书源名称或默认“书城”
                        .font(.headline)
                }
                ToolbarItem(placement: .navigationBarTrailing) { // 右侧按钮用于切换书源
                    Button {
                        showingSourceManager.toggle()
                    } label: {
                        Image(systemName: "list.bullet") // 示例图标，可以根据截图选择更合适的
                    }
                }
                //保留刷新按钮，或根据新设计决定是否需要
                ToolbarItem(placement: .navigationBarLeading) { // 左侧按钮可以用于刷新或移除
                     Button {
                        // TODO: 实现刷新功能
                        // bookListVM.refresh()
                    } label: {
                        Image(systemName: "arrow.clockwise")
                    }
                }
            }
        }
    }
}

// MARK: - 子组件扩展
extension BookStoreView {
    // 新的搜索栏组件
    @ViewBuilder
    func NewSearchBar() -> some View {
        HStack {
            Image(systemName: "magnifyingglass")
            // TODO: 实现搜索功能
            TextField("搜索书名或作者", text: .constant(""))
                .textFieldStyle(.plain)
                .autocorrectionDisabled()
            // if !bookListVM.searchText.isEmpty {
            //     Button {
            //         bookListVM.searchText = ""
            //     } label: {
            //         Image(systemName: "xmark.circle.fill")
            //             .foregroundColor(.gray)
            //     }
            // }
        }
        .padding(EdgeInsets(top: 8, leading: 12, bottom: 8, trailing: 12))
        .background(Color.gray.opacity(0.1))
        .cornerRadius(10)
        .padding(.horizontal)
        .padding(.top, 10) // 与截图顶部间距类似
    }

    // 新的分类选择器组件
    @ViewBuilder
    func NewCategoryPickerView() -> some View {
        // 这些分类可以从 ViewModel 获取，或者像现在这样硬编码
        let categories = ["重磅推荐", "男生必读", "女生爱看", "小编推荐"] // 根据截图调整
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 15) {
                ForEach(categories, id: \.self) { category in
                    Button(action: {
                        selectedCategoryForView = category // 更新视图的选中状态
                        // bookListVM.selectedCategory = category // TODO: 更新 ViewModel 的选中状态，触发书籍加载
                        print("选择了分类: \(category)")
                    }) {
                        Text(category)
                            .font(selectedCategoryForView == category ? .headline : .subheadline) // 选中时加粗
                            .padding(.vertical, 8)
                            .padding(.horizontal, 12)
                            .background(selectedCategoryForView == category ? Color.gray.opacity(0.2) : Color.clear)
                            .cornerRadius(8)
                            .foregroundColor(selectedCategoryForView == category ? .accentColor : .primary)
                    }
                }
            }
            .padding(.horizontal)
        }
        .padding(.top, 5)
        .padding(.bottom, 10) // 增加底部间距
        .onAppear {
            // TODO: 确保 ViewModel 的 selectedCategory 与视图的初始状态一致
            // if bookListVM.selectedCategory == nil && selectedCategoryForView != nil {
            //     bookListVM.selectedCategory = selectedCategoryForView
            // }
        }
    }

    // 书籍列表内容组件
    @ViewBuilder
    func BookListContentView() -> some View {
        List {
            // TODO: 实现书籍列表
            Text("书籍列表待实现")
            // ForEach(bookListVM.books) { book in
            //     Button(action: {
            //         coordinator.showBookDetail(book: book)
            //     }) {
            //         BookRow(book: book)
            //             .environmentObject(bookListVM)
            //             .onAppear {
            //                 if book == bookListVM.books.last {
            //                     bookListVM.loadMore()
            //                 }
            //             }
            //     }
            //     .buttonStyle(PlainButtonStyle())
            // }

            // TODO: 实现加载状态
            // if bookListVM.isLoading {
            //     ProgressView()
            //         .frame(maxWidth: .infinity)
            //         .padding()
            // }
        }
        .listStyle(.plain)
        // .refreshable { bookListVM.refresh() } // TODO: 实现刷新功能
    }

}


// 书籍行视图
struct BookRow: View {
    let book: Book // 书籍数据由外部传入
    
    // @EnvironmentObject var bookListVM: BookListViewModel // TODO: 从环境中获取 ViewModel

    var body: some View {
        HStack(spacing: 12) {
            // 书籍封面
            AsyncImage(url: book.coverUrl) { image in
                image.resizable()
                    .aspectRatio(contentMode: .fill)
            } placeholder: {
                Image(systemName: "book.closed.fill") // 使用占位符图标
                    .resizable()
                    .scaledToFit()
                    .frame(width: 40, height: 40)
                    .foregroundColor(.gray)
                    .frame(width: 60, height: 80)
                    .background(Color.gray.opacity(0.1))
            }
            .frame(width: 60, height: 80)
            .cornerRadius(6)
            
            // 书籍信息
            VStack(alignment: .leading, spacing: 4) {
                Text(book.title)
                    .font(.system(size: 16, weight: .bold)) // 标题加粗
                    .lineLimit(1)
                
                Text("作者: \(book.author)")
                    .font(.system(size: 13))
                    .foregroundColor(.secondary)
                    .lineLimit(1)
                
                if let intro = book.introduction, !intro.isEmpty {
                    Text(intro)
                        .font(.system(size: 12))
                        .foregroundColor(.gray)
                        .lineLimit(2) // 简介显示两行
                }
                
                if let tags = book.tags, !tags.isEmpty {
                    HStack {
                        ForEach(tags.prefix(3), id: \.self) { tag in // 最多显示3个标签
                            Text(tag)
                                .font(.caption2)
                                .padding(.horizontal, 6)
                                .padding(.vertical, 2)
                                .background(Color.blue.opacity(0.1))
                                .foregroundColor(.blue)
                                .cornerRadius(4)
                        }
                    }
                }
            }
            
            Spacer() // 将加号按钮推到右边
            
            // 添加到书架按钮
            Button {
                // TODO: 实现书架功能
                // bookListVM.toggleShelfStatus(for: book) // 调用 ViewModel 中的方法
                print("点击了书架按钮: \(book.title), 当前书架状态: \(book.isInShelf)")
            } label: {
                Image(systemName: book.isInShelf ? "checkmark.circle.fill" : "plus.circle.fill")
                    .font(.title2)
                    .foregroundColor(book.isInShelf ? .green : .blue)
            }
        }
        .padding(.vertical, 8)
    }
}
