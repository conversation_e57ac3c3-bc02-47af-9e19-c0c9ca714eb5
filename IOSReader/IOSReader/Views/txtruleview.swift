// TXTRuleView.swift
// TXT目录规则配置视图
// 功能:
// 1. 正则表达式规则配置 - 用于解析TXT文件的目录结构
// 2. 目录结构预览 - 显示解析后的目录层级
// 3. 规则模板管理 - 提供常用规则模板和自定义保存功能

import SwiftUI
import SwiftData

struct TXTRuleView: View {
    @EnvironmentObject var coordinator: ReaderCoordinator
    
    // 正则表达式规则状态
    @State private var regexPattern: String = "(?:(?:\\n|^)(第[一二三四五六七八九十百千万零]+章|CHAPTER\\s+\\d+)[\\s\\S]*?(?=\\n第[一二三四五六七八九十百千万零]+章|CHAPTER\\s+\\d+|$))"
    @State private var testContent: String = ""
    @State private var parsedChapters: [Chapter] = []
    @State private var showPreview = false
    @State private var showErrorAlert = false
    @State private var errorMessage = ""
    @State private var selectedTemplateIndex = 0
    @State private var customTemplates: [(String, String)] = []
    @State private var newTemplateName = ""
    @State private var newTemplatePattern = ""
    @State private var showTemplateEditor = false
    
    let defaultRegexTemplates = [
        ("中文章节", "(?:\\n|^)(第[一二三四五六七八九十百千万零]+章)[\\s\\S]*?(?=\\n第[一二三四五六七八九十百千万零]+章|$)"),
        ("英文章节", "(?:\\n|^)(CHAPTER\\s+\\d+)[\\s\\S]*?(?=\\nCHAPTER\\s+\\d+|$)"),
        ("数字章节", "(?:\\n|^)(\\d+\\.[\\s\\S]*?)(?=\\n\\d+\\.|$)"),
        ("完整章节", "(?:(?:\\n|^)(第[一二三四五六七八九十百千万零]+章|CHAPTER\\s+\\d+)[\\s\\S]*?(?=\\n第[一二三四五六七八九十百千万零]+章|CHAPTER\\s+\\d+|$))")
    ]
    
    var regexTemplates: [(String, String)] {
        defaultRegexTemplates + customTemplates
    }

    

    var body: some View {
        Form {
            regexSection
            testContentSection
            previewToggleSection
            if showPreview && !parsedChapters.isEmpty {
                previewResultSection
            }
        }
        .navigationTitle("TXT目录规则")
        .alert(isPresented: $showErrorAlert) {
            errorAlert
        }
        .sheet(isPresented: $showTemplateEditor) {
            templateSheet
        }
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                Button("清空") {
                    self.clearAll()
                }
            }
        }
    }

    // MARK: - 子视图提取
    private var regexSection: some View {
        Section(header: Text("正则表达式规则")) {
            Picker("模板", selection: $selectedTemplateIndex) {
                ForEach(0..<regexTemplates.count, id: \.self) { index in
                    Text(regexTemplates[index].0).tag(index)
                }
            }
            .onChange(of: selectedTemplateIndex) { oldValue, newValue in
                regexPattern = regexTemplates[newValue].1
            }
            
            TextEditor(text: $regexPattern)
                .frame(height: 80)
                .font(.system(.body, design: .monospaced))
            
            Button("测试规则") {
                self.parseContentWithRegex()
            }
            
            Button("保存为模板") {
                showTemplateEditor = true
            }
        }
    }

    private var testContentSection: some View {
        Section(header: Text("测试内容")) {
            TextEditor(text: $testContent)
                .frame(height: 120)
        }
    }

    private var previewToggleSection: some View {
        Section {
            Toggle("显示预览", isOn: $showPreview)
        }
    }

    private var previewResultSection: some View {
        Section(header: Text("解析结果")) {
            List(parsedChapters, id: \.id) { chapter in
                chapterRow(chapter: chapter)
            }
        }
    }

    private func chapterRow(chapter: Chapter) -> some View {
        VStack(alignment: .leading) {
            Text(chapter.title)
                .font(.headline)
            Text("第\(chapter.index + 1)章")
                .font(.subheadline)
                .foregroundColor(.gray)
        }
    }

    // MARK: - Alert 和 Sheet 提取
    private var errorAlert: Alert {
        Alert(
            title: Text("错误"),
            message: Text(errorMessage),
            dismissButton: .default(Text("确定"))
        )
    }

    private var templateSheet: some View {
        TemplateEditorView(
            name: $newTemplateName,
            pattern: $newTemplatePattern,
            onSave: {
                if !newTemplateName.isEmpty && !newTemplatePattern.isEmpty {
                    customTemplates.append((newTemplateName, newTemplatePattern))
                    newTemplateName = ""
                    newTemplatePattern = ""
                    showTemplateEditor = false
                }
            },
            onCancel: {
                newTemplateName = ""
                newTemplatePattern = ""
                showTemplateEditor = false
            }
        )
    }
}

// TXTRuleView的扩展方法
extension TXTRuleView {
    // 使用正则表达式解析内容
    func parseContentWithRegex() {
        do {
            let processor = BookProcessor()
            processor.chapterPattern = self.regexPattern
            self.parsedChapters = try processor.parseChapters(content: self.testContent)
        } catch {
            self.errorMessage = "正则表达式错误: \(error.localizedDescription)"
            self.showErrorAlert = true
        }
    }
    
    // 清空所有内容
    func clearAll() {
        self.testContent = ""
        self.parsedChapters = []
        self.showPreview = false
    }
}

@MainActor
class BookProcessor {
    var chapterPattern: String = ""
    
    func parseChapters(content: String) throws -> [Chapter] {
        let regex = try NSRegularExpression(pattern: chapterPattern, options: [])
        let range = NSRange(location: 0, length: content.utf16.count)
        let matches = regex.matches(in: content, options: [], range: range)
        
        var chapters: [Chapter] = []
        var previousEnd = content.startIndex
        var totalPages = 0
        
        for (index, match) in matches.enumerated() {
            guard let range = Range(match.range, in: content) else { continue }
            
            // 获取章节标题
            let titleRange = content[range].range(of: #"第[一二三四五六七八九十百千万零]+章|CHAPTER\s+\d+"#,
                                                 options: .regularExpression)!
            let title = String(content[range][titleRange])
            
            // 获取章节内容
            let chapterStart = previousEnd
            let chapterEnd = range.lowerBound
            let chapterContent = String(content[chapterStart..<chapterEnd])
            
            if !chapterContent.isEmpty {
                let pages = splitToPages(content: chapterContent)
                chapters.append(Chapter(id: UUID(), 
                                  index: index, 
                                  title: title, 
                                  startPosition: content.distance(from: content.startIndex, to: previousEnd), 
                                  endPosition: content.distance(from: content.startIndex, to: range.upperBound), 
                                  content: chapterContent))
                totalPages += pages.count
            }
            
            previousEnd = range.upperBound
        }
        
        // 处理最后一章
        let remainingContent = String(content[previousEnd...])
        if !remainingContent.isEmpty {
            _ = splitToPages(content: remainingContent)
            chapters.append(Chapter(index: chapters.count, 
                                  title: "终章", 
                                  startPosition: content.distance(from: content.startIndex, to: previousEnd),
                                  endPosition: content.count,
                                  content: remainingContent))
        }
        
        return chapters
    }
    
    private func splitToPages(content: String) -> [String] {
        let pageSize = 1500
        var pages: [String] = []
        var currentPage = ""
        
        for char in content {
            currentPage.append(char)
            if currentPage.count >= pageSize && char.isNewline {
                pages.append(currentPage)
                currentPage = ""
            }
        }
        
        if !currentPage.isEmpty {
            pages.append(currentPage)
        }
        
        return pages
    }
}

#Preview {
    Group {
        if let container = try? ModelContainer(for: User.self, Book.self, BookSource.self, Bookmark.self, ReadingProgress.self) {
            NavigationStack {
                TXTRuleView()
            }
            .environmentObject(ReaderCoordinator(modelContainer: container))
        } else {
            Text("预览错误")
        }
    }
}