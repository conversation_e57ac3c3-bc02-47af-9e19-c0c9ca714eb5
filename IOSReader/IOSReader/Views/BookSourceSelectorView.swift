import SwiftUI
import SwiftData

// 书源选择视图 - 用于书城中的书源展示与选择
struct BookSourceSelectorView: View {
    @EnvironmentObject var coordinator: ReaderCoordinator
    @Environment(\.dismiss) private var dismiss
    
    // 分类切换器，例如 "小说" / "漫画"
    @State private var selectedSourceType = "小说" // 默认选中小说
    let sourceTypes = ["小说", "漫画"] // 可选的分类
    
    // 当前选中的书源
    @State private var selectedSource: BookSource?
    
    private var sourceTypePicker: some View {
        Picker("书源类型", selection: $selectedSourceType) {
            ForEach(sourceTypes, id: \.self) { type in
                Text(type).tag(type)
            }
        }
        .pickerStyle(SegmentedPickerStyle())
        .padding()
    }
    
    // 筛选后的书源列表
    private var filteredSources: [BookSource] {
        coordinator.sourceManager.bookSources.filter { source in
            source.enabled // 只显示启用的书源
            // TODO: 根据BookSource模型添加类型筛选
            // && (source.type?.lowercased() == selectedSourceType.lowercased())
        }
    }
    
    private var sourceListSection: some View {
        List {
            ForEach(filteredSources) { source in
                BookSourceRow(source: source, isSelected: selectedSource?.id == source.id) {
                    selectedSource = source
                    // TODO: 通知书城视图切换当前书源
                    coordinator.sourceManager.setCurrentSource(source)
                }
            }
        }
    }
    
    var body: some View {
        NavigationView {
            VStack {
                sourceTypePicker
                sourceListSection
            }
            .navigationTitle("选择书源")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button(action: { dismiss() }) {
                        Image(systemName: "chevron.left")
                    }
                }
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("管理") {
                        // 跳转到书源管理页面
                        coordinator.presentSourceManager()
                        dismiss()
                    }
                }
            }
        }
        .onAppear {
            // 初始化选中当前书源
            selectedSource = coordinator.sourceManager.currentSource
        }
    }
}

// 书源选择行视图
struct BookSourceRow: View {
    let source: BookSource
    let isSelected: Bool
    let onTap: () -> Void
    
    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text(source.name)
                    .font(.headline)
                    .foregroundColor(isSelected ? .blue : .primary)
                Text(source.url)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .lineLimit(1)
            }
            
            Spacer()
            
            if isSelected {
                Image(systemName: "checkmark.circle.fill")
                    .foregroundColor(.blue)
            }
        }
        .padding(.vertical, 8)
        .contentShape(Rectangle())
        .onTapGesture {
            onTap()
        }
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(isSelected ? Color.blue.opacity(0.1) : Color.clear)
        )
    }
}

#Preview {
    Group {
        if let container = try? ModelContainer(for: User.self, Book.self, BookSource.self, Bookmark.self, ReadingProgress.self) {
            BookSourceSelectorView()
                .environmentObject(ReaderCoordinator(modelContainer: container))
        } else {
            Text("预览错误")
        }
    }
}
