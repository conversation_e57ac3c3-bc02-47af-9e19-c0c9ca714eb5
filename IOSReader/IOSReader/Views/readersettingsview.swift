import SwiftUI

/// 阅读设置视图
/// 允许用户自定义阅读体验，包括字体大小、行间距、背景颜色等
struct ReaderSettingsView: View {
    @Binding var settings: ReaderSettings
    @Environment(\.colorScheme) private var colorScheme
    @Environment(\.dismiss) private var dismiss
    @State private var selectedTab = 0
    
    private let fontSizes: [CGFloat] = [14, 16, 18, 20, 22, 24, 26]
    private let lineSpacings: [CGFloat] = [4, 6, 8, 10, 12]
    private let themes = ["日间", "夜间", "护眼"]
    
    var body: some View {
        NavigationView {
            Form {
                Section(header: Text("字体大小")) {
                    Slider(value: $settings.fontSize, in: 14...26, step: 1.0) {
                        Text("字体大小")
                    } minimumValueLabel: {
                        Text("A").font(.system(size: 14))
                    } maximumValueLabel: {
                        Text("A").font(.system(size: 26))
                    }
                    .onChange(of: settings.fontSize) { _ in
                        // 触觉反馈
                        #if os(iOS)
                        let generator = UIImpactFeedbackGenerator(style: .light)
                        generator.impactOccurred()
                        #endif
                    }
                    
                    Text("预览文本").font(.system(size: settings.fontSize))
                        .padding(.vertical, 8)
                }
                
                Section(header: Text("行间距")) {
                    Slider(value: $settings.lineSpacing, in: 4...12, step: 1.0) {
                        Text("行间距")
                    }
                    
                    Text("预览文本\n第二行文本")
                        .font(.system(size: settings.fontSize))
                        .lineSpacing(settings.lineSpacing)
                        .padding(.vertical, 8)
                }
                
                Section(header: Text("段落间距")) {
                    Slider(value: $settings.paragraphSpacing, in: 8...20, step: 1.0) {
                        Text("段落间距")
                    }
                }
                
                Section(header: Text("主题")) {
                    Picker("主题", selection: $selectedTab) {
                        ForEach(0..<themes.count, id: \.self) { index in
                            Text(themes[index])
                        }
                    }
                    .pickerStyle(SegmentedPickerStyle())
                    .onChange(of: selectedTab) { newValue in
                        switch newValue {
                        case 0:
                            settings.applyDayMode()
                        case 1:
                            settings.applyNightMode()
                        case 2:
                            settings.applySepia()
                        default:
                            break
                        }
                    }
                    
                    HStack(spacing: 15) {
                        ColorPreview(color: .white, name: "白色", isSelected: settings.backgroundColor == .white) {
                            settings.applyDayMode()
                            selectedTab = 0
                        }
                        
                        ColorPreview(color: Color(red: 0.1, green: 0.1, blue: 0.1), name: "黑色", isSelected: settings.backgroundColor.description.contains("0.1")) {
                            settings.applyNightMode()
                            selectedTab = 1
                        }
                        
                        ColorPreview(color: Color(red: 0.98, green: 0.94, blue: 0.85), name: "护眼", isSelected: settings.backgroundColor.description.contains("0.98")) {
                            settings.applySepia()
                            selectedTab = 2
                        }
                    }
                    .padding(.vertical, 8)
                }
            }
            .navigationTitle("阅读设置")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("完成") {
                        dismiss()
                    }
                }
            }
        }
        .onAppear {
            // 设置初始选中的主题
            if settings.backgroundColor == .white {
                selectedTab = 0
            } else if settings.backgroundColor.description.contains("0.1") {
                selectedTab = 1
            } else {
                selectedTab = 2
            }
        }
    }
}

/// 颜色预览组件
struct ColorPreview: View {
    let color: Color
    let name: String
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack {
                Circle()
                    .fill(color)
                    .frame(width: 40, height: 40)
                    .overlay(
                        Circle()
                            .stroke(isSelected ? Color.blue : Color.clear, lineWidth: 3)
                    )
                    .shadow(radius: 2)
                
                Text(name)
                    .font(.caption)
            }
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct ReaderSettingsView_Previews: PreviewProvider {
    static var previews: some View {
        ReaderSettingsView(settings: .constant(ReaderSettings()))
    }
}