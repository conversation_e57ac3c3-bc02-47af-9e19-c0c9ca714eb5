import SwiftUI
import SwiftData
//import SwiftData

struct BookDetailView: View {
    @EnvironmentObject var coordinator: ReaderCoordinator
    @Environment(\.dismiss) private var dismiss
    let book: Book
    
    var body: some View {
        ScrollView {
            VStack(spacing: 20) {
                // 封面图
                AsyncImage(url: book.coverUrl) { phase in
                    if let image = phase.image {
                        image
                            .resizable()
                            .aspectRatio(contentMode: .fit)
                    } else if phase.error != nil {
                        // 加载失败时显示错误提示
                        VStack {
                            Image(systemName: "exclamationmark.triangle.fill")
                                .foregroundColor(.red)
                            Text("图片加载失败")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    } else {
                        // 加载中或URL为空时显示占位符
                        Color.gray
                    }
                }
                .frame(width: 120, height: 160)
                .cornerRadius(8)
                .shadow(radius: 4)
                
                // 基本信息
                VStack(spacing: 8) {
                    Text(book.title)
                        .font(.title2)
                        .bold()
                    
                    Text(book.author)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                
                // 最新章节
                if let latest = book.latestChapter {
                    VStack(alignment: .leading) {
                        Text("最新章节")
                            .font(.headline)
                        Text(latest)
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding(.horizontal)
                }
                
                // 简介
                if let intro = book.introduction {
                    VStack(alignment: .leading) {
                        Text("简介")
                            .font(.headline)
                        Text(intro)
                            .font(.body)
                            .foregroundColor(.secondary)
                    }
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding(.horizontal)
                }
                
                // 阅读按钮
                Button(action: {
                    coordinator.currentBook = book
                    dismiss()
                }) {
                    Text("开始阅读")
                        .font(.headline)
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.blue)
                        .cornerRadius(8)
                }
                .padding(.horizontal)
                .padding(.top, 20)
            }
            .padding()
        }
        .navigationBarTitleDisplayMode(.inline)
    }
}

#Preview {
    let config = ModelConfiguration(isStoredInMemoryOnly: true)
    let container = try! ModelContainer(
        for: Book.self, BookSource.self, Item.self, Bookmark.self,
        ReadingProgress.self,
        configurations: config
    )
    
    let coordinator = ReaderCoordinator(modelContainer: container)
    
    let previewBook = Book(
        title: "示例书籍",
        author: "示例作者",
        coverUrl: URL(string: "https://example.com/cover.jpg"),
        introduction: "这是一本示例书籍的简介，用于预览界面效果。",
        latestChapter: "第十章 示例章节",
        url: URL(string: "https://example.com/book")!,
        sourceId: UUID(),
        bookType: "txt",
        filePath: nil
    )
    
    NavigationView {
        BookDetailView(book: previewBook)
            .environmentObject(coordinator)
    }
    .modelContainer(container)
}
