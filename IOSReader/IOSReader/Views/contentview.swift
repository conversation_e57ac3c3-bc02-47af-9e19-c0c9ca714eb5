// ContentView - 应用主界面
// 功能:
// 1. 管理底部标签导航: 包括书架、书城和我的三个主要功能模块
// 2. 协调各个功能页面的展示
// 3. 初始化ReaderCoordinator并传递给子视图
//
// 核心功能:
// 1. 底部标签导航管理: 提供书架、书城和我的三个主要功能模块的切换
// 2. 初始化ReaderCoordinator: 创建并管理应用的核心协调器 (已移除，从 Environment 获取)
// 3. 模块协调: 统一管理各功能模块的展示和交互
//
// 主要UI组件:
// - TabView: 底部导航栏容器
// - 三个TabItem: 分别对应书架、书城和我的功能
// - 各功能视图: BookShelfView、BookStoreView、SettingsView

import SwiftUI

struct ContentView: View {
    // 从环境中获取共享的 ReaderCoordinator 实例
    @EnvironmentObject private var coordinator: ReaderCoordinator
    // 移除本地 @StateObject 初始化
    // @StateObject private var coordinator = ReaderCoordinator(
    //     themeColor: .blue,
    //     readingMode: .scrolling,
    //     bookSources: [],
    //     books: [],
    //     bookmarks: [:]
    // )
    @State private var selectedTab = 0
    @State private var isReading = false
    @State private var currentBook: Book? = nil
    @State private var errorMessage: String? = nil
    @State private var showErrorAlert = false
    
    var body: some View {
        ZStack {
            if isReading, let book = currentBook, let filePath = book.filePath, !filePath.isEmpty, FileManager.default.fileExists(atPath: filePath) {                
                // 阅读视图 - 全屏显示，不显示TabBar
                // 添加文件存在检查，避免尝试打开不存在的文件
                UnifiedReaderView(filePath: filePath, modelContainer: coordinator.modelContainer)
                    .environmentObject(coordinator) // 确保传递 coordinator
                    .edgesIgnoringSafeArea(.all)
                    .onDisappear {
                        isReading = false
                    }
            } else if isReading {
                // 如果isReading为true但文件不存在，显示错误并重置状态
                Color.clear.onAppear {
                    errorMessage = "无法打开书籍：文件不存在或路径无效"
                    showErrorAlert = true
                    isReading = false
                }
            } else {
                // 主TabView - 非阅读状态下显示
                TabView(selection: $selectedTab) {
                    // 书架页面
                    BookShelfView(coordinator: coordinator)
                        .onAppear { print("BookShelfView已加载") }
                        .tabItem {
                            Image(systemName: "books.vertical")
                            Text("书架")
                        }
                        .tag(0)
                    
                    // 书城页面
                    BookStoreView(coordinator: coordinator)
                        .onAppear { print("BookStoreView已加载") }
                        .tabItem {
                            Image(systemName: "cart")
                            Text("书城")
                        }
                        .tag(1)
                    
                    // 我的页面
                    SettingsView()
                        .onAppear { print("SettingsView已加载") }
                        .tabItem {
                            Image(systemName: "person")
                            Text("我的")
                        }
                        .tag(2)
                }
                .toolbarBackground(.hidden)
                .tint(.blue)
            }
        }
        .environmentObject(coordinator) // Ensure coordinator is available to all children
        .alert("加载错误", isPresented: $showErrorAlert, actions: {
            Button("确定", role: .cancel) { 
                // 重置状态
                isReading = false
                currentBook = nil
            }
        }, message: {
            Text(errorMessage ?? "未知错误")
        })
        // 监听 coordinator 的 $currentBook 属性变化
        .onReceive(coordinator.$currentBook) { book in
            // 使用Task确保在主线程上处理，避免阻塞UI
            Task { @MainActor in
                if let book = book {
                    if let filePath = book.filePath, !filePath.isEmpty, FileManager.default.fileExists(atPath: filePath) {
                        currentBook = book
                        isReading = true
                    } else {
                        errorMessage = "无法打开书籍：文件路径无效或文件不存在"
                        showErrorAlert = true
                    }
                }
            }
        }
    }
}

import SwiftData // 确保导入 SwiftData

struct ContentView_Previews: PreviewProvider {
    @MainActor // 确保在主线程上创建模型容器
    static var previews: some View {
        // 为预览创建一个内存中的 ModelContainer
        do {
            let schema = Schema([
                Book.self,
                BookSource.self,
                Item.self, // 添加 Item.self
                Bookmark.self,
                ReadingProgress.self // 添加 ReadingProgress.self
                // 添加其他需要的模型
            ])
            let configuration = ModelConfiguration(isStoredInMemoryOnly: true)
            let container = try ModelContainer(for: schema, configurations: [configuration])
            
            // 使用内存容器创建 Coordinator
            let coordinator = ReaderCoordinator(modelContainer: container)
            
            // 使用 AnyView 包装所有返回，确保类型一致
            return AnyView(ContentView()
                .modelContainer(container) // Provide the container first
                .environmentObject(coordinator)) // Then provide the coordinator
        } catch {
            // 如果创建失败，显示错误文本，已包装在 AnyView 中
            return AnyView(Text("Failed to create preview: \(error.localizedDescription)"))
        }
    }
}