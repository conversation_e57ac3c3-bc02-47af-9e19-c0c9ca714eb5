//
//  ReadingProgress.swift
//  IOSReader
//
//  Created by Trae AI on $(date +'%Y/%m/%d').
//

import Foundation
import SwiftData

@Model
final class ReadingProgress {
    @Attribute(.unique) var bookID: String
    var chapterIndex: Int
    var pageNumber: Int
    var lastReadDate: Date

    init(bookID: String, chapterIndex: Int, pageNumber: Int, lastReadDate: Date = Date()) {
        self.bookID = bookID
        self.chapterIndex = chapterIndex
        self.pageNumber = pageNumber
        self.lastReadDate = lastReadDate
    }
}