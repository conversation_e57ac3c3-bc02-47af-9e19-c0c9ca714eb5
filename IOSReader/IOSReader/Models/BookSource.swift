import SwiftUI
import SwiftData

// MARK: - BookSource Category
enum BookSourceCategory: String, CaseIterable, Identifiable, Codable {
    case novel = "小说"
    case comic = "漫画"
    case audio = "有声"
    case video = "视频"
    case rss = "RSS"
    case other = "其他"
    
    var id: String { self.rawValue }
    
    // 本地化名称，用于UI显示
    var localizedName: String {
        return self.rawValue
    }
}

// MARK: - SwiftData Model

@Model
final class BookSource: Identifiable, @unchecked Sendable {
    // 类型别名，用于视图层引用
    typealias Category = BookSourceCategory
    var id: UUID
    var name: String // 对应bookSourceName
    var url: String // 对应bookSourceUrl
    var sourceGroup: String? // 对应bookSourceGroup
    var sourceType: Int? // 对应bookSourceType
    var category: String // 书源分类，存储为字符串
    var customOrder: Int? // 对应customOrder
    var isEnabled: Bool // 对应enabled
    var enabledExplore: Bool? // 对应enabledExplore
    var header: String? // 对应header
    var loginUrl: String? // 对应loginUrl
    var searchUrl: String? // 对应searchUrl
    var ruleBookInfoData: Data? // 将ruleBookInfo存储为JSON数据
    var ruleContentData: Data? // 将ruleContent存储为JSON数据
    var ruleExploreData: Data? // 将ruleExplore存储为JSON数据
    var ruleSearchData: Data? // 将ruleSearch存储为JSON数据
    var ruleTocData: Data? // 将ruleToc存储为JSON数据
    var lastUpdated: Date? // 对应lastUpdateTime(需要转换)
    var weight: Int? // 对应weight
    var respondTime: Int? // 对应respondTime
    var bookUrlPattern: String? // 对应bookUrlPattern
    var version: String? // 书源版本号
    
    // 计算属性，用于SourceManagerView中的Toggle绑定
    var enabled: Bool {
        get { isEnabled }
        set { isEnabled = newValue }
    }

    // 添加接受V3结构体并执行映射/编码的初始化方法
    init(from v3Source: BookSourceV3) throws {
        self.id = UUID()
        self.name = v3Source.bookSourceName
        self.url = v3Source.bookSourceUrl
        self.sourceGroup = v3Source.bookSourceGroup
        self.sourceType = v3Source.bookSourceType
        self.category = BookSourceCategory.novel.rawValue // 默认设置为小说分类
        self.customOrder = v3Source.customOrder
        self.isEnabled = v3Source.enabled
        self.enabledExplore = v3Source.enabledExplore
        self.header = v3Source.header
        self.loginUrl = v3Source.loginUrl
        self.searchUrl = v3Source.searchUrl
        self.bookUrlPattern = v3Source.bookUrlPattern // Map bookUrlPattern
        self.weight = v3Source.weight
        self.respondTime = v3Source.respondTime

        // Convert timestamp to Date
        if let timestamp = v3Source.lastUpdateTime {
            // Assuming timestamp is in milliseconds
            self.lastUpdated = Date(timeIntervalSince1970: TimeInterval(timestamp) / 1000.0)
        } else {
            self.lastUpdated = Date() // Default to now if not present
        }

        // Encode rule objects to Data
        let encoder = JSONEncoder()
        self.ruleBookInfoData = try? encoder.encode(v3Source.ruleBookInfo)
        self.ruleContentData = try? encoder.encode(v3Source.ruleContent)
        self.ruleExploreData = try? encoder.encode(v3Source.ruleExplore)
        self.ruleSearchData = try? encoder.encode(v3Source.ruleSearch)
        self.ruleTocData = try? encoder.encode(v3Source.ruleToc)
    }

    // 默认初始化方法(@Model需要)
    init(id: UUID = UUID(), name: String = "", url: String = "", sourceGroup: String? = nil, sourceType: Int? = nil, category: String = BookSourceCategory.novel.rawValue, customOrder: Int? = nil, isEnabled: Bool = true, enabledExplore: Bool? = nil, header: String? = nil, loginUrl: String? = nil, searchUrl: String? = nil, ruleBookInfoData: Data? = nil, ruleContentData: Data? = nil, ruleExploreData: Data? = nil, ruleSearchData: Data? = nil, ruleTocData: Data? = nil, lastUpdated: Date? = Date(), weight: Int? = nil, respondTime: Int? = nil, bookUrlPattern: String? = nil) {
         self.id = id
         self.name = name
         self.url = url
         self.sourceGroup = sourceGroup
         self.sourceType = sourceType
         self.category = category
         self.customOrder = customOrder
         self.isEnabled = isEnabled
         self.enabledExplore = enabledExplore
         self.header = header
         self.loginUrl = loginUrl
         self.searchUrl = searchUrl
         self.ruleBookInfoData = ruleBookInfoData
         self.ruleContentData = ruleContentData
         self.ruleExploreData = ruleExploreData
         self.ruleSearchData = ruleSearchData
         self.ruleTocData = ruleTocData
         self.lastUpdated = lastUpdated
         self.weight = weight
         self.respondTime = respondTime
         self.bookUrlPattern = bookUrlPattern
     }
}

// MARK: - BookSourceV3 Structure (Define based on actual JSON)
// 此结构体表示外部导入的JSON数据格式
struct BookSourceV3: Codable {
    enum CodingKeys: String, CodingKey {
        case bookSourceName
        case bookSourceUrl
        case bookSourceGroup
        case bookSourceType
        case customOrder
        case enabled
        case enabledExplore
        case header
        case loginUrl
        case searchUrl
        case bookUrlPattern
        case weight
        case exploreUrl
        case ruleBookInfo
        case ruleContent
        case ruleExplore
        case ruleSearch
        case ruleToc
        case lastUpdateTime
        case respondTime
    }
    
    var bookSourceName: String
    var bookSourceUrl: String
    var bookSourceGroup: String?
    var bookSourceType: Int?
    var customOrder: Int?
    var enabled: Bool
    var enabledExplore: Bool?
    var header: String?
    var loginUrl: String?
    var searchUrl: String?
    var bookUrlPattern: String?
    var weight: Int?
    var exploreUrl: String?
    var ruleBookInfo: [String: String]?
    var ruleContent: [String: String]?
    var ruleExplore: [String: String]?
    var ruleSearch: [String: String]?
    var ruleToc: [String: String]?
    var lastUpdateTime: TimeInterval?
    var respondTime: Int?

    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        bookSourceName = try container.decode(String.self, forKey: .bookSourceName)
        bookSourceUrl = try container.decode(String.self, forKey: .bookSourceUrl)
        bookSourceGroup = try container.decodeIfPresent(String.self, forKey: .bookSourceGroup)
        bookSourceType = try container.decodeIfPresent(Int.self, forKey: .bookSourceType)
        customOrder = try container.decodeIfPresent(Int.self, forKey: .customOrder)
        enabled = try container.decode(Bool.self, forKey: .enabled)
        enabledExplore = try container.decodeIfPresent(Bool.self, forKey: .enabledExplore)
        header = try container.decodeIfPresent(String.self, forKey: .header)
        loginUrl = try container.decodeIfPresent(String.self, forKey: .loginUrl)
        searchUrl = try container.decodeIfPresent(String.self, forKey: .searchUrl)
        bookUrlPattern = try container.decodeIfPresent(String.self, forKey: .bookUrlPattern)
        weight = try container.decodeIfPresent(Int.self, forKey: .weight)
        exploreUrl = try container.decodeIfPresent(String.self, forKey: .exploreUrl)
        ruleBookInfo = try container.decodeIfPresent([String: String].self, forKey: .ruleBookInfo)
        ruleContent = try container.decodeIfPresent([String: String].self, forKey: .ruleContent)
        ruleExplore = try container.decodeIfPresent([String: String].self, forKey: .ruleExplore)
        ruleSearch = try container.decodeIfPresent([String: String].self, forKey: .ruleSearch)
        ruleToc = try container.decodeIfPresent([String: String].self, forKey: .ruleToc)
        lastUpdateTime = try container.decodeIfPresent(TimeInterval.self, forKey: .lastUpdateTime)
        respondTime = try container.decodeIfPresent(Int.self, forKey: .respondTime)
    }
}