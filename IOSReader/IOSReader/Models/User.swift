//
//  User.swift
//  IOSReader
//
//  用户模型
//  功能:
//  - 定义用户账号的数据结构
//  - 支持用户基本信息管理
//  - 集成SwiftData持久化存储
//  - 提供用户状态和权限管理
//

import Foundation
import SwiftData

/// 用户模型
/// 管理用户账号信息和状态
@Model
class User {
    /// 用户唯一标识符
    @Attribute(.unique) var id: UUID
    
    /// 用户名（唯一）
    @Attribute(.unique) var username: String
    
    /// 邮箱地址（唯一）
    @Attribute(.unique) var email: String
    
    /// 密码哈希值（不直接存储明文密码）
    var passwordHash: String
    
    /// 用户昵称
    var nickname: String
    
    /// 头像URL或本地路径
    var avatarPath: String?
    
    /// 用户等级
    var userLevel: UserLevel
    
    /// 用户等级（别名，用于兼容性）
    var level: UserLevel { userLevel }
    
    /// 用户个人简介
    var bio: String
    
    /// 注册时间
    var registrationDate: Date
    
    /// 创建时间（别名，用于兼容性）
    var createdAt: Date { registrationDate }
    
    /// 最后登录时间
    var lastLoginDate: Date?
    
    /// 最后登录时间（别名，用于兼容性）
    var lastLoginAt: Date? { lastLoginDate }
    
    /// 是否已验证邮箱
    var isEmailVerified: Bool
    
    /// 账号状态
    var accountStatus: AccountStatus
    
    /// 账号状态（别名，用于兼容性）
    var status: AccountStatus { accountStatus }
    
    /// 用户偏好设置
    var preferences: UserPreferences?
    
    /// 初始化用户
    /// - Parameters:
    ///   - username: 用户名
    ///   - email: 邮箱地址
    ///   - passwordHash: 密码哈希值
    ///   - nickname: 用户昵称
    ///   - bio: 个人简介
    init(username: String, email: String, passwordHash: String, nickname: String, bio: String = "") {
        self.id = UUID()
        self.username = username
        self.email = email
        self.passwordHash = passwordHash
        self.nickname = nickname
        self.bio = bio
        self.userLevel = .regular
        self.registrationDate = Date()
        self.isEmailVerified = false
        self.accountStatus = .active
    }
    
    /// 更新最后登录时间
    func updateLastLoginDate() {
        self.lastLoginDate = Date()
    }
    
    /// 验证邮箱
    func verifyEmail() {
        self.isEmailVerified = true
    }
    
    /// 更新用户信息
    /// - Parameters:
    ///   - nickname: 新昵称
    ///   - avatarPath: 新头像路径
    func updateProfile(nickname: String? = nil, avatarPath: String? = nil) {
        if let nickname = nickname {
            self.nickname = nickname
        }
        if let avatarPath = avatarPath {
            self.avatarPath = avatarPath
        }
    }
}

/// 用户等级枚举
enum UserLevel: String, CaseIterable, Codable {
    case regular = "普通用户"
    case vip = "VIP用户"
    case premium = "高级用户"
    case admin = "管理员"
    
    /// 等级权限描述
    var description: String {
        switch self {
        case .regular:
            return "基础功能使用权限"
        case .vip:
            return "高级功能使用权限"
        case .premium:
            return "全功能使用权限"
        case .admin:
            return "系统管理权限"
        }
    }
    
    /// 等级图标
    var icon: String {
        switch self {
        case .regular:
            return "person.circle"
        case .vip:
            return "crown"
        case .premium:
            return "star.circle.fill"
        case .admin:
            return "shield.fill"
        }
    }
}

/// 账号状态枚举
enum AccountStatus: String, CaseIterable, Codable {
    case active = "正常"
    case suspended = "暂停"
    case banned = "封禁"
    case pending = "待激活"
    
    /// 状态描述
    var description: String {
        return self.rawValue
    }
    
    /// 状态颜色
    var color: String {
        switch self {
        case .active:
            return "green"
        case .suspended:
            return "orange"
        case .banned:
            return "red"
        case .pending:
            return "blue"
        }
    }
}

/// 用户偏好设置
@Model
class UserPreferences {
    /// 偏好设置ID
    var id: UUID
    
    /// 关联的用户
    var user: User?
    
    /// 是否接收推送通知
    var enablePushNotifications: Bool
    
    /// 是否接收邮件通知
    var enableEmailNotifications: Bool
    
    /// 默认阅读模式
    var defaultReadingMode: String
    
    /// 自动同步设置
    var autoSync: Bool
    
    /// 数据使用偏好（仅WiFi同步等）
    var dataUsagePreference: DataUsagePreference
    
    /// 初始化用户偏好
    init() {
        self.id = UUID()
        self.enablePushNotifications = true
        self.enableEmailNotifications = true
        self.defaultReadingMode = "day"
        self.autoSync = true
        self.dataUsagePreference = .wifiOnly
    }
}

/// 数据使用偏好
enum DataUsagePreference: String, CaseIterable, Codable {
    case wifiOnly = "仅WiFi"
    case wifiAndCellular = "WiFi和蜂窝网络"
    case manual = "手动同步"
    
    var description: String {
        switch self {
        case .wifiOnly:
            return "仅在WiFi环境下同步数据"
        case .wifiAndCellular:
            return "WiFi和蜂窝网络都可同步"
        case .manual:
            return "手动控制数据同步"
        }
    }
}