import Foundation
import SwiftData

// 用于存储的章节数据结构
public struct ChapterData: Identifiable, Sendable, Codable {
    public var id: UUID
    public var index: Int          // 章节序号
    public var title: String       // 章节标题
    public var startPosition: Int  // 章节在原文中的起始位置（例如字符数或行号）
    public var endPosition: Int?   // 章节在原文中的结束位置（可选，用于确定章节内容范围）
    public var content: String?    // 章节内容（可选，可以按需加载）
    public var url: URL?           // 章节URL，用于网络书源
    public var bookId: UUID?       // 关联的书籍ID

    public init(id: UUID = UUID(), index: Int, title: String, startPosition: Int, endPosition: Int? = nil, content: String? = nil, url: URL? = nil, bookId: UUID? = nil) {
        self.id = id
        self.index = index
        self.title = title
        self.startPosition = startPosition
        self.endPosition = endPosition
        self.content = content
        self.url = url
        self.bookId = bookId
    }
}

// 定义章节模型
@Model
public final class Chapter: Identifiable, @unchecked Sendable {
    @Attribute(.unique) public var id: UUID
    public var index: Int          // 章节序号
    public var title: String       // 章节标题
    public var startPosition: Int  // 章节在原文中的起始位置（例如字符数或行号）
    public var endPosition: Int?   // 章节在原文中的结束位置（可选，用于确定章节内容范围）
    public var content: String?    // 章节内容（可选，可以按需加载）
    public var url: URL?           // 章节URL，用于网络书源
    public var bookId: UUID?       // 关联的书籍ID

    public init(id: UUID = UUID(), index: Int, title: String, startPosition: Int, endPosition: Int? = nil, content: String? = nil, url: URL? = nil, bookId: UUID? = nil) {
        self.id = id
        self.index = index
        self.title = title
        self.startPosition = startPosition
        self.endPosition = endPosition
        self.content = content
        self.url = url
        self.bookId = bookId
    }
    
    // 从 ChapterData 创建 Chapter
    public convenience init(from data: ChapterData) {
        self.init(
            id: data.id,
            index: data.index,
            title: data.title,
            startPosition: data.startPosition,
            endPosition: data.endPosition,
            content: data.content,
            url: data.url,
            bookId: data.bookId
        )
    }
    
    // 转换为 ChapterData
    public func toChapterData() -> ChapterData {
        return ChapterData(
            id: self.id,
            index: self.index,
            title: self.title,
            startPosition: self.startPosition,
            endPosition: self.endPosition,
            content: self.content,
            url: self.url,
            bookId: self.bookId
        )
    }
}

// 扩展：添加比较功能
extension Chapter: Comparable {
    public static func < (lhs: Chapter, rhs: Chapter) -> Bool {
        // 首先按书籍ID比较（如果都有的话）
        if let lhsBookId = lhs.bookId, let rhsBookId = rhs.bookId {
            if lhsBookId != rhsBookId {
                return lhsBookId.uuidString < rhsBookId.uuidString
            }
            // 如果是同一本书，按章节序号比较
            return lhs.index < rhs.index
        } else if lhs.bookId != nil && rhs.bookId == nil {
            return true  // 有书籍ID的排在前面
        } else if lhs.bookId == nil && rhs.bookId != nil {
            return false // 没有书籍ID的排在后面
        } else {
            // 都没有书籍ID，直接按序号比较
            // 注意：这种情况下可能需要额外的逻辑来处理
            // 或者如果 bookId 必须存在，则这里应该有不同的逻辑
            // 为简单起见，如果 bookId 不同，我们默认不小于
            // 但更好的做法是确保比较的章节属于同一本书，或者有明确的跨书比较规则
            // 此处假设比较总是在同一本书的上下文中进行，或者 index 是全局唯一的
        }
        return lhs.index < rhs.index
    }
}