//
//  Bookmark.swift
//  IOSReader
//
//  书签模型

import Foundation
import SwiftData

@Model
public final class Bookmark: Identifiable, @unchecked Sendable { // 移除显式 Codable
    public var id: UUID
    var bookId: UUID // 关联的书籍ID
    var pageIndex: Int // 页码
    var chapterIndex: Int? // 章节索引，可选
    var content: String // 书签内容预览或核心文本
    var userDescription: String? // 用户自定义的描述，可选
    var createdAt: Date // 创建时间

    init(id: UUID = UUID(), 
         bookId: UUID, 
         pageIndex: Int, 
         chapterIndex: Int? = nil, 
         content: String, 
         userDescription: String? = nil, 
         createdAt: Date = Date()) {
        self.id = id
        self.bookId = bookId
        self.pageIndex = pageIndex
        self.chapterIndex = chapterIndex
        self.content = content
        self.userDescription = userDescription
        self.createdAt = createdAt
    }

    // 移除了手动的 Codable 实现和 CodingKeys，让 @Model 自动合成

    // 用于显示的书签文本
    func displayText() -> String {
        if let desc = userDescription, !desc.isEmpty {
            return desc
        }
        return content
    }
    

}