import Foundation
import SwiftData
import SwiftUI // 新增：用于 Image 类型
#if canImport(UIKit)
import UIKit
#elseif canImport(AppKit)
import AppKit
#endif

// Import the required classes for chapter handling
// These are defined in other files in the project

@Model
final class Book: Identifiable, Equatable {
    @Attribute(.unique) var id = UUID()
    var title: String
    var author: String
    var coverUrl: URL?
    var introduction: String?
    var latestChapter: String?
    var url: URL
    var sourceId: UUID // 关联的书源ID
    var bookType: String? // 书籍类型，如txt、epub等
    var filePath: String? // 本地文件路径，用于打开本地文件
    var tags: [String]? // 书籍标签
    var wordCount: String? // 字数，例如 "1608.04万字"
    var chapterCount: Int? // 章节数
    var isInShelf: Bool = false // 是否已在书架

    // 章节列表 (非持久化，按需加载)
    // @Transient private(set) var chapters: [Chapter] = []

    // 新增存储属性
    var lastReadTimestamp: Double? // 存储 Date 的 timeIntervalSince1970
    var coverImageData: Data?    // 存储封面图片数据

    // 计算属性
    var lastReadDate: Date? {
        get {
            guard let timestamp = lastReadTimestamp else { return nil }
            return Date(timeIntervalSince1970: timestamp)
        }
        set {
            lastReadTimestamp = newValue?.timeIntervalSince1970
        }
    }

    var coverImage: Image? {
        get {
            guard let data = coverImageData else { return nil }
            // 根据平台条件编译创建 Image
#if canImport(UIKit)
            if let uiImage = UIImage(data: data) {
                return Image(uiImage: uiImage)
            }
#elseif canImport(AppKit)
            if let nsImage = NSImage(data: data) {
                return Image(nsImage: nsImage)
            }
#endif
            return nil // 如果无法创建图像，则返回nil
        }
        // coverImage 是基于 coverImageData 的计算属性，通常设为只读
        // 如果需要修改封面，应直接修改 coverImageData
        // 或者提供一个方法来设置图片数据并更新 coverImageData
    }

    static func == (lhs: Book, rhs: Book) -> Bool {
        lhs.id == rhs.id
    }

    // 更新 init 方法以包含新属性
    init(title: String, author: String, coverUrl: URL? = nil, introduction: String? = nil, latestChapter: String? = nil, url: URL, sourceId: UUID, bookType: String? = nil, filePath: String? = nil, lastReadTimestamp: Double? = nil, coverImageData: Data? = nil, tags: [String]? = nil, wordCount: String? = nil, chapterCount: Int? = nil, isInShelf: Bool = false) {
        self.id = UUID() // 确保 id 被初始化，如果它不是在声明时初始化的话
        self.title = title
        self.author = author
        self.coverUrl = coverUrl
        self.introduction = introduction
        self.latestChapter = latestChapter
        self.url = url
        self.sourceId = sourceId
        self.bookType = bookType
        self.filePath = filePath
        self.lastReadTimestamp = lastReadTimestamp
        self.coverImageData = coverImageData
        self.tags = tags
        self.wordCount = wordCount
        self.chapterCount = chapterCount
        self.isInShelf = isInShelf
    }

    // TODO: Chapter-related methods will be implemented once Chapter dependencies are resolved
    // These methods are temporarily commented out to fix compilation issues

    /*
    /// 获取或解析书籍的章节列表
    /// - Parameter completion: 完成回调，返回章节数组或nil（如果失败）
    // 移除基于回调的实现，统一使用异步版本
    @available(*, deprecated, message: "Use async version instead")
    nonisolated func fetchChapters(completion: @escaping @MainActor ([Chapter]?) -> Void) {
        Task { @MainActor in
            do {
                let chapters = try await self.fetchChapters(progressCallback: { @Sendable _ in })
                completion(chapters)
            } catch {
                print("获取章节失败: \(error.localizedDescription)")
                completion(nil)
            }
        }
    }

    /// 获取或解析书籍的章节列表（异步版本）
    /// - Parameter progressCallback: 进度回调，返回0-1之间的进度值
    /// - Returns: 章节数组
    nonisolated func fetchChapters(progressCallback: @escaping @Sendable (Double) -> Void) async throws -> [Chapter] {
        // Implementation will be added once dependencies are resolved
        return []
    }
    */
}
