import Foundation
@preconcurrency import SwiftData
import SwiftUI // 新增：用于 Image 类型
#if canImport(UIKit)
import UIKit
#elseif canImport(AppKit)
import AppKit
#endif

@Model
final class Book: Identifiable, Equatable {
    @Attribute(.unique) var id = UUID()
    var title: String
    var author: String
    var coverUrl: URL?
    var introduction: String?
    var latestChapter: String?
    var url: URL
    var sourceId: UUID // 关联的书源ID
    var bookType: String? // 书籍类型，如txt、epub等
    var filePath: String? // 本地文件路径，用于打开本地文件
    var tags: [String]? // 书籍标签
    var wordCount: String? // 字数，例如 "1608.04万字"
    var chapterCount: Int? // 章节数
    var isInShelf: Bool = false // 是否已在书架
    
    // 章节列表 (非持久化，按需加载)
    @Transient private(set) var chapters: [Chapter] = []
    
    // 新增存储属性
    var lastReadTimestamp: Double? // 存储 Date 的 timeIntervalSince1970
    var coverImageData: Data?    // 存储封面图片数据
    
    // 计算属性
    var lastReadDate: Date? {
        get {
            guard let timestamp = lastReadTimestamp else { return nil }
            return Date(timeIntervalSince1970: timestamp)
        }
        set {
            lastReadTimestamp = newValue?.timeIntervalSince1970
        }
    }
    
    var coverImage: Image? {
        get {
            guard let data = coverImageData else { return nil }
            // 根据平台条件编译创建 Image
#if canImport(UIKit)
            if let uiImage = UIImage(data: data) {
                return Image(uiImage: uiImage)
            }
#elseif canImport(AppKit)
            if let nsImage = NSImage(data: data) {
                return Image(nsImage: nsImage)
            }
#endif
            return nil // 如果无法创建图像，则返回nil
        }
        // coverImage 是基于 coverImageData 的计算属性，通常设为只读
        // 如果需要修改封面，应直接修改 coverImageData
        // 或者提供一个方法来设置图片数据并更新 coverImageData
    }
    
    static func == (lhs: Book, rhs: Book) -> Bool {
        lhs.id == rhs.id
    }
    
    // 更新 init 方法以包含新属性
    init(title: String, author: String, coverUrl: URL? = nil, introduction: String? = nil, latestChapter: String? = nil, url: URL, sourceId: UUID, bookType: String? = nil, filePath: String? = nil, lastReadTimestamp: Double? = nil, coverImageData: Data? = nil, tags: [String]? = nil, wordCount: String? = nil, chapterCount: Int? = nil, isInShelf: Bool = false) {
        self.id = UUID() // 确保 id 被初始化，如果它不是在声明时初始化的话
        self.title = title
        self.author = author
        self.coverUrl = coverUrl
        self.introduction = introduction
        self.latestChapter = latestChapter
        self.url = url
        self.sourceId = sourceId
        self.bookType = bookType
        self.filePath = filePath
        self.lastReadTimestamp = lastReadTimestamp
        self.coverImageData = coverImageData
        self.tags = tags
        self.wordCount = wordCount
        self.chapterCount = chapterCount
        self.isInShelf = isInShelf
    }
    
    /// 获取或解析书籍的章节列表
    /// - Parameter completion: 完成回调，返回章节数组或nil（如果失败）
    // 移除基于回调的实现，统一使用异步版本
    @available(*, deprecated, message: "Use async version instead")
    nonisolated func fetchChapters(completion: @escaping @MainActor ([Chapter]?) -> Void) {
        Task {
            do {
                let chapters = try await self.fetchChapters(progressCallback: { _ in })
                await MainActor.run {
                    completion(chapters)
                }
            } catch {
                print("获取章节失败: \(error.localizedDescription)")
                await MainActor.run {
                    completion(nil)
                }
            }
        }
    }
    
    /// 获取或解析书籍的章节列表（异步版本）
    /// - Parameter progressCallback: 进度回调，返回0-1之间的进度值
    /// - Returns: 章节数组
    nonisolated func fetchChapters(progressCallback: @escaping @Sendable (Double) -> Void) async throws -> [Chapter] {
        // 1. 尝试从缓存加载
        do {
            if try await ChapterStorage.shared.hasChapters(for: self.id) {
                let cachedChapters = try await ChapterStorage.shared.loadChapters(for: self.id)
                let sortedChapters = cachedChapters.sorted()
                print("从缓存加载了 \(sortedChapters.count) 个章节 for book \(self.title)")
                await MainActor.run {
                    progressCallback(1.0) // 从缓存加载完成，进度为100%
                }
                return sortedChapters
            }
        } catch {
            print("检查章节缓存失败: \(error.localizedDescription)")
        }
        
        // 2. 如果是本地 TXT 文件，则尝试解析
        let filePath = self.filePath
        let bookType = self.bookType
        let title = self.title
        
        guard let filePath = filePath,
              (bookType?.lowercased() == "txt" || filePath.lowercased().hasSuffix(".txt")),
              let fileURL = URL(string: filePath),
              FileManager.default.fileExists(atPath: fileURL.path) else {
            // TODO: 对于网络书籍或非TXT文件，需要实现不同的章节获取逻辑
            print("无法加载章节：非TXT文件或文件路径无效 for book \(title)")
            await MainActor.run {
                progressCallback(1.0) // 加载失败，进度为100%
            }
            return []
        }
        
        do {
            await MainActor.run {
                progressCallback(0.2) // 开始解析，进度为20%
            }
            
            // 注意：对于非常大的文件，直接 String(contentsOf:) 可能导致性能问题或内存溢出
            // 考虑使用更安全的方式读取大文件，例如逐行读取或分块读取
            let content = try String(contentsOf: fileURL, encoding: .utf8) // 尝试UTF-8，可根据需要添加其他编码检测
            
            await MainActor.run {
                progressCallback(0.5) // 文件读取完成，进度为50%
            }
            
            let parser = await ChapterParser(bookId: self.id)
            let parsedChapters = await parser.parseChapters(from: content, bookId: self.id)
            let sortedChapters = parsedChapters.sorted()
            
            await MainActor.run {
                progressCallback(0.8) // 章节解析完成，进度为80%
            }
            
            // 填充内容 - 这一步非常耗时耗内存，通常应该在用户请求特定章节时才执行
            // let chaptersWithContent = parser.fillChapterContents(chapters: sortedChapters, originalText: content)
            // self.chapters = chaptersWithContent
            // 注意：在nonisolated方法中不能直接修改self.chapters，由调用方处理
            
            // 3. 缓存解析结果
            do {
                try await ChapterStorage.shared.saveChapters(sortedChapters, for: self.id)
                print("解析并缓存了 \(sortedChapters.count) 个章节 for book \(self.title)")
            } catch {
                print("缓存章节失败: \(error.localizedDescription)")
            }
            
            await MainActor.run {
                progressCallback(1.0) // 完成，进度为100%
            }
            return sortedChapters
        } catch {
            print("解析TXT文件章节失败 for book \(title): \(error.localizedDescription)")
            await MainActor.run {
                progressCallback(1.0) // 加载失败，进度为100%
            }
            throw error
        }
    }
}
