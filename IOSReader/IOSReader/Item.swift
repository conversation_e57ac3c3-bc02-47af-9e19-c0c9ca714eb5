//
//  Item.swift
//  IOSReader
//
//  Created by gawen on 2025/3/27.
//
//  数据模型文件，用于存储阅读项目的基本信息
//  核心功能：
//  1. 记录项目时间戳
//  2. 存储标题和内容
//  3. 标记阅读状态
//  关键属性：
//  - timestamp: 记录创建/修改时间
//  - title: 项目标题
//  - content: 项目内容
//  - isRead: 阅读状态标记
//

import Foundation
import SwiftData

@Model
final class Item: @unchecked Sendable {
    var timestamp: Date
    var title: String
    var content: String
    var isRead: Bool
    
    init(timestamp: Date, title: String, content: String, isRead: Bool = false) {
        self.timestamp = timestamp
        self.title = title
        self.content = content
        self.isRead = isRead
    }
}
