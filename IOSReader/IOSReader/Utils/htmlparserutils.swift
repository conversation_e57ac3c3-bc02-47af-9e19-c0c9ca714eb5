import Foundation

/// HTML解析工具类
/// 提供简单的HTML解析功能，替代Kanna库
class HTMLParserUtils {
    
    // MARK: - HTML实体解码
    
    /// HTML实体映射表
    private static let htmlEntities: [String: String] = [
        // 基本HTML实体
        "&nbsp;": " ",
        "&lt;": "<",
        "&gt;": ">",
        "&amp;": "&",
        "&quot;": "\"",
        "&apos;": "'",
        // 常用特殊字符
        "&copy;": "©",
        "&reg;": "®",
        "&trade;": "™",
        "&euro;": "€",
        "&pound;": "£",
        "&yen;": "¥",
        "&cent;": "¢",
        // 常用空格和换行
        "&ensp;": " ",
        "&emsp;": " ",
        "&thinsp;": " ",
        "&zwnj;": "",
        "&zwj;": "",
        "&lrm;": "",
        "&rlm;": "",
        "&ndash;": "–",
        "&mdash;": "—",
        "&lsquo;": "'",
        "&rsquo;": "'",
        "&sbquo;": "‚",
        "&ldquo;": "\"",
        "&rdquo;": "\"",
        "&bdquo;": "„",
        "&dagger;": "†",
        "&Dagger;": "‡",
        "&bull;": "•",
        "&hellip;": "…"
    ]
    
    /// 解码HTML实体
    /// - Parameter html: 包含HTML实体的字符串
    /// - Returns: 解码后的字符串
    static func decodeHTMLEntities(_ html: String) -> String {
        var result = html
        
        // 替换已知的HTML实体
        for (entity, replacement) in htmlEntities {
            result = result.replacingOccurrences(of: entity, with: replacement)
        }
        
        // 处理数字实体 (十进制): &#123;
        let decimalPattern = "&#([0-9]+);"
        if let regex = try? NSRegularExpression(pattern: decimalPattern, options: []) {
            let nsString = result as NSString
            let matches = regex.matches(in: result, options: [], range: NSRange(location: 0, length: nsString.length))
            
            // 从后向前替换，避免替换过程中改变字符串长度影响后续匹配位置
            for match in matches.reversed() {
                if match.numberOfRanges > 1 {
                    let codeRange = match.range(at: 1)
                    let entityRange = match.range(at: 0)
                    
                    if let codeStr = nsString.substring(with: codeRange) as String?,
                       let codeInt = Int(codeStr),
                       let unicodeScalar = UnicodeScalar(codeInt) {
                        let char = String(unicodeScalar)
                        result = (result as NSString).replacingCharacters(in: entityRange, with: char)
                    }
                }
            }
        }
        
        // 处理数字实体 (十六进制): &#x7B;
        let hexPattern = "&#[xX]([0-9A-Fa-f]+);"
        if let regex = try? NSRegularExpression(pattern: hexPattern, options: []) {
            let nsString = result as NSString
            let matches = regex.matches(in: result, options: [], range: NSRange(location: 0, length: nsString.length))
            
            for match in matches.reversed() {
                if match.numberOfRanges > 1 {
                    let codeRange = match.range(at: 1)
                    let entityRange = match.range(at: 0)
                    
                    if let codeStr = nsString.substring(with: codeRange) as String?,
                       let codeInt = Int(codeStr, radix: 16),
                       let unicodeScalar = UnicodeScalar(codeInt) {
                        let char = String(unicodeScalar)
                        result = (result as NSString).replacingCharacters(in: entityRange, with: char)
                    }
                }
            }
        }
        
        return result
    }
    
    // MARK: - XPath解析
    
    /// XPath节点类型
    enum XPathNodeType {
        case element       // 元素节点
        case attribute    // 属性节点
        case text         // 文本节点
        case all          // 所有节点
    }
    
    /// XPath节点
    class XPathNode {
        var name: String
        var attributes: [String: String]
        var content: String
        var children: [XPathNode]
        var parent: XPathNode?
        
        init(name: String, attributes: [String: String] = [:], content: String = "", children: [XPathNode] = []) {
            self.name = name
            self.attributes = attributes
            self.content = content
            self.children = children
        }
        
        /// 获取节点文本内容（包括所有子节点的文本）
        var textContent: String {
            var result = content
            for child in children {
                result += child.textContent
            }
            return result
        }
        
        /// 获取HTML表示
        var outerHTML: String {
            var result = "<\(name)"
            
            // 添加属性
            for (key, value) in attributes {
                result += " \(key)=\"\(value)\""
            }
            
            if children.isEmpty && content.isEmpty {
                // 自闭合标签
                result += " />"
            } else {
                result += ">"
                
                // 添加内容
                result += content
                
                // 添加子节点
                for child in children {
                    result += child.outerHTML
                }
                
                // 闭合标签
                result += "</\(name)>"
            }
            
            return result
        }
    }
    
    /// 简单的HTML解析器，将HTML字符串解析为节点树
    /// - Parameter html: HTML字符串
    /// - Returns: 根节点
    static func parseHTML(_ html: String) -> XPathNode {
        // 创建根节点
        let root = XPathNode(name: "root")
        
        // 使用正则表达式匹配标签
        let tagPattern = "<([^>]+)>([^<]*)"
        let attributePattern = "([^\\s=]+)=\"([^\"]*)\""
        
        if let tagRegex = try? NSRegularExpression(pattern: tagPattern, options: [.dotMatchesLineSeparators]) {
            let nsString = html as NSString
            let matches = tagRegex.matches(in: html, options: [], range: NSRange(location: 0, length: nsString.length))
            
            var currentNode = root
            var stack: [XPathNode] = [root]
            
            for match in matches {
                if match.numberOfRanges > 2 {
                    let tagRange = match.range(at: 1)
                    let contentRange = match.range(at: 2)
                    
                    let tagStr = nsString.substring(with: tagRange)
                    let content = nsString.substring(with: contentRange)
                    
                    if tagStr.hasPrefix("/") {
                        // 结束标签
                        let tagName = String(tagStr.dropFirst())
                        
                        // 查找匹配的开始标签
                        if stack.count > 1 && stack.last?.name == tagName {
                            stack.removeLast()
                            currentNode = stack.last ?? root
                        }
                    } else if tagStr.hasSuffix("/") {
                        // 自闭合标签
                        let tagName = String(tagStr.dropLast())
                        
                        // 解析属性
                        var attributes: [String: String] = [:]
                        if let attrRegex = try? NSRegularExpression(pattern: attributePattern, options: []) {
                            let attrMatches = attrRegex.matches(in: tagName, options: [], range: NSRange(location: 0, length: (tagName as NSString).length))
                            
                            for attrMatch in attrMatches {
                                if attrMatch.numberOfRanges > 2 {
                                    let keyRange = attrMatch.range(at: 1)
                                    let valueRange = attrMatch.range(at: 2)
                                    
                                    let key = (tagName as NSString).substring(with: keyRange)
                                    let value = (tagName as NSString).substring(with: valueRange)
                                    
                                    attributes[key] = value
                                }
                            }
                        }
                        
                        // 创建节点
                        let node = XPathNode(name: tagName.components(separatedBy: " ").first ?? tagName, attributes: attributes, content: content)
                        node.parent = currentNode
                        currentNode.children.append(node)
                    } else {
                        // 开始标签
                        // 解析属性
                        var attributes: [String: String] = [:]
                        if let attrRegex = try? NSRegularExpression(pattern: attributePattern, options: []) {
                            let attrMatches = attrRegex.matches(in: tagStr, options: [], range: NSRange(location: 0, length: (tagStr as NSString).length))
                            
                            for attrMatch in attrMatches {
                                if attrMatch.numberOfRanges > 2 {
                                    let keyRange = attrMatch.range(at: 1)
                                    let valueRange = attrMatch.range(at: 2)
                                    
                                    let key = (tagStr as NSString).substring(with: keyRange)
                                    let value = (tagStr as NSString).substring(with: valueRange)
                                    
                                    attributes[key] = value
                                }
                            }
                        }
                        
                        // 创建节点
                        let tagName = tagStr.components(separatedBy: " ").first ?? tagStr
                        let node = XPathNode(name: tagName, attributes: attributes, content: content)
                        node.parent = currentNode
                        currentNode.children.append(node)
                        
                        // 入栈
                        stack.append(node)
                        currentNode = node
                    }
                }
            }
        }
        
        return root
    }
    
    /// 使用XPath查询HTML
    /// - Parameters:
    ///   - html: HTML字符串
    ///   - xpath: XPath表达式
    /// - Returns: 匹配的节点数组
    static func queryHTML(html: String, xpath: String) -> [XPathNode] {
        let root = parseHTML(html)
        return evaluateXPath(root: root, xpath: xpath)
    }
    
    /// 使用XPath提取HTML中的文本
    /// - Parameters:
    ///   - html: HTML字符串
    ///   - xpath: XPath表达式
    /// - Returns: 提取的文本数组
    static func extractTextByXPath(from html: String, xpath: String) -> [String] {
        let nodes = queryHTML(html: html, xpath: xpath)
        return nodes.map { node in 
            // 获取节点的文本内容并解码HTML实体
            return decodeHTMLEntities(node.textContent.trimmingCharacters(in: .whitespacesAndNewlines))
        }
    }
    
    /// 使用XPath提取HTML中的单个文本
    /// - Parameters:
    ///   - html: HTML字符串
    ///   - xpath: XPath表达式
    /// - Returns: 提取的第一个文本，如果没有匹配则返回nil
    static func extractFirstTextByXPath(from html: String, xpath: String) -> String? {
        return extractTextByXPath(from: html, xpath: xpath).first
    }
    
    /// 使用XPath提取HTML中的属性值
    /// - Parameters:
    ///   - html: HTML字符串
    ///   - xpath: XPath表达式
    ///   - attribute: 属性名称
    /// - Returns: 提取的属性值数组
    static func extractAttributeByXPath(from html: String, xpath: String, attribute: String) -> [String] {
        let nodes = queryHTML(html: html, xpath: xpath)
        return nodes.compactMap { $0.attributes[attribute] }
    }
    
    /// 使用XPath提取HTML中的单个属性值
    /// - Parameters:
    ///   - html: HTML字符串
    ///   - xpath: XPath表达式
    ///   - attribute: 属性名称
    /// - Returns: 提取的第一个属性值，如果没有匹配则返回nil
    static func extractFirstAttributeByXPath(from html: String, xpath: String, attribute: String) -> String? {
        return extractAttributeByXPath(from: html, xpath: xpath, attribute: attribute).first
    }
    
    /// 使用XPath提取HTML中的链接
    /// - Parameters:
    ///   - html: HTML字符串
    ///   - xpath: XPath表达式，应该指向a标签
    /// - Returns: 提取的链接数组，每个元素为(文本, URL)元组
    static func extractLinksByXPath(from html: String, xpath: String) -> [(text: String, url: String)] {
        let nodes = queryHTML(html: html, xpath: xpath)
        return nodes.compactMap { node -> (text: String, url: String)? in
            if node.name.lowercased() == "a", let href = node.attributes["href"] {
                return (text: decodeHTMLEntities(node.textContent.trimmingCharacters(in: .whitespacesAndNewlines)), url: href)
            }
            return nil
        }
    }
    
    /// 评估XPath表达式
    /// - Parameters:
    ///   - root: 根节点
    ///   - xpath: XPath表达式
    /// - Returns: 匹配的节点数组
    private static func evaluateXPath(root: XPathNode, xpath: String) -> [XPathNode] {
        // 分割XPath路径
        let components = xpath.components(separatedBy: "/")
            .filter { !$0.isEmpty }
        
        var currentNodes = [root]
        
        for component in components {
            var nextNodes: [XPathNode] = []
            
            // 处理特殊选择器
            if component == "*" {
                // 选择所有子节点
                for node in currentNodes {
                    nextNodes.append(contentsOf: node.children)
                }
            } else if component.hasPrefix("@") {
                // 属性选择器 - 在实际实现中需要更复杂的处理
                let attributeName = String(component.dropFirst())
                for node in currentNodes {
                    for child in node.children {
                        if child.attributes[attributeName] != nil {
                            nextNodes.append(child)
                        }
                    }
                }
            } else if component.contains("[") && component.contains("]") {
                // 带条件的选择器
                let parts = component.components(separatedBy: "[")
                if parts.count > 1 {
                    let tagName = parts[0]
                    let condition = parts[1].replacingOccurrences(of: "]", with: "")
                    
                    // 处理条件
                    if condition.hasPrefix("@") {
                        // 属性条件
                        let attrCondition = condition.dropFirst()
                        if attrCondition.contains("=") {
                            let attrParts = attrCondition.components(separatedBy: "=")
                            if attrParts.count > 1 {
                                let attrName = String(attrParts[0])
                                var attrValue = attrParts[1]
                                
                                // 移除引号
                                if attrValue.hasPrefix("'") && attrValue.hasSuffix("'") {
                                    attrValue = String(attrValue.dropFirst().dropLast())
                                } else if attrValue.hasPrefix("\"") && attrValue.hasSuffix("\"") {
                                    attrValue = String(attrValue.dropFirst().dropLast())
                                }
                                
                                for node in currentNodes {
                                    for child in node.children where child.name == tagName {
                                        if child.attributes[attrName] == attrValue {
                                            nextNodes.append(child)
                                        }
                                    }
                                }
                            }
                        } else {
                            // 仅检查属性存在
                            let attrName = String(attrCondition)
                            for node in currentNodes {
                                for child in node.children where child.name == tagName {
                                    if child.attributes[attrName] != nil {
                                        nextNodes.append(child)
                                    }
                                }
                            }
                        }
                    } else if let index = Int(condition) {
                        // 索引条件
                        for node in currentNodes {
                            let matchingChildren = node.children.filter { $0.name == tagName }
                            if index < matchingChildren.count {
                                nextNodes.append(matchingChildren[index])
                            }
                        }
                    }
                }
            } else {
                // 简单标签名选择器
                for node in currentNodes {
                    for child in node.children {
                        if child.name == component {
                            nextNodes.append(child)
                        }
                    }
                }
            }
            
            currentNodes = nextNodes
        }
        
        return currentNodes
    }
    
    /// 从HTML中提取纯文本
    /// - Parameter htmlString: HTML字符串
    /// - Returns: 提取的纯文本
    static func extractTextFromHTML(_ htmlString: String) -> String? {
        // 移除HTML标签
        var result = htmlString
        // 移除脚本和样式内容
        result = result.replacingOccurrences(of: "<script[\\s\\S]*?</script>", with: "", options: .regularExpression)
        result = result.replacingOccurrences(of: "<style[\\s\\S]*?</style>", with: "", options: .regularExpression)
        // 移除HTML标签
        result = result.replacingOccurrences(of: "<[^>]+>", with: "", options: .regularExpression)
        // 解码HTML实体
        result = decodeHTMLEntities(result)
        // 移除多余空白
        result = result.replacingOccurrences(of: "\\s+", with: " ", options: .regularExpression)
        return result.trimmingCharacters(in: .whitespacesAndNewlines)
    }
    
    /// 从HTML中提取指定标签的内容
    /// - Parameters:
    ///   - htmlString: HTML字符串
    ///   - tagName: 标签名称
    /// - Returns: 提取的标签内容数组
    static func extractTagContent(from htmlString: String, tagName: String) -> [String] {
        let pattern = "<\\s*\(tagName)\\s*[^>]*>(.*?)</\\s*\(tagName)\\s*>"
        let regex = try? NSRegularExpression(pattern: pattern, options: [.dotMatchesLineSeparators, .caseInsensitive])
        
        guard let regex = regex else { return [] }
        
        let nsString = htmlString as NSString
        let matches = regex.matches(in: htmlString, options: [], range: NSRange(location: 0, length: nsString.length))
        
        return matches.compactMap { match -> String? in
            if match.numberOfRanges > 1 {
                let range = match.range(at: 1)
                return nsString.substring(with: range)
            }
            return nil
        }
    }
    
    /// 从HTML中提取链接
    /// - Parameter htmlString: HTML字符串
    /// - Returns: 提取的链接数组，每个元素为(文本, URL)元组
    static func extractLinks(from htmlString: String) -> [(text: String, url: String)] {
        let pattern = "<a\\s+[^>]*href=\"([^\"]*)\"[^>]*>(.*?)</a>"
        let regex = try? NSRegularExpression(pattern: pattern, options: [.dotMatchesLineSeparators, .caseInsensitive])
        
        guard let regex = regex else { return [] }
        
        let nsString = htmlString as NSString
        let matches = regex.matches(in: htmlString, options: [], range: NSRange(location: 0, length: nsString.length))
        
        return matches.compactMap { match -> (text: String, url: String)? in
            if match.numberOfRanges > 2 {
                let urlRange = match.range(at: 1)
                let textRange = match.range(at: 2)
                
                let url = nsString.substring(with: urlRange)
                let text = extractTextFromHTML(nsString.substring(with: textRange)) ?? ""
                
                return (text: text, url: url)
            }
            return nil
        }
    }
    
    /// 从HTML中提取图片
    /// - Parameter htmlString: HTML字符串
    /// - Returns: 提取的图片URL数组
    static func extractImages(from htmlString: String) -> [String] {
        let pattern = "<img\\s+[^>]*src=\"([^\"]*)\"[^>]*>"
        let regex = try? NSRegularExpression(pattern: pattern, options: [.dotMatchesLineSeparators, .caseInsensitive])
        
        guard let regex = regex else { return [] }
        
        let nsString = htmlString as NSString
        let matches = regex.matches(in: htmlString, options: [], range: NSRange(location: 0, length: nsString.length))
        
        return matches.compactMap { match -> String? in
            if match.numberOfRanges > 1 {
                let range = match.range(at: 1)
                let url = nsString.substring(with: range)
                // 解码HTML实体
                return decodeHTMLEntities(url)
            }
            return nil
        }
    }
    
    /// 使用XPath提取HTML中的图片
    /// - Parameters:
    ///   - html: HTML字符串
    ///   - xpath: XPath表达式，应该指向img标签
    /// - Returns: 提取的图片URL数组
    static func extractImagesByXPath(from html: String, xpath: String) -> [String] {
        let nodes = queryHTML(html: html, xpath: xpath)
        return nodes.compactMap { node -> String? in
            if node.name.lowercased() == "img", let src = node.attributes["src"] {
                return decodeHTMLEntities(src)
            }
            return nil
        }
    }
    
    /// 使用XPath提取HTML中的第一张图片
    /// - Parameters:
    ///   - html: HTML字符串
    ///   - xpath: XPath表达式，应该指向img标签
    /// - Returns: 提取的第一个图片URL，如果没有匹配则返回nil
    static func extractFirstImageByXPath(from html: String, xpath: String) -> String? {
        return extractImagesByXPath(from: html, xpath: xpath).first
    }
    
    // MARK: - CSS选择器支持
    
    /// 将CSS选择器转换为XPath表达式
    /// - Parameter cssSelector: CSS选择器字符串
    /// - Returns: 等效的XPath表达式
    static func cssToXPath(_ cssSelector: String) -> String {
        var xpath = ""
        
        // 分割选择器
        let selectors = cssSelector.components(separatedBy: " ")
        
        for (index, selector) in selectors.enumerated() {
            if index > 0 {
                // 子元素关系
                xpath += "/"
            }
            
            // 处理ID选择器 (#id)
            if selector.hasPrefix("#") {
                let id = String(selector.dropFirst())
                xpath += "*[@id='\(id)']"
            }
            // 处理类选择器 (.class)
            else if selector.hasPrefix(".") {
                let className = String(selector.dropFirst())
                xpath += "*[contains(@class,'\(className)')]"
            }
            // 处理属性选择器 ([attr=value])
            else if selector.contains("[") && selector.contains("]") {
                let parts = selector.components(separatedBy: "[")
                let element = parts[0].isEmpty ? "*" : parts[0]
                
                var conditions = ""
                for i in 1..<parts.count {
                    var part = parts[i]
                    if part.hasSuffix("]") {
                        part = String(part.dropLast())
                    }
                    
                    if part.contains("=") {
                        let attrParts = part.components(separatedBy: "=")
                        if attrParts.count > 1 {
                            let attrName = attrParts[0]
                            var attrValue = attrParts[1]
                            
                            // 移除引号
                            if attrValue.hasPrefix("'") && attrValue.hasSuffix("'") {
                                attrValue = String(attrValue.dropFirst().dropLast())
                            } else if attrValue.hasPrefix("\"") && attrValue.hasSuffix("\"") {
                                attrValue = String(attrValue.dropFirst().dropLast())
                            }
                            
                            conditions += "[@\(attrName)='\(attrValue)']"
                        }
                    } else {
                        conditions += "[@\(part)]"
                    }
                }
                
                xpath += element + conditions
            }
            // 处理普通元素选择器
            else {
                xpath += selector
            }
        }
        
        // 如果没有指定路径，默认从根开始
        if !xpath.hasPrefix("/") {
            xpath = "//" + xpath
        }
        
        return xpath
    }
    
    /// 使用CSS选择器查询HTML
    /// - Parameters:
    ///   - html: HTML字符串
    ///   - cssSelector: CSS选择器
    /// - Returns: 匹配的节点数组
    static func queryHTMLWithCSS(html: String, cssSelector: String) -> [XPathNode] {
        let xpath = cssToXPath(cssSelector)
        return queryHTML(html: html, xpath: xpath)
    }
    
    /// 使用CSS选择器提取HTML中的文本
    /// - Parameters:
    ///   - html: HTML字符串
    ///   - cssSelector: CSS选择器
    /// - Returns: 提取的文本数组
    static func extractTextByCSS(from html: String, cssSelector: String) -> [String] {
        let xpath = cssToXPath(cssSelector)
        return extractTextByXPath(from: html, xpath: xpath)
    }
    
    /// 使用CSS选择器提取HTML中的单个文本
    /// - Parameters:
    ///   - html: HTML字符串
    ///   - cssSelector: CSS选择器
    /// - Returns: 提取的第一个文本，如果没有匹配则返回nil
    static func extractFirstTextByCSS(from html: String, cssSelector: String) -> String? {
        return extractTextByCSS(from: html, cssSelector: cssSelector).first
    }
    
    /// 使用CSS选择器提取HTML中的属性值
    /// - Parameters:
    ///   - html: HTML字符串
    ///   - cssSelector: CSS选择器
    ///   - attribute: 属性名称
    /// - Returns: 提取的属性值数组
    static func extractAttributeByCSS(from html: String, cssSelector: String, attribute: String) -> [String] {
        let xpath = cssToXPath(cssSelector)
        return extractAttributeByXPath(from: html, xpath: xpath, attribute: attribute)
    }
    
    /// 使用CSS选择器提取HTML中的单个属性值
    /// - Parameters:
    ///   - html: HTML字符串
    ///   - cssSelector: CSS选择器
    ///   - attribute: 属性名称
    /// - Returns: 提取的第一个属性值，如果没有匹配则返回nil
    static func extractFirstAttributeByCSS(from html: String, cssSelector: String, attribute: String) -> String? {
        return extractAttributeByCSS(from: html, cssSelector: cssSelector, attribute: attribute).first
    }
    
    /// 使用CSS选择器提取HTML中的链接
    /// - Parameters:
    ///   - html: HTML字符串
    ///   - cssSelector: CSS选择器，应该指向a标签
    /// - Returns: 提取的链接数组，每个元素为(文本, URL)元组
    static func extractLinksByCSS(from html: String, cssSelector: String) -> [(text: String, url: String)] {
        let xpath = cssToXPath(cssSelector)
        return extractLinksByXPath(from: html, xpath: xpath)
    }
    
    /// 使用CSS选择器提取HTML中的图片
    /// - Parameters:
    ///   - html: HTML字符串
    ///   - cssSelector: CSS选择器，应该指向img标签
    /// - Returns: 提取的图片URL数组
    static func extractImagesByCSS(from html: String, cssSelector: String) -> [String] {
        let xpath = cssToXPath(cssSelector)
        return extractImagesByXPath(from: html, xpath: xpath)
    }
    
    /// 使用CSS选择器提取HTML中的第一张图片
    /// - Parameters:
    ///   - html: HTML字符串
    ///   - cssSelector: CSS选择器，应该指向img标签
    /// - Returns: 提取的第一个图片URL，如果没有匹配则返回nil
    static func extractFirstImageByCSS(from html: String, cssSelector: String) -> String? {
        return extractImagesByCSS(from: html, cssSelector: cssSelector).first
    }
}