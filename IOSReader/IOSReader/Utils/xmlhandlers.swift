import Foundation
import SwiftUI

// MARK: - EPUB解析错误及解析器

/// EPUB解析错误
enum EPUBError: Error {
    case invalidData
    case parsingFailed
    case missingRootfile
    case archiveError
    case missingContainerFile
    case invalidContainerFile
    case missingOPFFile
    case invalidOPFFile

    var localizedDescription: String {
        switch self {
        case .invalidData:
            return "无效的XML数据"
        case .parsingFailed:
            return "解析文件失败"
        case .missingRootfile:
            return "未找到rootfile路径"
        case .archiveError:
            return "无法解压EPUB文件"
        case .missingContainerFile:
            return "无法找到container.xml文件"
        case .invalidContainerFile:
            return "无法解析container.xml文件"
        case .missingOPFFile:
            return "无法找到OPF文件"
        case .invalidOPFFile:
            return "无法解析OPF文件"
        }
    }
}

/// EPUB容器文件解析器
struct EPUBContainerParser {
    static func parse(data: Data) throws -> String? {
        let parser = XMLParser(data: data)
        let handler = RootFileHandler() // RootFileHandler 在此文件稍后定义
        parser.delegate = handler

        guard parser.parse() else {
            throw EPUBError.invalidData
        }

        guard let fullPath = handler.rootFilePath else {
            throw EPUBError.missingRootfile
        }

        return fullPath.trimmingCharacters(in: .whitespacesAndNewlines)
    }
}

/// EPUB OPF文件解析器
struct EPUBOPFParser {
    /// 解析OPF文件，提取项目和脊柱信息
    /// - Parameter data: OPF文件数据
    /// - Returns: 包含项目和脊柱信息的元组
    /// - Throws: 解析错误
    static func parse(data: Data) throws -> (items: [EPUBItem], spine: [String]) {
        let parser = XMLParser(data: data)
        // _ = String(data: data, encoding: .utf8) ?? "" // DEBUG: 查看原始数据
        let handler = OPFHandler() // OPFHandler 在此文件稍后定义
        parser.delegate = handler
        guard parser.parse() else {
            throw EPUBError.invalidData
        }
        let items = handler.items
        let spine = handler.spine
        return (items, spine)
    }

    /// 根据ID查找项目
    /// - Parameters:
    ///   - itemId: 项目ID
    ///   - items: 项目列表
    /// - Returns: 匹配的项目，如果未找到则返回nil
    static func getItemById(itemId: String, items: [EPUBItem]) -> EPUBItem? {
        return items.first { $0.id == itemId }
    }
}

/// EPUB文件中的项目结构
struct EPUBItem: Equatable, Hashable {
    var id: String
    var href: String?
    var mediaType: String?
    
    init(id: String, href: String? = nil, mediaType: String? = nil) {
        self.id = id
        self.href = href
        self.mediaType = mediaType
    }
}

/// 用于解析container.xml文件中rootfile路径的处理器
class RootFileHandler: NSObject, XMLParserDelegate {
    private(set) var rootFilePath: String?
    private var foundRootfile = false
    private var currentElement = ""
    
    func parser(_ parser: XMLParser, didStartElement elementName: String, namespaceURI: String?, qualifiedName qName: String?, attributes attributeDict: [String : String] = [:]) {
        currentElement = elementName
        if elementName == "rootfile", let path = attributeDict["full-path"] {
            rootFilePath = path
            foundRootfile = true
            parser.abortParsing() // 找到后立即停止解析
        }
    }
    
    func parserDidEndDocument(_ parser: XMLParser) {
        // 验证解析结果
        if !foundRootfile {
            parser.abortParsing()
        }
    }
}

/// 用于解析OPF文件的处理器
class OPFHandler: NSObject, XMLParserDelegate {
    private(set) var items: [EPUBItem] = []
    private(set) var spine: [String] = []
    private var currentElement = ""
    private var tempItems: [EPUBItem] = []
    private var tempSpine: [String] = []
    
    func parser(_ parser: XMLParser, didStartElement elementName: String, namespaceURI: String?, qualifiedName qName: String?, attributes attributeDict: [String : String] = [:]) {
        if elementName == "item" {
            let id = attributeDict["id"] ?? ""
            let href = attributeDict["href"]
            let mediaType = attributeDict["media-type"]
            tempItems.append(EPUBItem(id: id, href: href, mediaType: mediaType))
        } else if elementName == "itemref" {
            if let idref = attributeDict["idref"] {
                tempSpine.append(idref)
            }
        }
    }
    func parserDidEndDocument(_ parser: XMLParser) {
        self.items = tempItems
        self.spine = tempSpine
    }
}