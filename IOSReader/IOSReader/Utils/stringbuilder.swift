import Foundation

/// 字符串构建器
/// 用于高效地构建大型字符串，避免频繁的字符串连接操作
class StringBuilder {
    private var stringParts: [String] = []
    
    /// 初始化一个空的字符串构建器
    init() {}
    
    /// 初始化并添加初始内容
    /// - Parameter initialContent: 初始内容
    init(_ initialContent: String) {
        stringParts.append(initialContent)
    }
    
    /// 添加字符串到构建器
    /// - Parameter string: 要添加的字符串
    func append(_ string: String) {
        stringParts.append(string)
    }
    
    /// 将构建器中的所有部分合并为一个字符串
    /// - Returns: 合并后的字符串
    func toString() -> String {
        return stringParts.joined()
    }
    
    /// 清空构建器
    func clear() {
        stringParts.removeAll()
    }
    
    /// 获取当前构建器中的字符串长度
    var length: Int {
        return stringParts.reduce(0) { $0 + $1.count }
    }
}