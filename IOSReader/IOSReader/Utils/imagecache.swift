import SwiftUI

/// 用于包装 Image 的结构体，以便在缓存中存储
private struct ImageCacheWrapper {
    let image: Image
    
    init(_ image: Image) {
        self.image = image
    }
}

/// 线程安全的图片缓存类
actor ImageCache: Sendable {
    private var cache: [String: ImageCacheWrapper] = [:]
    private var keyOrder: [String] = []
    private let maxCacheSize = 100
    
    /// 获取缓存中的图片
    /// - Parameter key: 缓存键
    /// - Returns: 缓存的图片，如果不存在则返回nil
    func image(forKey key: String) -> Image? {
        return cache[key]?.image
    }
    
    /// 将图片存入缓存
    /// - Parameters:
    ///   - image: 要缓存的图片
    ///   - key: 缓存键
    func setImage(_ image: Image, forKey key: String) {
        // 如果缓存已满，移除最早添加的图片
        if cache.count >= maxCacheSize && !cache.keys.contains(key) {
            if let firstKey = keyOrder.first {
                cache.removeValue(forKey: firstKey)
                keyOrder.removeFirst()
            }
        }
        
        cache[key] = ImageCacheWrapper(image)
        
        // 更新键顺序
        if let index = keyOrder.firstIndex(of: key) {
            keyOrder.remove(at: index)
        }
        keyOrder.append(key)
    }
    
    /// 移除指定键的图片
    /// - Parameter key: 要移除的图片的键
    func removeImage(forKey key: String) {
        cache.removeValue(forKey: key)
        if let index = keyOrder.firstIndex(of: key) {
            keyOrder.remove(at: index)
        }
    }
    
    /// 移除所有缓存的图片
    func removeAllImages() {
        cache.removeAll()
        keyOrder.removeAll()
    }
    
    /// 获取缓存中的键顺序
    var order: [String] {
        return keyOrder
    }
}