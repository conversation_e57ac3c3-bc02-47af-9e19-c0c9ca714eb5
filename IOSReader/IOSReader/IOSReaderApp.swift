//
//  IOSReaderApp.swift
//  IOSReader
//
//  Created by gawen on 2025/3/27.
//
//  应用主入口文件，负责：
//  1. 应用生命周期管理
//  2. 数据模型容器初始化
//  3. 主窗口设置
//
//  关键配置项：
//  - 使用@main标记应用入口
//  - 初始化SwiftData模型容器
//  - 设置主窗口ContentView
//

import SwiftUI
import SwiftData

// 将图片管理相关功能移到单独的类中
actor ImageManager: Sendable {
    static let shared = ImageManager()
    private init() {}
    
    func loadCustomLaunchImage() async -> UIImage? {
        guard let documentsDirectory = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first else {
            return nil
        }
        let fileURL = documentsDirectory.appendingPathComponent("customLaunchImage.png")
        return UIImage(contentsOfFile: fileURL.path)
    }
    
    func saveCustomLaunchImage(_ image: UIImage) async {
        guard let documentsDirectory = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first else {
            return
        }
        let fileURL = documentsDirectory.appendingPathComponent("customLaunchImage.png")
        if let data = image.pngData() {
            try? data.write(to: fileURL)
        }
    }
}

@main
struct IOSReaderApp: App {
    // Initialize coordinator AFTER modelContainer is ready
    @StateObject private var coordinator: ReaderCoordinator
    private let modelContainer: ModelContainer

    init() {
        do {
            // Define the schema with all necessary model types
            let schema = Schema([
                User.self,
                Book.self,
                BookSource.self,
                Bookmark.self,
                ReadingProgress.self,
                Item.self
                // Add other SwiftData models here if needed
            ])
            // Configure the model container
            let modelConfiguration = ModelConfiguration(schema: schema, isStoredInMemoryOnly: true) // 修改为 true 以进行内存存储测试
            let container = try ModelContainer(for: schema, configurations: [modelConfiguration])
            modelContainer = container

            // Initialize coordinator AFTER modelContainer is ready and pass it explicitly
            // Provide default values or fetch initial state as needed for other parameters
            _coordinator = StateObject(wrappedValue: ReaderCoordinator(modelContainer: container)) // 使用正确的初始化方法

        } catch {
            fatalError("Could not create ModelContainer: \(error)")
        }
    }

    var body: some Scene {
        WindowGroup {
            // Pass coordinator directly, no need for .environmentObject here if passed down
            ContentView()
                .environmentObject(coordinator) // Pass coordinator via environment for nested views
        }
        .modelContainer(modelContainer) // Provide the container to the environment
    }
}
